<?xml version="1.0"?>
<package format="3">
  <name>hospital_gazebo</name>
  <version>1.0.0</version>
  <description>Hospital simulation environment for ROS1/ROS2 with realistic medical facility layout</description>

  <maintainer email="<EMAIL>">Hospital Simulation Team</maintainer>
  <license>MIT</license>

  <!-- ROS1 Dependencies -->
  <buildtool_depend condition="$ROS_VERSION == 1">catkin</buildtool_depend>
  <build_depend condition="$ROS_VERSION == 1">rospy</build_depend>
  <exec_depend condition="$ROS_VERSION == 1">rospy</exec_depend>
  <exec_depend condition="$ROS_VERSION == 1">gazebo_ros_pkgs</exec_depend>

  <!-- ROS2 Dependencies -->
  <buildtool_depend condition="$ROS_VERSION == 2">ament_cmake</buildtool_depend>
  <buildtool_depend condition="$ROS_VERSION == 2">ament_cmake_python</buildtool_depend>
  <build_depend condition="$ROS_VERSION == 2">rclpy</build_depend>
  <exec_depend condition="$ROS_VERSION == 2">rclpy</exec_depend>
  <exec_depend condition="$ROS_VERSION == 2">gazebo_ros_pkgs</exec_depend>
  <exec_depend condition="$ROS_VERSION == 2">launch</exec_depend>
  <exec_depend condition="$ROS_VERSION == 2">launch_ros</exec_depend>

  <!-- Common Dependencies -->
  <build_depend>std_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>gazebo_ros</build_depend>

  <build_export_depend>std_msgs</build_export_depend>
  <build_export_depend>geometry_msgs</build_export_depend>
  <build_export_depend>gazebo_ros</build_export_depend>

  <exec_depend>std_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>

  <export>
    <build_type condition="$ROS_VERSION == 1">catkin</build_type>
    <build_type condition="$ROS_VERSION == 2">ament_cmake</build_type>
  </export>
</package>

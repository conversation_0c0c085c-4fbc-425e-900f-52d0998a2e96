<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="hospital_simulation">
    
    <!-- Physics settings optimized for hospital environment -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1.0</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <!-- Scene settings for hospital environment -->
    <scene>
      <ambient>0.6 0.6 0.65 1</ambient>
      <background>0.85 0.87 0.9 1</background>
      <shadows>true</shadows>
      <grid>false</grid>
    </scene>

    <!-- Hospital lighting system (no sun model to avoid bright circles) -->

    <!-- Subtle hospital ambient lighting -->
    <light name="hospital_ambient_1" type="directional">
      <pose>0 0 10 0 0 0</pose>
      <diffuse>0.6 0.6 0.65 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <direction>0.1 0.1 -1</direction>
      <cast_shadows>true</cast_shadows>
    </light>

    <light name="hospital_ambient_2" type="directional">
      <pose>10 10 8 0 0 0</pose>
      <diffuse>0.4 0.4 0.45 1</diffuse>
      <specular>0.05 0.05 0.05 1</specular>
      <direction>-0.2 -0.2 -1</direction>
      <cast_shadows>false</cast_shadows>
    </light>

    <light name="hospital_ambient_3" type="directional">
      <pose>-10 -10 6 0 0 0</pose>
      <diffuse>0.3 0.3 0.35 1</diffuse>
      <specular>0.02 0.02 0.02 1</specular>
      <direction>0.3 0.3 -1</direction>
      <cast_shadows>false</cast_shadows>
    </light>

    <!-- Ground plane -->
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <!-- HOSPITAL BUILDING STRUCTURE -->
    
    <!-- Exterior Walls -->
    <model name="north_wall">
      <pose>0 25 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="south_wall">
      <pose>0 -25 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="east_wall">
      <pose>25 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="west_wall">
      <pose>-25 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- MAIN CORRIDOR WALLS -->
    <model name="main_corridor_wall_north">
      <pose>0 8 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="main_corridor_wall_south">
      <pose>0 -8 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="main_corridor_wall_east">
      <pose>8 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="main_corridor_wall_west">
      <pose>-8 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- EMERGENCY ROOM (North-West: -16, 16) -->
    <include>
      <uri>model://table</uri>
      <pose>-18 18 0 0 0 0</pose>
      <name>er_examination_table_1</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-14 18 0 0 0 1.57</pose>
      <name>er_examination_table_2</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-22 19 0 0 0 0</pose>
      <name>er_medical_cabinet_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-15 16 0 0 0 0</pose>
      <name>er_doctor</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-19 15 0 0 0 1.57</pose>
      <name>er_nurse</name>
    </include>

    <!-- OPERATING ROOM 1 (North-East: 16, 16) -->
    <include>
      <uri>model://table_marble</uri>
      <pose>16 16 0 0 0 0</pose>
      <name>or1_operating_table</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>22 18 0 0 0 0</pose>
      <name>or1_instrument_cabinet_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>14 18 0 0 0 -1.57</pose>
      <name>or1_surgeon</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>18 18 0 0 0 -1.57</pose>
      <name>or1_nurse</name>
    </include>

    <!-- PATIENT ROOM 1 (West: -16, 4) -->
    <include>
      <uri>model://table_marble</uri>
      <pose>-18 4 0 0 0 0</pose>
      <name>pr1_hospital_bed</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-14 5 0 0 0 0</pose>
      <name>pr1_visitor_chair</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-16 5 0 0 0 0</pose>
      <name>pr1_patient</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-14 3 0 0 0 1.57</pose>
      <name>pr1_visitor</name>
    </include>

    <!-- WAITING AREA (East: 16, 4) -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>12 4 0 0 0 1.57</pose>
      <name>wa_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>14 4 0 0 0 1.57</pose>
      <name>wa_chair_2</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>16 4 0 0 0 1.57</pose>
      <name>wa_chair_3</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>13 3 0 0 0 0</pose>
      <name>wa_patient_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>19 3 0 0 0 0</pose>
      <name>wa_patient_2</name>
    </include>

    <!-- RECEPTION (East: 16, -4) -->
    <include>
      <uri>model://table</uri>
      <pose>16 -4 0 0 0 0</pose>
      <name>rec_reception_desk</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>16 -3 0 0 0 0</pose>
      <name>rec_receptionist_chair</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>15 -4 0 0 0 1.57</pose>
      <name>rec_receptionist</name>
    </include>

    <!-- PHARMACY (South-West: -16, -16) -->
    <include>
      <uri>model://bookshelf</uri>
      <pose>-22 -14 0 0 0 0</pose>
      <name>pharm_medicine_shelf_1</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-16 -16 0 0 0 0</pose>
      <name>pharm_counter</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-15 -16 0 0 0 1.57</pose>
      <name>pharm_pharmacist</name>
    </include>

    <!-- CORRIDOR FURNITURE -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>-4 0 0 0 0 1.57</pose>
      <name>corridor_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>4 0 0 0 0 -1.57</pose>
      <name>corridor_chair_2</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-3 2 0 0 0 0</pose>
      <name>corridor_visitor_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>3 2 0 0 0 3.14</pose>
      <name>corridor_visitor_2</name>
    </include>

    <!-- MEDICAL SUPPLIES -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>-6 6 0 0 0 0</pose>
      <name>corridor_supply_box_1</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>6 6 0 0 0 0</pose>
      <name>corridor_supply_box_2</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>-18 18.3 0.8 0 0 0</pose>
      <name>er_medical_bowl</name>
    </include>

    <include>
      <uri>model://coke_can</uri>
      <pose>-15.7 -15.7 0.8 0 0 0</pose>
      <name>pharm_medicine_bottle</name>
    </include>

  </world>
</sdf>

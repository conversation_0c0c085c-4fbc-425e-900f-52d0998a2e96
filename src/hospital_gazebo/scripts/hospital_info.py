#!/usr/bin/env python3

"""
Hospital Simulation Information Display
ROS1/ROS2 Node for displaying hospital environment information
"""

import sys
import os

# Try ROS2 first, then fall back to ROS1
try:
    import rclpy
    from rclpy.node import Node
    ROS_VERSION = 2
except ImportError:
    try:
        import rospy
        ROS_VERSION = 1
    except ImportError:
        print("Neither ROS1 nor ROS2 found. Running standalone.")
        ROS_VERSION = 0

class HospitalInfo:
    def __init__(self):
        if ROS_VERSION == 2:
            rclpy.init()
            self.node = rclpy.create_node('hospital_info')
            self.node.get_logger().info('Hospital Info Node started (ROS2)')
        elif ROS_VERSION == 1:
            rospy.init_node('hospital_info', anonymous=True)
            rospy.loginfo('Hospital Info Node started (ROS1)')
        
        self.display_hospital_info()
    
    def display_hospital_info(self):
        """Display comprehensive hospital simulation information"""
        
        print("\n" + "="*80)
        print("🏥 HOSPITAL SIMULATION ENVIRONMENT - FIXED LIGHTING")
        print("="*80)
        
        print("\n🏗️ HOSPITAL LAYOUT:")
        print("   📍 Coordinates: 50m x 50m facility")
        print("   🚪 Main Corridor: Central cross-shaped hallway")
        print("   🏠 Total Rooms: 8 specialized medical areas")
        
        print("\n🏥 MEDICAL DEPARTMENTS:")
        print("   🚨 Emergency Room (North-West: -16, 16)")
        print("   🔬 Operating Room 1 (North-East: 16, 16)")
        print("   🔬 Operating Room 2 (South-East: 16, -16)")
        print("   🛏️ Patient Room 1 (West: -16, 4)")
        print("   🛏️ Patient Room 2 (West: -16, -4)")
        print("   🪑 Waiting Area (East: 16, 4)")
        print("   📋 Reception/Nurse Station (East: 16, -4)")
        print("   💊 Pharmacy (South-West: -16, -16)")
        
        print("\n💡 LIGHTING IMPROVEMENTS:")
        print("   ✅ Removed harsh point lights")
        print("   ✅ Added subtle directional lighting")
        print("   ✅ Balanced ambient lighting")
        print("   ✅ Proper hospital atmosphere")
        
        print("\n✅ HOSPITAL SIMULATION READY!")
        print("   🏥 All medical departments operational")
        print("   👥 Staff and patients positioned")
        print("   🪑 Furniture and equipment placed")
        print("   🤖 Ready for robot navigation integration")
        
        print("\n" + "="*80)
        print("🏥 Hospital Environment Successfully Loaded!")
        print("="*80 + "\n")

def main():
    try:
        hospital_info = HospitalInfo()
        
        if ROS_VERSION == 2:
            rclpy.spin(hospital_info.node)
            rclpy.shutdown()
        elif ROS_VERSION == 1:
            rospy.spin()
        else:
            print("Running in standalone mode - hospital info displayed")
            
    except KeyboardInterrupt:
        if ROS_VERSION == 2:
            rclpy.shutdown()
        print("\nHospital info node stopped.")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3

"""
Hospital Simulation Launch File for ROS2 Humble
Launches the complete hospital environment using ROS2 and Gazebo
"""

import os
from launch import LaunchDescription
from launch.actions import ExecuteProcess, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    
    # Get the launch directory
    pkg_dir = get_package_share_directory('hospital_gazebo')
    
    # Path to the world file
    world_file = os.path.join(pkg_dir, '..', '..', '..', 'src', 'hospital_gazebo', 'worlds', 'hospital_simulation.world')
    
    # Set Gazebo model path
    gazebo_models_path = os.path.join(pkg_dir, '..', '..', '..', 'gazebo_models-master')
    
    # Launch configuration variables
    use_sim_time = LaunchConfiguration('use_sim_time', default='true')
    world = LaunchConfiguration('world', default=world_file)
    
    # Set environment variables
    env = {'GAZEBO_MODEL_PATH': gazebo_models_path}
    
    # Gazebo launch
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            get_package_share_directory('gazebo_ros'), 
            '/launch/gazebo.launch.py'
        ]),
        launch_arguments={
            'world': world,
            'verbose': 'true'
        }.items()
    )
    
    # Hospital info node
    hospital_info_node = Node(
        package='hospital_gazebo',
        executable='hospital_info.py',
        name='hospital_info',
        output='screen'
    )
    
    return LaunchDescription([
        gazebo,
        hospital_info_node
    ])

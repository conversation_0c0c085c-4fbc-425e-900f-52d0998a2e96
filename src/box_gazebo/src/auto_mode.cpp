#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "std_msgs/msg/bool.hpp"
#include <cmath>
#include <random>

class AutoMode : public rclcpp::Node {
public:
  AutoMode() : Node("auto_mode") {
    // Publishers
    cmd_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/small_car/cmd_vel", 10);
    
    // Subscribers
    auto_mode_sub_ = this->create_subscription<std_msgs::msg::Bool>(
      "/small_car/auto_mode", 10,
      std::bind(&AutoMode::autoModeCallback, this, std::placeholders::_1));
    
    // Parameters
    this->declare_parameter("patrol_speed", 1.0);
    this->declare_parameter("turn_speed", 1.0);
    
    patrol_speed_ = this->get_parameter("patrol_speed").as_double();
    turn_speed_ = this->get_parameter("turn_speed").as_double();
    
    // Initialize
    auto_mode_enabled_ = false;
    current_state_ = State::MOVING_FORWARD;
    state_timer_ = 0;
    
    // Random number generator
    rng_.seed(std::chrono::steady_clock::now().time_since_epoch().count());
    
    // Timer for control loop
    control_timer_ = this->create_wall_timer(
      std::chrono::milliseconds(100),  // 10 Hz
      std::bind(&AutoMode::controlLoop, this));
    
    RCLCPP_INFO(this->get_logger(), "🤖 Auto Mode Controller started!");
    RCLCPP_INFO(this->get_logger(), "📡 Publish to /small_car/auto_mode (true/false) to enable/disable");
  }

private:
  enum class State {
    MOVING_FORWARD,
    TURNING_LEFT,
    TURNING_RIGHT,
    PAUSING
  };

  void autoModeCallback(const std_msgs::msg::Bool::SharedPtr msg) {
    auto_mode_enabled_ = msg->data;
    if (auto_mode_enabled_) {
      RCLCPP_INFO(this->get_logger(), "🤖 Auto mode ENABLED - Car will move automatically");
      current_state_ = State::MOVING_FORWARD;
      state_timer_ = 0;
    } else {
      RCLCPP_INFO(this->get_logger(), "🎮 Auto mode DISABLED - Manual control active");
      // Stop the car when auto mode is disabled
      auto cmd = geometry_msgs::msg::Twist();
      cmd.linear.x = 0.0;
      cmd.angular.z = 0.0;
      cmd_vel_pub_->publish(cmd);
    }
  }
  
  void controlLoop() {
    if (!auto_mode_enabled_) {
      return;  // Do nothing if auto mode is disabled
    }
    
    auto cmd = geometry_msgs::msg::Twist();
    state_timer_++;
    
    switch (current_state_) {
      case State::MOVING_FORWARD:
        cmd.linear.x = patrol_speed_;
        cmd.angular.z = 0.0;
        
        // Move forward for 3-6 seconds, then change direction
        if (state_timer_ > getRandomDuration(30, 60)) {  // 3-6 seconds at 10Hz
          changeState();
        }
        break;
        
      case State::TURNING_LEFT:
        cmd.linear.x = 0.0;
        cmd.angular.z = turn_speed_;
        
        // Turn for 1-2 seconds
        if (state_timer_ > getRandomDuration(10, 20)) {  // 1-2 seconds at 10Hz
          current_state_ = State::MOVING_FORWARD;
          state_timer_ = 0;
          RCLCPP_INFO(this->get_logger(), "🔼 Moving forward");
        }
        break;
        
      case State::TURNING_RIGHT:
        cmd.linear.x = 0.0;
        cmd.angular.z = -turn_speed_;
        
        // Turn for 1-2 seconds
        if (state_timer_ > getRandomDuration(10, 20)) {  // 1-2 seconds at 10Hz
          current_state_ = State::MOVING_FORWARD;
          state_timer_ = 0;
          RCLCPP_INFO(this->get_logger(), "🔼 Moving forward");
        }
        break;
        
      case State::PAUSING:
        cmd.linear.x = 0.0;
        cmd.angular.z = 0.0;
        
        // Pause for 1-3 seconds
        if (state_timer_ > getRandomDuration(10, 30)) {  // 1-3 seconds at 10Hz
          current_state_ = State::MOVING_FORWARD;
          state_timer_ = 0;
          RCLCPP_INFO(this->get_logger(), "🔼 Moving forward");
        }
        break;
    }
    
    cmd_vel_pub_->publish(cmd);
    
    // Log status occasionally
    static int log_counter = 0;
    if (++log_counter % 50 == 0) {  // Every 5 seconds
      RCLCPP_INFO(this->get_logger(), 
        "🤖 Auto mode: %s, Speed: %.1f m/s, %.1f rad/s", 
        getStateName().c_str(), cmd.linear.x, cmd.angular.z);
    }
  }
  
  void changeState() {
    // Randomly choose next state
    std::uniform_int_distribution<int> dist(0, 3);
    int next_state = dist(rng_);
    
    switch (next_state) {
      case 0:
        current_state_ = State::TURNING_LEFT;
        RCLCPP_INFO(this->get_logger(), "◀️ Turning left");
        break;
      case 1:
        current_state_ = State::TURNING_RIGHT;
        RCLCPP_INFO(this->get_logger(), "▶️ Turning right");
        break;
      case 2:
        current_state_ = State::PAUSING;
        RCLCPP_INFO(this->get_logger(), "⏸️ Pausing");
        break;
      case 3:
        current_state_ = State::MOVING_FORWARD;
        RCLCPP_INFO(this->get_logger(), "🔼 Continuing forward");
        break;
    }
    
    state_timer_ = 0;
  }
  
  int getRandomDuration(int min_ticks, int max_ticks) {
    std::uniform_int_distribution<int> dist(min_ticks, max_ticks);
    return dist(rng_);
  }
  
  std::string getStateName() {
    switch (current_state_) {
      case State::MOVING_FORWARD: return "Moving Forward";
      case State::TURNING_LEFT: return "Turning Left";
      case State::TURNING_RIGHT: return "Turning Right";
      case State::PAUSING: return "Pausing";
      default: return "Unknown";
    }
  }
  
  // Member variables
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub_;
  rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr auto_mode_sub_;
  rclcpp::TimerBase::SharedPtr control_timer_;
  
  bool auto_mode_enabled_;
  State current_state_;
  int state_timer_;
  
  double patrol_speed_;
  double turn_speed_;
  
  std::mt19937 rng_;
};

int main(int argc, char** argv) {
  rclcpp::init(argc, argv);
  
  auto node = std::make_shared<AutoMode>();
  
  try {
    rclcpp::spin(node);
  } catch (const std::exception& e) {
    RCLCPP_ERROR(node->get_logger(), "Exception: %s", e.what());
  }
  
  rclcpp::shutdown();
  return 0;
}

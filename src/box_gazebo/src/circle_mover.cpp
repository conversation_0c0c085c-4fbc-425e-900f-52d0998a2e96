#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"

class CircleMover : public rclcpp::Node {
public:
  CircleMover() : Node("circle_mover") {
    publisher_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/circle_bot/cmd_vel", 10);
    timer_ = this->create_wall_timer(
      std::chrono::milliseconds(100),
      std::bind(&CircleMover::move_circle, this));
  }

private:
  void move_circle() {
    auto message = geometry_msgs::msg::Twist();
    message.linear.x = 0.5;  // Move forward at 0.5 m/s
    message.angular.z = 0.5; // Rotate at 0.5 rad/s
    publisher_->publish(message);
  }
  
  rclcpp::TimerBase::SharedPtr timer_;
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr publisher_;
};

int main(int argc, char * argv[]) {
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<CircleMover>());
  rclcpp::shutdown();
  return 0;
}

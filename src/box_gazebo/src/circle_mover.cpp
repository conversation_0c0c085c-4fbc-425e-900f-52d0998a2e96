#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"

class CircleMover : public rclcpp::Node {
public:
  CircleMover() : Node("circle_mover") {
    // Declare parameters with default values
    this->declare_parameter("linear_velocity", 0.5);
    this->declare_parameter("angular_velocity", 0.5);
    this->declare_parameter("publish_rate", 10.0);

    // Get parameters
    linear_vel_ = this->get_parameter("linear_velocity").as_double();
    angular_vel_ = this->get_parameter("angular_velocity").as_double();
    double rate = this->get_parameter("publish_rate").as_double();

    // Create publisher
    publisher_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/circle_bot/cmd_vel", 10);

    // Create timer with configurable rate
    auto period = std::chrono::duration<double>(1.0 / rate);
    timer_ = this->create_wall_timer(
      std::chrono::duration_cast<std::chrono::milliseconds>(period),
      std::bind(&CircleMover::move_circle, this));

    RCLCPP_INFO(this->get_logger(),
      "Circle Mover started! Linear vel: %.2f m/s, Angular vel: %.2f rad/s",
      linear_vel_, angular_vel_);
  }

private:
  void move_circle() {
    auto message = geometry_msgs::msg::Twist();
    message.linear.x = linear_vel_;   // Move forward
    message.angular.z = angular_vel_; // Rotate (creates circular motion)
    publisher_->publish(message);

    // Log occasionally
    static int counter = 0;
    if (++counter % 50 == 0) {  // Log every 5 seconds at 10Hz
      RCLCPP_INFO(this->get_logger(), "Moving in circle...");
    }
  }

  rclcpp::TimerBase::SharedPtr timer_;
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr publisher_;
  double linear_vel_;
  double angular_vel_;
};

int main(int argc, char * argv[]) {
  rclcpp::init(argc, argv);
  rclcpp::spin(std::make_shared<CircleMover>());
  rclcpp::shutdown();
  return 0;
}

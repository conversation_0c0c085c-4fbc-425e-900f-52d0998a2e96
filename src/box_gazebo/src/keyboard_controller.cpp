#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <iostream>

class KeyboardController : public rclcpp::Node {
public:
  KeyboardController() : Node("keyboard_controller") {
    // Publisher for velocity commands
    cmd_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/small_car/cmd_vel", 10);
    
    // Parameters
    this->declare_parameter("linear_speed", 2.0);
    this->declare_parameter("angular_speed", 2.0);
    
    linear_speed_ = this->get_parameter("linear_speed").as_double();
    angular_speed_ = this->get_parameter("angular_speed").as_double();
    
    // Initialize
    current_linear_ = 0.0;
    current_angular_ = 0.0;
    
    // Set up non-blocking keyboard input
    setupKeyboard();
    
    // Timer for control loop
    control_timer_ = this->create_wall_timer(
      std::chrono::milliseconds(50),  // 20 Hz
      std::bind(&KeyboardController::controlLoop, this));
    
    RCLCPP_INFO(this->get_logger(), "🎮 Keyboard Controller started!");
    RCLCPP_INFO(this->get_logger(), "📋 Controls:");
    RCLCPP_INFO(this->get_logger(), "   W/Z - Forward");
    RCLCPP_INFO(this->get_logger(), "   S   - Backward");
    RCLCPP_INFO(this->get_logger(), "   A/Q - Turn Left");
    RCLCPP_INFO(this->get_logger(), "   D   - Turn Right");
    RCLCPP_INFO(this->get_logger(), "   X   - Stop");
    RCLCPP_INFO(this->get_logger(), "   ESC - Quit");
    RCLCPP_INFO(this->get_logger(), "🚗 Ready to drive!");
  }
  
  ~KeyboardController() {
    restoreKeyboard();
  }

private:
  void setupKeyboard() {
    // Get current terminal settings
    tcgetattr(STDIN_FILENO, &old_termios_);
    
    // Set new terminal settings for non-blocking input
    struct termios new_termios = old_termios_;
    new_termios.c_lflag &= ~(ICANON | ECHO);
    new_termios.c_cc[VMIN] = 0;
    new_termios.c_cc[VTIME] = 0;
    tcsetattr(STDIN_FILENO, TCSANOW, &new_termios);
    
    // Set stdin to non-blocking
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, flags | O_NONBLOCK);
  }
  
  void restoreKeyboard() {
    tcsetattr(STDIN_FILENO, TCSANOW, &old_termios_);
  }
  
  char getKey() {
    char key = 0;
    if (read(STDIN_FILENO, &key, 1) == 1) {
      return key;
    }
    return 0;
  }
  
  void controlLoop() {
    char key = getKey();
    
    bool movement_changed = false;
    
    if (key != 0) {
      switch (key) {
        case 'w':
        case 'W':
        case 'z':
        case 'Z':
          // Forward
          current_linear_ = linear_speed_;
          current_angular_ = 0.0;
          movement_changed = true;
          RCLCPP_INFO(this->get_logger(), "🔼 Moving Forward");
          break;
          
        case 's':
        case 'S':
          // Backward
          current_linear_ = -linear_speed_;
          current_angular_ = 0.0;
          movement_changed = true;
          RCLCPP_INFO(this->get_logger(), "🔽 Moving Backward");
          break;
          
        case 'a':
        case 'A':
        case 'q':
        case 'Q':
          // Turn left
          current_linear_ = 0.0;
          current_angular_ = angular_speed_;
          movement_changed = true;
          RCLCPP_INFO(this->get_logger(), "◀️ Turning Left");
          break;
          
        case 'd':
        case 'D':
          // Turn right
          current_linear_ = 0.0;
          current_angular_ = -angular_speed_;
          movement_changed = true;
          RCLCPP_INFO(this->get_logger(), "▶️ Turning Right");
          break;
          
        case 'x':
        case 'X':
        case ' ':
          // Stop
          current_linear_ = 0.0;
          current_angular_ = 0.0;
          movement_changed = true;
          RCLCPP_INFO(this->get_logger(), "⏹️ Stopping");
          break;
          
        case 27: // ESC key
          RCLCPP_INFO(this->get_logger(), "👋 Goodbye!");
          rclcpp::shutdown();
          return;
          
        default:
          // Unknown key, do nothing
          break;
      }
    }
    
    // Always publish current velocity (for smooth control)
    auto cmd = geometry_msgs::msg::Twist();
    cmd.linear.x = current_linear_;
    cmd.angular.z = current_angular_;
    cmd_vel_pub_->publish(cmd);
    
    // Show status occasionally
    static int status_counter = 0;
    if (movement_changed || (++status_counter % 100 == 0)) {  // Every 5 seconds or on change
      RCLCPP_INFO(this->get_logger(), 
        "🚗 Speed: linear=%.1f m/s, angular=%.1f rad/s", 
        current_linear_, current_angular_);
    }
  }
  
  // Member variables
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub_;
  rclcpp::TimerBase::SharedPtr control_timer_;
  
  struct termios old_termios_;
  
  double linear_speed_;
  double angular_speed_;
  double current_linear_;
  double current_angular_;
};

int main(int argc, char** argv) {
  rclcpp::init(argc, argv);
  
  auto node = std::make_shared<KeyboardController>();
  
  try {
    rclcpp::spin(node);
  } catch (const std::exception& e) {
    RCLCPP_ERROR(node->get_logger(), "Exception: %s", e.what());
  }
  
  rclcpp::shutdown();
  return 0;
}

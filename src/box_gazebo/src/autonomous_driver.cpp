#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "sensor_msgs/msg/laser_scan.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include <cmath>
#include <algorithm>

class AutonomousDriver : public rclcpp::Node {
public:
  AutonomousDriver() : Node("autonomous_driver") {
    // Declare parameters
    this->declare_parameter("max_linear_speed", 2.0);
    this->declare_parameter("max_angular_speed", 1.0);
    this->declare_parameter("safe_distance", 2.0);
    this->declare_parameter("follow_wall_distance", 1.5);
    
    // Get parameters
    max_linear_speed_ = this->get_parameter("max_linear_speed").as_double();
    max_angular_speed_ = this->get_parameter("max_angular_speed").as_double();
    safe_distance_ = this->get_parameter("safe_distance").as_double();
    follow_wall_distance_ = this->get_parameter("follow_wall_distance").as_double();
    
    // Publishers
    cmd_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/autonomous_car/cmd_vel", 10);
    
    // Subscribers
    lidar_sub_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
      "/autonomous_car/scan", 10,
      std::bind(&AutonomousDriver::lidar_callback, this, std::placeholders::_1));
    
    odom_sub_ = this->create_subscription<nav_msgs::msg::Odometry>(
      "/autonomous_car/odom", 10,
      std::bind(&AutonomousDriver::odom_callback, this, std::placeholders::_1));
    
    // Timer for control loop
    control_timer_ = this->create_wall_timer(
      std::chrono::milliseconds(100),
      std::bind(&AutonomousDriver::control_loop, this));
    
    // Initialize state
    current_state_ = State::EXPLORING;
    has_lidar_data_ = false;
    
    RCLCPP_INFO(this->get_logger(), 
      "Autonomous Driver started! Max speed: %.2f m/s, Safe distance: %.2f m", 
      max_linear_speed_, safe_distance_);
  }

private:
  enum class State {
    EXPLORING,      // General exploration
    AVOIDING,       // Obstacle avoidance
    WALL_FOLLOWING, // Following a wall
    TURNING         // Making a turn
  };

  void lidar_callback(const sensor_msgs::msg::LaserScan::SharedPtr msg) {
    lidar_data_ = *msg;
    has_lidar_data_ = true;
    
    // Analyze lidar data
    analyze_surroundings();
  }
  
  void odom_callback(const nav_msgs::msg::Odometry::SharedPtr msg) {
    current_pose_ = *msg;
  }
  
  void analyze_surroundings() {
    if (!has_lidar_data_) return;
    
    // Find minimum distances in different sectors
    int num_readings = lidar_data_.ranges.size();
    int sector_size = num_readings / 5;  // Divide into 5 sectors
    
    front_distance_ = get_min_distance_in_sector(2 * sector_size, 3 * sector_size);
    left_distance_ = get_min_distance_in_sector(3 * sector_size, 4 * sector_size);
    right_distance_ = get_min_distance_in_sector(sector_size, 2 * sector_size);
    front_left_distance_ = get_min_distance_in_sector(3.5 * sector_size, 4.5 * sector_size);
    front_right_distance_ = get_min_distance_in_sector(1.5 * sector_size, 2.5 * sector_size);
    
    // Log distances occasionally
    static int log_counter = 0;
    if (++log_counter % 50 == 0) {  // Every 5 seconds at 10Hz
      RCLCPP_INFO(this->get_logger(), 
        "Distances - Front: %.2f, Left: %.2f, Right: %.2f", 
        front_distance_, left_distance_, right_distance_);
    }
  }
  
  double get_min_distance_in_sector(int start_idx, int end_idx) {
    if (!has_lidar_data_) return 10.0;
    
    double min_dist = 30.0;  // Max lidar range
    int num_readings = lidar_data_.ranges.size();
    
    start_idx = std::max(0, std::min(start_idx, num_readings - 1));
    end_idx = std::max(0, std::min(end_idx, num_readings - 1));
    
    for (int i = start_idx; i < end_idx; ++i) {
      if (std::isfinite(lidar_data_.ranges[i]) && lidar_data_.ranges[i] > 0.1) {
        min_dist = std::min(min_dist, static_cast<double>(lidar_data_.ranges[i]));
      }
    }
    
    return min_dist;
  }
  
  void control_loop() {
    if (!has_lidar_data_) {
      // No sensor data, stop
      publish_cmd_vel(0.0, 0.0);
      return;
    }
    
    // State machine for autonomous behavior
    switch (current_state_) {
      case State::EXPLORING:
        explore_behavior();
        break;
      case State::AVOIDING:
        avoid_obstacle();
        break;
      case State::WALL_FOLLOWING:
        follow_wall();
        break;
      case State::TURNING:
        turn_behavior();
        break;
    }
  }
  
  void explore_behavior() {
    // Check if we need to avoid obstacles
    if (front_distance_ < safe_distance_) {
      current_state_ = State::AVOIDING;
      RCLCPP_INFO(this->get_logger(), "Switching to AVOIDING mode");
      return;
    }
    
    // Check if we can follow a wall
    if (left_distance_ < follow_wall_distance_ || right_distance_ < follow_wall_distance_) {
      current_state_ = State::WALL_FOLLOWING;
      RCLCPP_INFO(this->get_logger(), "Switching to WALL_FOLLOWING mode");
      return;
    }
    
    // Continue exploring - move forward with slight random steering
    double linear_vel = max_linear_speed_ * 0.7;  // 70% of max speed
    double angular_vel = 0.0;
    
    // Add slight steering to make movement more interesting
    static int steering_counter = 0;
    steering_counter++;
    if (steering_counter % 100 == 0) {  // Change direction every 10 seconds
      steering_direction_ = (rand() % 3 - 1) * 0.3;  // -0.3, 0, or 0.3
    }
    angular_vel = steering_direction_;
    
    publish_cmd_vel(linear_vel, angular_vel);
  }
  
  void avoid_obstacle() {
    // Stop and turn away from obstacle
    double linear_vel = 0.0;
    double angular_vel = 0.0;
    
    if (front_distance_ < safe_distance_) {
      // Choose direction based on which side has more space
      if (left_distance_ > right_distance_) {
        angular_vel = max_angular_speed_ * 0.8;  // Turn left
      } else {
        angular_vel = -max_angular_speed_ * 0.8; // Turn right
      }
    } else {
      // Obstacle cleared, return to exploring
      current_state_ = State::EXPLORING;
      RCLCPP_INFO(this->get_logger(), "Obstacle cleared, returning to EXPLORING");
    }
    
    publish_cmd_vel(linear_vel, angular_vel);
  }
  
  void follow_wall() {
    double linear_vel = max_linear_speed_ * 0.5;  // Slower when wall following
    double angular_vel = 0.0;
    
    // Check if we still have a wall to follow
    if (left_distance_ > follow_wall_distance_ && right_distance_ > follow_wall_distance_) {
      current_state_ = State::EXPLORING;
      RCLCPP_INFO(this->get_logger(), "Lost wall, returning to EXPLORING");
      return;
    }
    
    // Check for obstacles ahead
    if (front_distance_ < safe_distance_) {
      current_state_ = State::AVOIDING;
      RCLCPP_INFO(this->get_logger(), "Obstacle ahead while wall following, switching to AVOIDING");
      return;
    }
    
    // Follow the closer wall
    if (left_distance_ < right_distance_) {
      // Follow left wall
      double error = left_distance_ - (follow_wall_distance_ * 0.7);
      angular_vel = -error * 1.0;  // Proportional control
    } else {
      // Follow right wall
      double error = right_distance_ - (follow_wall_distance_ * 0.7);
      angular_vel = error * 1.0;   // Proportional control
    }
    
    // Limit angular velocity
    angular_vel = std::max(-max_angular_speed_, std::min(max_angular_speed_, angular_vel));
    
    publish_cmd_vel(linear_vel, angular_vel);
  }
  
  void turn_behavior() {
    // Simple turning behavior (can be expanded)
    current_state_ = State::EXPLORING;
  }
  
  void publish_cmd_vel(double linear, double angular) {
    auto cmd = geometry_msgs::msg::Twist();
    cmd.linear.x = linear;
    cmd.angular.z = angular;
    cmd_vel_pub_->publish(cmd);
  }
  
  // Member variables
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub_;
  rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr lidar_sub_;
  rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_sub_;
  rclcpp::TimerBase::SharedPtr control_timer_;
  
  sensor_msgs::msg::LaserScan lidar_data_;
  nav_msgs::msg::Odometry current_pose_;
  
  State current_state_;
  bool has_lidar_data_;
  
  // Parameters
  double max_linear_speed_;
  double max_angular_speed_;
  double safe_distance_;
  double follow_wall_distance_;
  
  // Sensor data
  double front_distance_;
  double left_distance_;
  double right_distance_;
  double front_left_distance_;
  double front_right_distance_;
  
  // Control variables
  double steering_direction_ = 0.0;
};

int main(int argc, char** argv) {
  rclcpp::init(argc, argv);
  
  auto node = std::make_shared<AutonomousDriver>();
  
  try {
    rclcpp::spin(node);
  } catch (const std::exception& e) {
    RCLCPP_ERROR(node->get_logger(), "Exception: %s", e.what());
  }
  
  rclcpp::shutdown();
  return 0;
}

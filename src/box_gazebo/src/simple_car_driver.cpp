#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "sensor_msgs/msg/laser_scan.hpp"
#include <cmath>

class SimpleCarDriver : public rclcpp::Node {
public:
  SimpleCarDriver() : Node("simple_car_driver") {
    // Publishers
    cmd_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>(
      "/autonomous_car/cmd_vel", 10);
    
    // Subscribers
    lidar_sub_ = this->create_subscription<sensor_msgs::msg::LaserScan>(
      "/autonomous_car/scan", 10,
      std::bind(&SimpleCarDriver::lidar_callback, this, std::placeholders::_1));
    
    // Timer for control loop
    control_timer_ = this->create_wall_timer(
      std::chrono::milliseconds(200),
      std::bind(&SimpleCarDriver::control_loop, this));
    
    // Initialize
    has_lidar_data_ = false;
    front_distance_ = 10.0;
    
    RCLCPP_INFO(this->get_logger(), "Simple Car Driver started!");
  }

private:
  void lidar_callback(const sensor_msgs::msg::LaserScan::SharedPtr msg) {
    lidar_data_ = *msg;
    has_lidar_data_ = true;
    
    // Get front distance (middle of scan)
    int middle_idx = msg->ranges.size() / 2;
    front_distance_ = msg->ranges[middle_idx];
    
    if (!std::isfinite(front_distance_)) {
      front_distance_ = 10.0;
    }
  }
  
  void control_loop() {
    auto cmd = geometry_msgs::msg::Twist();
    
    if (!has_lidar_data_) {
      // No sensor data, stop
      cmd.linear.x = 0.0;
      cmd.angular.z = 0.0;
    } else if (front_distance_ < 2.0) {
      // Obstacle ahead, turn
      cmd.linear.x = 0.0;
      cmd.angular.z = 1.0;
      RCLCPP_INFO(this->get_logger(), "Obstacle detected! Turning...");
    } else {
      // Clear path, move forward
      cmd.linear.x = 1.0;
      cmd.angular.z = 0.0;
    }
    
    cmd_vel_pub_->publish(cmd);
  }
  
  // Member variables
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_pub_;
  rclcpp::Subscription<sensor_msgs::msg::LaserScan>::SharedPtr lidar_sub_;
  rclcpp::TimerBase::SharedPtr control_timer_;
  
  sensor_msgs::msg::LaserScan lidar_data_;
  bool has_lidar_data_;
  double front_distance_;
};

int main(int argc, char** argv) {
  rclcpp::init(argc, argv);
  auto node = std::make_shared<SimpleCarDriver>();
  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}

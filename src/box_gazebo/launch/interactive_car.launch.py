import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, TimerAction, DeclareLaunchArgument
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration
from launch.conditions import IfCondition
from launch_ros.actions import Node
import xacro

def generate_launch_description():
    pkg_path = get_package_share_directory('box_gazebo')
    urdf_file = os.path.join(pkg_path, 'urdf', 'small_car.urdf.xacro')
    world_file = os.path.join(pkg_path, 'worlds', 'interactive_world.world')
    
    # Launch arguments
    use_keyboard = DeclareLaunchArgument(
        'use_keyboard',
        default_value='true',
        description='Whether to start keyboard controller'
    )
    
    use_auto_mode = DeclareLaunchArgument(
        'use_auto_mode',
        default_value='true',
        description='Whether to start auto mode controller'
    )
    
    # Process XACRO file to get URDF
    robot_desc = xacro.process_file(urdf_file).toxml()
    
    # Start Gazebo with our world
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')
        ]),
        launch_arguments={
            'world': world_file,
            'verbose': 'false'
        }.items()
    )
    
    # Robot state publisher
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_desc}]
    )
    
    # Spawn the car in Gazebo
    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-topic', 'robot_description',
            '-entity', 'small_car',
            '-x', '0.0',
            '-y', '0.0', 
            '-z', '0.2',
            '-Y', '0.0'
        ],
        output='screen'
    )
    
    # Keyboard controller
    keyboard_controller = Node(
        package='box_gazebo',
        executable='keyboard_controller',
        name='keyboard_controller',
        output='screen',
        parameters=[{
            'linear_speed': 2.0,
            'angular_speed': 2.0
        }],
        condition=IfCondition(LaunchConfiguration('use_keyboard'))
    )
    
    # Auto mode controller
    auto_mode = Node(
        package='box_gazebo',
        executable='auto_mode',
        name='auto_mode',
        output='screen',
        parameters=[{
            'patrol_speed': 1.5,
            'turn_speed': 1.0
        }],
        condition=IfCondition(LaunchConfiguration('use_auto_mode'))
    )
    
    return LaunchDescription([
        # Launch arguments
        use_keyboard,
        use_auto_mode,
        
        # Start Gazebo
        gazebo,
        
        # Publish robot description
        robot_state_publisher,
        
        # Spawn car in Gazebo (wait for Gazebo to be ready)
        TimerAction(
            period=3.0,
            actions=[spawn_entity]
        ),
        
        # Start keyboard controller (wait for car to be spawned)
        TimerAction(
            period=5.0,
            actions=[keyboard_controller]
        ),
        
        # Start auto mode controller
        TimerAction(
            period=6.0,
            actions=[auto_mode]
        )
    ])

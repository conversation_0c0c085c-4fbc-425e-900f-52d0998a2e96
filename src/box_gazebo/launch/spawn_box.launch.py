import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import ExecuteProcess, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
import xacro

def generate_launch_description():
    pkg_path = get_package_share_directory('box_gazebo')
    urdf_file = os.path.join(pkg_path, 'urdf', 'circle_bot.urdf.xacro')

    # Process XACRO file to get URDF
    robot_desc = xacro.process_file(urdf_file).toxml()

    # Gazebo launch
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            [os.path.join(get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')]
        ),
        launch_arguments={'verbose': 'false'}.items()
    )

    # Robot State Publisher
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'robot_description': robot_desc,
            'use_sim_time': True
        }]
    )

    # Spawn robot
    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-entity', 'circle_bot',
            '-topic', 'robot_description',
            '-x', '0.0',
            '-y', '0.0',
            '-z', '0.1'
        ],
        output='screen'
    )
    
    # Circle Mover Node
    circle_mover = Node(
        package='box_gazebo',
        executable='circle_mover',
        name='circle_mover'
    )

    return LaunchDescription([
        # Start Gazebo
        gazebo,

        # Publish robot description
        robot_state_publisher,

        # Spawn robot in Gazebo (wait for Gazebo to be ready)
        TimerAction(
            period=3.0,
            actions=[spawn_entity]
        ),

        # Start circle mover (wait for robot to be spawned)
        TimerAction(
            period=5.0,
            actions=[circle_mover]
        ),

        # Optional: Start joint state publisher
        TimerAction(
            period=6.0,
            actions=[ExecuteProcess(
                cmd=['ros2', 'run', 'joint_state_publisher_gui', 'joint_state_publisher_gui'],
                output='screen'
            )]
        )
    ])

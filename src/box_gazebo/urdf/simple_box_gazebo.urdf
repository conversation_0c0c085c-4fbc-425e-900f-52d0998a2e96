<?xml version="1.0"?>
<robot name="simple_box">

  <!-- Base Link -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="1.0 0.5 0.2"/>  <!-- Length, Width, Height -->
      </geometry>
      <material name="blue">
        <color rgba="0.0 0.0 0.8 1.0"/>
      </material>
    </visual>
    
    <collision>
      <geometry>
        <box size="1.0 0.5 0.2"/>
      </geometry>
    </collision>
    
    <inertial>
      <mass value="10.0"/>  <!-- Mass in kg -->
      <inertia ixx="0.1" ixy="0.0" ixz="0.0"
               iyy="0.1" iyz="0.0"
               izz="0.1"/>
    </inertial>
  </link>

  <!-- Gazebo Extensions -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
    <static>false</static>  <!-- Set to true for immovable objects -->
  </gazebo>

  <!-- Gazebo ROS Control -->
  <gazebo>
    <plugin filename="libgazebo_ros_camera.so" name="camera_controller">
      <ros>
        <namespace>box_camera</namespace>
      </ros>
      <camera_name>box_camera</camera_name>
      <update_rate>30.0</update_rate>
    </plugin>
  </gazebo>

</robot>

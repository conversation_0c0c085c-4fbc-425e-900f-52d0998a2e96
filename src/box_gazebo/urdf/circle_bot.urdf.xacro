<?xml version="1.0"?>
<robot name="circle_bot" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- Constants -->
  <xacro:property name="base_length" value="0.8" />
  <xacro:property name="base_width" value="0.4" />
  <xacro:property name="base_height" value="0.2" />
  <xacro:property name="wheel_radius" value="0.1" />
  <xacro:property name="wheel_width" value="0.05" />
  <xacro:property name="wheel_y_offset" value="${base_width/2 + wheel_width/2}" />

  <!-- Material definitions -->
  <material name="blue">
    <color rgba="0.2 0.2 0.8 1.0"/>
  </material>

  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>

  <!-- Base Link -->
  <link name="base_link">
    <visual>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="5.0"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="${(1/12) * 5.0 * (base_width*base_width + base_height*base_height)}"
               ixy="0.0" ixz="0.0"
               iyy="${(1/12) * 5.0 * (base_length*base_length + base_height*base_height)}"
               iyz="0.0"
               izz="${(1/12) * 5.0 * (base_length*base_length + base_width*base_width)}"/>
    </inertial>
  </link>

  <!-- Wheel Macro -->
  <xacro:macro name="wheel" params="prefix reflect">
    <link name="${prefix}_wheel">
      <visual>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
        <material name="black"/>
      </visual>
      <collision>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
      </collision>
      <inertial>
        <mass value="0.5"/>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <inertia ixx="${(1/12) * 0.5 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                 ixy="0.0" ixz="0.0"
                 iyy="${(1/12) * 0.5 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}"
                 iyz="0.0"
                 izz="${0.5 * 0.5 * wheel_radius*wheel_radius}"/>
      </inertial>
    </link>
    
    <joint name="${prefix}_wheel_joint" type="continuous">
      <parent link="base_link"/>
      <child link="${prefix}_wheel"/>
      <origin xyz="0 ${reflect * wheel_y_offset} ${-base_height/2}" rpy="${math.pi/2} 0 0"/>
      <axis xyz="0 1 0"/>
    </joint>
    
    <gazebo reference="${prefix}_wheel">
      <material>Gazebo/Black</material>
    </gazebo>
  </xacro:macro>

  <!-- Instantiate Wheels -->
  <xacro:wheel prefix="left" reflect="1"/>
  <xacro:wheel prefix="right" reflect="-1"/>

  <!-- Differential Drive Plugin -->
  <gazebo>
    <plugin filename="libgazebo_ros_diff_drive.so" name="differential_drive_controller">
      <ros>
        <namespace>/circle_bot</namespace>
      </ros>
      <wheel_separation>${base_width + wheel_width}</wheel_separation>
      <wheel_diameter>${2*wheel_radius}</wheel_diameter>
      <left_joint>left_wheel_joint</left_joint>
      <right_joint>right_wheel_joint</right_joint>
      <wheel_torque>5</wheel_torque>
      <command_topic>cmd_vel</command_topic>
      <odometry_topic>odom</odometry_topic>
      <odometry_frame>odom</odometry_frame>
      <robot_base_frame>base_link</robot_base_frame>
      <publish_odom>true</publish_odom>
      <publish_odom_tf>true</publish_odom_tf>
    </plugin>
  </gazebo>
  
  <!-- Base Gazebo Properties -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
  </gazebo>
</robot>

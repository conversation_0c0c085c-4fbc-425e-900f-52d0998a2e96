<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="indoor_house">
    
    <!-- Physics settings -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1.0</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <!-- Scene settings -->
    <scene>
      <ambient>0.6 0.6 0.6 1</ambient>
      <background>0.9 0.9 0.9 1</background>
      <shadows>true</shadows>
    </scene>

    <!-- Sun -->
    <include>
      <uri>model://sun</uri>
    </include>

    <!-- Ground plane -->
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <!-- House Structure -->
    
    <!-- Exterior Walls -->
    <!-- North Wall -->
    <model name="north_wall">
      <pose>0 8 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.7 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- South Wall -->
    <model name="south_wall">
      <pose>0 -8 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.7 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- East Wall -->
    <model name="east_wall">
      <pose>8 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.7 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- West Wall -->
    <model name="west_wall">
      <pose>-8 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.7 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Interior Walls -->
    
    <!-- Central Hallway Wall (North-South) -->
    <model name="central_wall_ns">
      <pose>0 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Central Hallway Wall (East-West) -->
    <model name="central_wall_ew">
      <pose>0 0 1.5 0 0 1.57</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Room Labels -->
    
    <!-- Kitchen Label -->
    <model name="kitchen_label">
      <pose>-4 4 2.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2 0.1 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.8 0.2 1</ambient>
          </material>
        </visual>
      </link>
    </model>

    <!-- Bedroom Label -->
    <model name="bedroom_label">
      <pose>4 4 2.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2 0.1 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.2 0.8 1</ambient>
          </material>
        </visual>
      </link>
    </model>

    <!-- Living Room Label -->
    <model name="living_room_label">
      <pose>-4 -4 2.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2 0.1 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.2 1</ambient>
          </material>
        </visual>
      </link>
    </model>

    <!-- Office Label -->
    <model name="office_label">
      <pose>4 -4 2.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2 0.1 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.2 0.8 1</ambient>
          </material>
        </visual>
      </link>
    </model>

    <!-- KITCHEN FURNITURE (Top-Left Room) -->

    <!-- Kitchen Table -->
    <model name="kitchen_table">
      <pose>-5 5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.5 1.0 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.5 1.0 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Kitchen Chairs -->
    <model name="kitchen_chair_1">
      <pose>-5.8 5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.4 0.4 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.3 0.1 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.4 0.4 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="kitchen_chair_2">
      <pose>-4.2 5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.4 0.4 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.3 0.1 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.4 0.4 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Kitchen Cabinets -->
    <model name="kitchen_cabinet">
      <pose>-6.5 6.5 0.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2.0 1.0 1.0</size>
            </box>
          </geometry>
          <material>
            <ambient>0.7 0.7 0.6 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>2.0 1.0 1.0</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Kitchen Person -->
    <model name="kitchen_person">
      <pose>-3 6 0 0 0 0</pose>
      <static>true</static>
      <link name="body">
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.1 0.8 0.1 1</ambient>
          </material>
        </visual>
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Kitchen Moveable Boxes -->
    <model name="kitchen_box_1">
      <pose>-2.5 3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.2 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <model name="kitchen_box_2">
      <pose>-6 3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.8 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <!-- BEDROOM FURNITURE (Top-Right Room) -->

    <!-- Bed -->
    <model name="bedroom_bed">
      <pose>5.5 6 0.3 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2.0 1.5 0.6</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.6 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>2.0 1.5 0.6</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Dresser -->
    <model name="bedroom_dresser">
      <pose>3.5 6.5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.0 0.5 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.0 0.5 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Nightstand -->
    <model name="bedroom_nightstand">
      <pose>6.5 5 0.3 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.6</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.6</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Bedroom Person -->
    <model name="bedroom_person">
      <pose>3 4.5 0 0 0 1.57</pose>
      <static>true</static>
      <link name="body">
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.8 0.1 0.8 1</ambient>
          </material>
        </visual>
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Bedroom Moveable Boxes -->
    <model name="bedroom_box_1">
      <pose>2.5 3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.2 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <model name="bedroom_box_2">
      <pose>6 3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.2 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <!-- LIVING ROOM FURNITURE (Bottom-Left Room) -->

    <!-- Sofa -->
    <model name="living_room_sofa">
      <pose>-5.5 -5.5 0.4 0 0 1.57</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>2.5 1.0 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>2.5 1.0 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Coffee Table -->
    <model name="living_room_coffee_table">
      <pose>-4 -5.5 0.2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.2 0.8 0.4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.2 0.8 0.4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- TV Stand -->
    <model name="living_room_tv_stand">
      <pose>-2.5 -6.5 0.3 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.5 0.4 0.6</size>
            </box>
          </geometry>
          <material>
            <ambient>0.4 0.4 0.4 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.5 0.4 0.6</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Living Room Person -->
    <model name="living_room_person">
      <pose>-3 -4 0 0 0 0</pose>
      <static>true</static>
      <link name="body">
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.1 1</ambient>
          </material>
        </visual>
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Living Room Moveable Boxes -->
    <model name="living_room_box_1">
      <pose>-2.5 -3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.8 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <model name="living_room_box_2">
      <pose>-6 -3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.8 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <!-- OFFICE FURNITURE (Bottom-Right Room) -->

    <!-- Office Desk -->
    <model name="office_desk">
      <pose>5.5 -5.5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.5 0.8 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.5 0.8 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Office Chair -->
    <model name="office_chair">
      <pose>4.8 -5.5 0.4 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.8</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.2 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.8</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Office Bookshelf -->
    <model name="office_bookshelf">
      <pose>6.5 -6.5 0.8 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1.0 0.3 1.6</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1.0 0.3 1.6</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Office Person -->
    <model name="office_person">
      <pose>3 -4 0 0 0 -1.57</pose>
      <static>true</static>
      <link name="body">
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.2 0.2 0.8 1</ambient>
          </material>
        </visual>
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Office Moveable Boxes -->
    <model name="office_box_1">
      <pose>2.5 -3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.2 0.2 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <model name="office_box_2">
      <pose>6 -3.5 0.25 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.2 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
        <inertial>
          <mass>2.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

  </world>
</sdf>

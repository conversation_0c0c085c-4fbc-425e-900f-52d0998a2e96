<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="interactive_world">
    
    <!-- Physics settings -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1.0</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <!-- Scene settings -->
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.9 1.0 1</background>
      <shadows>true</shadows>
    </scene>

    <!-- Sun -->
    <include>
      <uri>model://sun</uri>
    </include>

    <!-- Ground plane -->
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <!-- Boundary walls to create a play area -->
    <model name="boundary_wall_north">
      <pose>0 10 1 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.5 0.5 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="boundary_wall_south">
      <pose>0 -10 1 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.5 0.5 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="boundary_wall_east">
      <pose>10 0 1 0 0 1.57</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.5 0.5 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="boundary_wall_west">
      <pose>-10 0 1 0 0 1.57</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
          <material>
            <ambient>0.5 0.5 0.5 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>20 0.2 2</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Trees -->
    <model name="tree_1">
      <pose>-5 5 0 0 0 0</pose>
      <static>true</static>
      <link name="trunk">
        <visual name="trunk_visual">
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>3</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.4 0.2 0.1 1</ambient>
          </material>
        </visual>
        <visual name="leaves_visual">
          <pose>0 0 2 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>1.5</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.1 0.6 0.1 1</ambient>
          </material>
        </visual>
        <collision name="trunk_collision">
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>3</length>
            </cylinder>
          </geometry>
        </collision>
        <collision name="leaves_collision">
          <pose>0 0 2 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>1.5</radius>
            </sphere>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="tree_2">
      <pose>5 -5 0 0 0 0</pose>
      <static>true</static>
      <link name="trunk">
        <visual name="trunk_visual">
          <geometry>
            <cylinder>
              <radius>0.25</radius>
              <length>2.5</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.4 0.2 0.1 1</ambient>
          </material>
        </visual>
        <visual name="leaves_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>1.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.1 0.5 0.1 1</ambient>
          </material>
        </visual>
        <collision name="trunk_collision">
          <geometry>
            <cylinder>
              <radius>0.25</radius>
              <length>2.5</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Balls -->
    <model name="red_ball">
      <pose>3 3 0.5 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.8 0.1 0.1 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <sphere>
              <radius>0.5</radius>
            </sphere>
          </geometry>
        </collision>
        <inertial>
          <mass>1.0</mass>
          <inertia>
            <ixx>0.1</ixx>
            <iyy>0.1</iyy>
            <izz>0.1</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <model name="blue_ball">
      <pose>-3 -3 0.3 0 0 0</pose>
      <link name="link">
        <visual name="visual">
          <geometry>
            <sphere>
              <radius>0.3</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.1 0.1 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <sphere>
              <radius>0.3</radius>
            </sphere>
          </geometry>
        </collision>
        <inertial>
          <mass>0.5</mass>
          <inertia>
            <ixx>0.05</ixx>
            <iyy>0.05</iyy>
            <izz>0.05</izz>
          </inertia>
        </inertial>
      </link>
    </model>

    <!-- Human-like figures -->
    <model name="person_1">
      <pose>-2 2 0 0 0 0</pose>
      <static>true</static>
      <link name="body">
        <!-- Body -->
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.1 0.1 0.8 1</ambient>
          </material>
        </visual>
        <!-- Head -->
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="person_2">
      <pose>2 -2 0 0 0 1.57</pose>
      <static>true</static>
      <link name="body">
        <!-- Body -->
        <visual name="body_visual">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
          <material>
            <ambient>0.8 0.1 0.1 1</ambient>
          </material>
        </visual>
        <!-- Head -->
        <visual name="head_visual">
          <pose>0 0 1.8 0 0 0</pose>
          <geometry>
            <sphere>
              <radius>0.2</radius>
            </sphere>
          </geometry>
          <material>
            <ambient>0.9 0.7 0.5 1</ambient>
          </material>
        </visual>
        <collision name="body_collision">
          <pose>0 0 1 0 0 0</pose>
          <geometry>
            <cylinder>
              <radius>0.3</radius>
              <length>1.2</length>
            </cylinder>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Some boxes as obstacles -->
    <model name="box_1">
      <pose>0 5 0.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
          <material>
            <ambient>0.6 0.4 0.2 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>1 1 1</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="box_2">
      <pose>-4 0 0.25 0 0 0.5</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
          <material>
            <ambient>0.8 0.6 0.1 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.5 0.5 0.5</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

  </world>
</sdf>

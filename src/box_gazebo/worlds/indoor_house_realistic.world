<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="indoor_house_realistic">
    
    <!-- Physics settings -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1.0</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <!-- Scene settings -->
    <scene>
      <ambient>0.7 0.7 0.7 1</ambient>
      <background>0.9 0.9 0.9 1</background>
      <shadows>true</shadows>
    </scene>

    <!-- Sun -->
    <include>
      <uri>model://sun</uri>
    </include>

    <!-- Ground plane -->
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <!-- House Structure - Walls -->
    
    <!-- Exterior Walls -->
    <model name="north_wall">
      <pose>0 8 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="south_wall">
      <pose>0 -8 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="east_wall">
      <pose>8 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="west_wall">
      <pose>-8 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.8 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Interior Walls -->
    <model name="central_wall_ns">
      <pose>0 0 1.5 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="central_wall_ew">
      <pose>0 0 1.5 0 0 1.57</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 12 3</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- KITCHEN FURNITURE (Top-Left Room: -4, 4) -->
    
    <!-- Kitchen Table -->
    <include>
      <uri>model://table</uri>
      <pose>-5 5 0 0 0 0</pose>
      <name>kitchen_table</name>
    </include>

    <!-- Kitchen Chairs -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>-5.8 5 0 0 0 1.57</pose>
      <name>kitchen_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-4.2 5 0 0 0 -1.57</pose>
      <name>kitchen_chair_2</name>
    </include>

    <!-- Kitchen Cabinet -->
    <include>
      <uri>model://cabinet</uri>
      <pose>-6.5 6.5 0 0 0 0</pose>
      <name>kitchen_cabinet</name>
    </include>

    <!-- Kitchen Person -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-3 6 0 0 0 0</pose>
      <name>kitchen_person</name>
    </include>

    <!-- Kitchen Interactive Objects -->
    <include>
      <uri>model://wood_cube_5cm</uri>
      <pose>-2.5 3.5 0 0 0 0</pose>
      <name>kitchen_box_1</name>
    </include>

    <include>
      <uri>model://wood_cube_7_5cm</uri>
      <pose>-6 3.5 0 0 0 0</pose>
      <name>kitchen_box_2</name>
    </include>

    <!-- Kitchen Bowl -->
    <include>
      <uri>model://bowl</uri>
      <pose>-5 5.3 0.8 0 0 0</pose>
      <name>kitchen_bowl</name>
    </include>

    <!-- Kitchen Coke Can -->
    <include>
      <uri>model://coke_can</uri>
      <pose>-5.3 5.3 0.8 0 0 0</pose>
      <name>kitchen_coke</name>
    </include>

    <!-- BEDROOM FURNITURE (Top-Right Room: 4, 4) -->

    <!-- Bedroom Table (as bed substitute) -->
    <include>
      <uri>model://table_marble</uri>
      <pose>5.5 6 0 0 0 0</pose>
      <name>bedroom_bed</name>
    </include>

    <!-- Bedroom Dresser -->
    <include>
      <uri>model://cabinet</uri>
      <pose>3.5 6.5 0 0 0 1.57</pose>
      <name>bedroom_dresser</name>
    </include>

    <!-- Bedroom Nightstand -->
    <include>
      <uri>model://table</uri>
      <pose>6.5 5 0 0 0 0</pose>
      <name>bedroom_nightstand</name>
    </include>

    <!-- Bedroom Person -->
    <include>
      <uri>model://person_standing</uri>
      <pose>3 4.5 0 0 0 1.57</pose>
      <name>bedroom_person</name>
    </include>

    <!-- Bedroom Interactive Objects -->
    <include>
      <uri>model://wood_cube_5cm</uri>
      <pose>2.5 3.5 0 0 0 0</pose>
      <name>bedroom_box_1</name>
    </include>

    <include>
      <uri>model://wood_cube_7_5cm</uri>
      <pose>6 3.5 0 0 0 0</pose>
      <name>bedroom_box_2</name>
    </include>

    <!-- LIVING ROOM FURNITURE (Bottom-Left Room: -4, -4) -->

    <!-- Living Room Sofa (using cafe table) -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>-5.5 -5.5 0 0 0 1.57</pose>
      <name>living_room_sofa</name>
    </include>

    <!-- Coffee Table -->
    <include>
      <uri>model://table</uri>
      <pose>-4 -5.5 0 0 0 0</pose>
      <name>living_room_coffee_table</name>
    </include>

    <!-- TV Stand -->
    <include>
      <uri>model://cabinet</uri>
      <pose>-2.5 -6.5 0 0 0 0</pose>
      <name>living_room_tv_stand</name>
    </include>

    <!-- Living Room Person -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-3 -4 0 0 0 0</pose>
      <name>living_room_person</name>
    </include>

    <!-- Living Room Interactive Objects -->
    <include>
      <uri>model://wood_cube_5cm</uri>
      <pose>-2.5 -3.5 0 0 0 0</pose>
      <name>living_room_box_1</name>
    </include>

    <include>
      <uri>model://wood_cube_7_5cm</uri>
      <pose>-6 -3.5 0 0 0 0</pose>
      <name>living_room_box_2</name>
    </include>

    <!-- OFFICE FURNITURE (Bottom-Right Room: 4, -4) -->

    <!-- Office Desk -->
    <include>
      <uri>model://table</uri>
      <pose>5.5 -5.5 0 0 0 0</pose>
      <name>office_desk</name>
    </include>

    <!-- Office Chair (using cafe table) -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>4.8 -5.5 0 0 0 0</pose>
      <name>office_chair</name>
    </include>

    <!-- Office Bookshelf -->
    <include>
      <uri>model://bookshelf</uri>
      <pose>6.5 -6.5 0 0 0 0</pose>
      <name>office_bookshelf</name>
    </include>

    <!-- Office Person -->
    <include>
      <uri>model://person_standing</uri>
      <pose>3 -4 0 0 0 -1.57</pose>
      <name>office_person</name>
    </include>

    <!-- Office Interactive Objects -->
    <include>
      <uri>model://wood_cube_5cm</uri>
      <pose>2.5 -3.5 0 0 0 0</pose>
      <name>office_box_1</name>
    </include>

    <include>
      <uri>model://wood_cube_7_5cm</uri>
      <pose>6 -3.5 0 0 0 0</pose>
      <name>office_box_2</name>
    </include>

    <!-- Office Coke Can -->
    <include>
      <uri>model://coke_can</uri>
      <pose>5.5 -5.3 0.8 0 0 0</pose>
      <name>office_coke</name>
    </include>

    <!-- Additional Realistic Objects -->

    <!-- Cardboard Boxes for more realism -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>-1 1 0 0 0 0</pose>
      <name>hallway_box_1</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>1 -1 0 0 0 0</pose>
      <name>hallway_box_2</name>
    </include>

  </world>
</sdf>

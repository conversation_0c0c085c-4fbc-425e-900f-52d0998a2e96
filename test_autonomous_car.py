#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from sensor_msgs.msg import LaserScan
import time

class AutonomousCarTester(Node):
    def __init__(self):
        super().__init__('autonomous_car_tester')
        
        # Subscribe to lidar data
        self.lidar_subscription = self.create_subscription(
            LaserScan,
            '/autonomous_car/scan',
            self.lidar_callback,
            10)
        
        # Subscribe to cmd_vel to see what commands are being sent
        self.cmd_subscription = self.create_subscription(
            Twist,
            '/autonomous_car/cmd_vel',
            self.cmd_callback,
            10)
        
        # Publisher to send test commands
        self.cmd_publisher = self.create_publisher(Twist, '/autonomous_car/cmd_vel', 10)
        
        self.get_logger().info('🚗 Autonomous Car Tester started!')
        self.get_logger().info('📡 Monitoring lidar and movement commands...')
        
        # Test movement
        self.test_movement()
        
    def lidar_callback(self, msg):
        # Get front distance (middle of scan)
        middle_idx = len(msg.ranges) // 2
        front_distance = msg.ranges[middle_idx]
        
        if hasattr(self, 'lidar_count'):
            self.lidar_count += 1
        else:
            self.lidar_count = 1
            
        # Log every 10th message to avoid spam
        if self.lidar_count % 10 == 0:
            self.get_logger().info(f'📡 Lidar: Front distance = {front_distance:.2f}m')
    
    def cmd_callback(self, msg):
        if hasattr(self, 'cmd_count'):
            self.cmd_count += 1
        else:
            self.cmd_count = 1
            
        # Log every 5th command
        if self.cmd_count % 5 == 0:
            self.get_logger().info(
                f'🎮 Command: linear={msg.linear.x:.2f} m/s, angular={msg.angular.z:.2f} rad/s'
            )
    
    def test_movement(self):
        """Send some test movement commands"""
        self.get_logger().info('🧪 Starting movement test sequence...')
        
        # Wait a bit for everything to initialize
        time.sleep(2)
        
        # Test 1: Move forward
        self.get_logger().info('Test 1: Moving forward...')
        cmd = Twist()
        cmd.linear.x = 1.0
        cmd.angular.z = 0.0
        self.cmd_publisher.publish(cmd)
        time.sleep(3)
        
        # Test 2: Turn left
        self.get_logger().info('Test 2: Turning left...')
        cmd = Twist()
        cmd.linear.x = 0.0
        cmd.angular.z = 1.0
        self.cmd_publisher.publish(cmd)
        time.sleep(2)
        
        # Test 3: Move forward again
        self.get_logger().info('Test 3: Moving forward again...')
        cmd = Twist()
        cmd.linear.x = 1.0
        cmd.angular.z = 0.0
        self.cmd_publisher.publish(cmd)
        time.sleep(3)
        
        # Test 4: Stop
        self.get_logger().info('Test 4: Stopping...')
        cmd = Twist()
        cmd.linear.x = 0.0
        cmd.angular.z = 0.0
        self.cmd_publisher.publish(cmd)
        
        self.get_logger().info('✅ Movement test sequence completed!')
        self.get_logger().info('🤖 Now the autonomous driver should take control...')

def main(args=None):
    rclpy.init(args=args)
    
    tester = AutonomousCarTester()
    
    try:
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    
    tester.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()

#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
import sys
import termios
import tty
import select
import threading

class KeyboardCarControl(Node):
    def __init__(self):
        super().__init__('keyboard_car_control')
        
        # Publisher for car movement
        self.cmd_vel_pub = self.create_publisher(Twist, '/small_car/cmd_vel', 10)
        
        # Movement speeds
        self.linear_speed = 2.0
        self.angular_speed = 1.5
        
        # Current movement state
        self.current_twist = Twist()
        
        # Setup keyboard
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
        # Timer to continuously publish commands
        self.timer = self.create_timer(0.1, self.publish_twist)  # 10 Hz
        
        # Start keyboard listener
        self.running = True
        self.keyboard_thread = threading.Thread(target=self.keyboard_listener)
        self.keyboard_thread.daemon = True
        self.keyboard_thread.start()
        
        self.print_instructions()
        
    def print_instructions(self):
        print("\n" + "="*60)
        print("🎮 KEYBOARD CAR CONTROL - REAL TIME!")
        print("="*60)
        print("🚗 Use these keys to control your car:")
        print("")
        print("   W or Z  - Move Forward")
        print("   S       - Move Backward") 
        print("   A or Q  - Turn Left")
        print("   D       - Turn Right")
        print("   X       - Stop")
        print("   ESC     - Quit")
        print("")
        print("💡 Tips:")
        print("   - Hold keys for continuous movement")
        print("   - Release to stop that movement")
        print("   - You can combine forward/back with left/right")
        print("")
        print("="*60)
        print("🚗 Ready! Start pressing keys...")
        print("")
        
    def keyboard_listener(self):
        while self.running and rclpy.ok():
            if select.select([sys.stdin], [], [], 0.1)[0]:
                key = sys.stdin.read(1)
                self.handle_key(key)
    
    def handle_key(self, key):
        key_lower = key.lower()
        
        if key_lower in ['w', 'z']:
            # Forward
            self.current_twist.linear.x = self.linear_speed
            print("🔼 Moving Forward")
            
        elif key_lower == 's':
            # Backward
            self.current_twist.linear.x = -self.linear_speed
            print("🔽 Moving Backward")
            
        elif key_lower in ['a', 'q']:
            # Turn Left
            self.current_twist.angular.z = self.angular_speed
            print("◀️ Turning Left")
            
        elif key_lower == 'd':
            # Turn Right
            self.current_twist.angular.z = -self.angular_speed
            print("▶️ Turning Right")
            
        elif key_lower == 'x' or key == ' ':
            # Stop all movement
            self.current_twist.linear.x = 0.0
            self.current_twist.angular.z = 0.0
            print("⏹️ Stopped")
            
        elif ord(key) == 27:  # ESC key
            print("\n👋 Goodbye!")
            self.stop_car()
            self.running = False
            rclcpp.shutdown()
            
        # For any other key, we could add more controls or ignore
        
    def publish_twist(self):
        """Continuously publish the current twist command"""
        self.cmd_vel_pub.publish(self.current_twist)
        
    def stop_car(self):
        """Send stop command"""
        stop_twist = Twist()
        stop_twist.linear.x = 0.0
        stop_twist.angular.z = 0.0
        self.cmd_vel_pub.publish(stop_twist)
        
    def __del__(self):
        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
        except:
            pass

def main(args=None):
    rclpy.init(args=args)
    
    try:
        controller = KeyboardCarControl()
        rclpy.spin(controller)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, controller.old_settings)
        except:
            pass
        
        # Stop the car
        if 'controller' in locals():
            controller.stop_car()
            controller.destroy_node()
        
        rclpy.shutdown()

if __name__ == '__main__':
    main()

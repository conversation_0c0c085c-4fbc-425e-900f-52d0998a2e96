# 🏠 Realistic Indoor Navigation System

## 🎯 **Overview**

This project provides an advanced indoor navigation system using **realistic Gazebo models** instead of primitive shapes. The robot can autonomously navigate between rooms in a 4-room house populated with actual furniture models from the Gazebo model library.

## 🏗️ **Environment Features**

### **🏠 House Layout**
```
(-8,8)  ┌─────────────────┐ (8,8)
        │ KITCHEN │ BEDROOM │
        │  (-4,4) │  (4,4)  │
(-8,0)  ├─────────┼─────────┤ (8,0)
        │ LIVING  │ OFFICE  │
        │ (-4,-4) │ (4,-4)  │
(-8,-8) └─────────────────┘ (8,-8)
```

### **🪑 Realistic Furniture (Gazebo Models)**

**Kitchen (Top-Left):**
- Wooden table with bowl and coke can
- Cafe table chairs
- Kitchen cabinet
- Standing person
- Interactive wooden boxes

**Bedroom (Top-Right):**
- Marble table (as bed)
- Cabinet dresser
- Wooden nightstand
- Standing person
- Interactive wooden boxes

**Living Room (Bottom-Left):**
- Cafe table (as sofa)
- Coffee table
- TV stand cabinet
- Standing person
- Interactive wooden boxes

**Office (Bottom-Right):**
- Wooden desk with coke can
- Cafe table chair
- Bookshelf
- Standing person
- Interactive wooden boxes

**Hallway:**
- Cardboard boxes for added realism

## 🤖 **Robot Features**

- **Enhanced small car** with realistic physics
- **360-degree lidar sensor** for obstacle detection
- **Front camera** for visual feedback
- **Differential drive** with improved wheel friction
- **Precise turning mechanism** with segmented control
- **Autonomous navigation** between predefined room coordinates

## 🚀 **Quick Start**

### **1. Start the Simulation**
```bash
cd /home/<USER>/box_gazebo_ws
python3 test_indoor_navigation.py
```

### **2. Navigate to Rooms**
```bash
# In a new terminal:
cd /home/<USER>/box_gazebo_ws
source install/setup.bash

# Navigate to specific rooms:
python3 simple_room_navigator.py kitchen
python3 simple_room_navigator.py bedroom
python3 simple_room_navigator.py living_room
python3 simple_room_navigator.py office

# Run full house tour:
python3 simple_room_navigator.py demo
```

## 📁 **Project Structure**

```
box_gazebo_ws/
├── src/box_gazebo/
│   ├── urdf/small_car.urdf.xacro          # Enhanced robot with friction
│   └── worlds/indoor_house_realistic.world # Realistic world with Gazebo models
├── gazebo_models-master/                   # Gazebo model library
├── simple_room_navigator.py               # Navigation system
├── test_indoor_navigation.py              # Launch script
└── README.md                              # This file
```

## 🔧 **Technical Details**

### **Navigation Algorithm**
- **Point-to-point navigation** using predefined coordinates
- **Improved turning mechanism** with segmented control
- **Enhanced wheel friction** for realistic movement
- **Time-based movement** with precise distance calculation

### **Gazebo Models Used**
- `table` - Wooden tables for kitchen, office, bedroom
- `table_marble` - Marble table as bedroom bed
- `cafe_table` - Chairs and sofa substitute
- `cabinet` - Kitchen cabinets, dresser, TV stand
- `bookshelf` - Office bookshelf
- `person_standing` - Realistic people in each room
- `wood_cube_5cm/7_5cm` - Interactive wooden boxes
- `bowl` - Kitchen bowl
- `coke_can` - Realistic objects on tables
- `cardboard_box` - Hallway objects

### **Key Improvements**
- ✅ **Realistic models** instead of primitive shapes
- ✅ **Enhanced physics** with proper wheel friction
- ✅ **Improved turning** with segmented control
- ✅ **Clean project structure** with only essential files
- ✅ **Automatic model path** configuration

## 📡 **Available ROS2 Topics**

- `/small_car/cmd_vel` - Movement commands
- `/small_car/scan` - Lidar data (360-degree)
- `/small_car/front_camera/image_raw` - Camera feed
- `/small_car/odom` - Robot odometry

## 🎮 **Navigation Commands**

```bash
# Individual room navigation:
python3 simple_room_navigator.py kitchen      # Navigate to kitchen
python3 simple_room_navigator.py bedroom      # Navigate to bedroom  
python3 simple_room_navigator.py living_room  # Navigate to living room
python3 simple_room_navigator.py office       # Navigate to office

# Full house tour:
python3 simple_room_navigator.py demo         # Visit all rooms in sequence
```

## 🔍 **Troubleshooting**

### **Models Not Loading**
If Gazebo models don't appear:
```bash
export GAZEBO_MODEL_PATH=/home/<USER>/box_gazebo_ws/gazebo_models-master:$GAZEBO_MODEL_PATH
```

### **Navigation Issues**
If robot doesn't turn properly:
- Check wheel friction settings in URDF
- Verify angular velocity commands are being published
- Ensure proper coordinate calculations

## 🎯 **Success Metrics**

✅ **Realistic Environment**: Uses actual Gazebo models instead of primitives  
✅ **Functional Navigation**: Robot successfully navigates between all rooms  
✅ **Enhanced Physics**: Proper wheel friction and turning mechanics  
✅ **Clean Structure**: Only essential files for indoor navigation  
✅ **Easy to Use**: Simple commands for room navigation  

## 🚀 **Future Enhancements**

- Add SLAM mapping with lidar data
- Implement dynamic obstacle avoidance
- Add RViz visualization
- Include more interactive objects
- Expand to multi-floor navigation

---

**🏠 Enjoy exploring your realistic indoor navigation system! 🤖**

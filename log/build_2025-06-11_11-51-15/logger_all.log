[0.189s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'box_gazebo']
[0.189s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=12, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['box_gazebo'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7878f8c49f60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7878f8dc75b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7878f8dc75b0>>, mixin_verb=('build',))
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.653s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.653s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/box_gazebo_ws'
[0.653s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.653s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.653s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.654s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.663s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.663s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ignore', 'ignore_ament_install']
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore_ament_install'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_pkg']
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_pkg'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_meta']
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_meta'
[0.664s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ros']
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ros'
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['cmake', 'python']
[0.665s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'cmake'
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'python'
[0.680s] DEBUG:colcon.colcon_core.package_identification:Package 'gazebo_models-master' with type 'cmake' and name 'gazebo_models'
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore_ament_install'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_pkg']
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_pkg'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_meta']
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_meta'
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ros']
[0.682s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ros'
[0.685s] DEBUG:colcon.colcon_core.package_identification:Package 'src/box_gazebo' with type 'ros.ament_cmake' and name 'box_gazebo'
[0.685s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.685s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.686s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.686s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.686s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.722s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gazebo_models' in 'gazebo_models-master'
[0.723s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.723s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.728s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[0.729s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 319 installed packages in /opt/ros/humble
[0.731s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_args' from command line to 'None'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target' from command line to 'None'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.776s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.776s] DEBUG:colcon.colcon_core.verb:Building package 'box_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/box_gazebo_ws/install/box_gazebo', 'merge_install': False, 'path': '/home/<USER>/box_gazebo_ws/src/box_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.776s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.777s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.777s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/box_gazebo_ws/src/box_gazebo' with build type 'ament_cmake'
[0.778s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/box_gazebo_ws/src/box_gazebo'
[0.792s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.793s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.793s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.809s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[2.811s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[2.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.868s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.879s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[2.890s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[2.891s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[2.901s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[2.902s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[2.902s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[2.903s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[2.904s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[2.906s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[2.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[2.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[2.909s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[2.911s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[2.912s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[2.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[2.915s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[2.917s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[2.918s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[2.918s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[2.919s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[2.919s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[2.919s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[2.920s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[2.920s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[2.921s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[2.921s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[2.921s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[2.921s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[2.922s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[2.922s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[2.923s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[2.923s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[2.924s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[2.924s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[2.925s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[2.926s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.926s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.926s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.926s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.936s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.937s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.937s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.955s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.955s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.ps1'
[2.957s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_ps1.py'
[2.960s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.ps1'
[2.963s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.sh'
[2.964s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_sh.py'
[2.965s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.sh'
[2.967s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.bash'
[2.968s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.bash'
[2.970s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.zsh'
[2.971s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.zsh'

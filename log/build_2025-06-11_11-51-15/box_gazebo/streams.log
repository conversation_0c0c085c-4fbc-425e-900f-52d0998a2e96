[0.030s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.213s] -- The C compiler identification is GNU 11.4.0
[0.347s] -- The CXX compiler identification is GNU 11.4.0
[0.373s] -- Detecting C compiler ABI info
[0.474s] -- Detecting C compiler ABI info - done
[0.480s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.481s] -- Detecting C compile features
[0.483s] -- Detecting C compile features - done
[0.490s] -- Detecting CXX compiler ABI info
[0.601s] -- Detecting CXX compiler AB<PERSON> info - done
[0.608s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.608s] -- Detecting CXX compile features
[0.609s] -- Detecting CXX compile features - done
[0.624s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.786s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.926s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[1.024s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.036s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.060s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.085s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.114s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.201s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.209s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.368s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[1.400s] -- Found FastRTPS: /opt/ros/humble/include  
[1.447s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.466s] -- Looking for pthread.h
[1.587s] -- Looking for pthread.h - found
[1.588s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.687s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.688s] -- Found Threads: TRUE  
[1.788s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.818s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.865s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.900s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.005s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.010s] -- Added test 'lint_cmake' to check CMake code style
[2.013s] -- Added test 'xmllint' to check XML markup files
[2.015s] -- Configuring done
[2.020s] -- Generating done
[2.024s] -- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[2.033s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[2.036s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.089s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.101s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[2.109s] -- Install configuration: ""
[2.110s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf
[2.110s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/small_car.urdf.xacro
[2.110s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds
[2.110s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/interactive_world.world
[2.110s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[2.113s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

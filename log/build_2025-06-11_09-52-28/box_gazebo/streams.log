[0.015s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.036s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.158s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.165s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.191s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.193s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.198s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.208s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.224s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.257s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.258s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.356s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.393s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.404s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.479s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.480s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.480s] -- Configured cppcheck include dirs: 
[0.480s] -- Configured cppcheck exclude dirs and/or files: 
[0.481s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.481s] -- Configured cpplint exclude dirs and/or files: 
[0.482s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.482s] -- Added test 'lint_cmake' to check CMake code style
[0.483s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.484s] -- Added test 'uncrustify' to check C / C++ code style
[0.484s] -- Configured uncrustify additional arguments: 
[0.484s] -- Added test 'xmllint' to check XML markup files
[0.486s] -- Configuring done
[0.558s] -- Generating done
[0.566s] -- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[0.575s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.578s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[0.633s] [35m[1mConsolidate compiler generated dependencies of target circle_mover[0m
[0.659s] [ 50%] [32mBuilding CXX object CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o[0m
[5.160s] [100%] [32m[1mLinking CXX executable circle_mover[0m
[5.582s] [100%] Built target circle_mover
[5.613s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[5.627s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[5.636s] -- Install configuration: ""
[5.636s] -- Execute custom install script
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
[5.636s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
[5.637s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
[5.637s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
[5.638s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
[5.638s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
[5.638s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
[5.638s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
[5.638s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[5.649s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
[5.649s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
[5.649s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
[5.650s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml
[5.652s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[ 50%] [32mBuilding CXX object CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o[0m
[100%] [32m[1mLinking CXX executable circle_mover[0m
[100%] Built target circle_mover
-- Install configuration: ""
-- Execute custom install script
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml

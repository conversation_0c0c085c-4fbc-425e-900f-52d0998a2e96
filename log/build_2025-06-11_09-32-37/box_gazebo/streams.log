[0.019s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.229s] -- The C compiler identification is GNU 11.4.0
[0.382s] -- The CXX compiler identification is GNU 11.4.0
[0.402s] -- Detecting C compiler ABI info
[0.520s] -- Detecting C compiler ABI info - done
[0.525s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.526s] -- Detecting C compile features
[0.527s] -- Detecting C compile features - done
[0.534s] -- Detecting CXX compiler AB<PERSON> info
[0.641s] -- Detecting CXX compiler <PERSON><PERSON> info - done
[0.645s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.645s] -- Detecting CXX compile features
[0.646s] -- Detecting CXX compile features - done
[0.650s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.816s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.908s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.960s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[1.035s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.042s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.060s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.081s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.114s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.201s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.206s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.345s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[1.373s] -- Found FastRTPS: /opt/ros/humble/include  
[1.423s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.442s] -- Looking for pthread.h
[1.593s] -- Looking for pthread.h - found
[1.593s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.679s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.680s] -- Found Threads: TRUE  
[1.788s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.816s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.008s] -- Added test 'copyright' to check source files copyright and LICENSE
[2.009s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.009s] -- Configured cppcheck include dirs: 
[2.009s] -- Configured cppcheck exclude dirs and/or files: 
[2.012s] -- Added test 'cpplint' to check C / C++ code against the Google style
[2.012s] -- Configured cpplint exclude dirs and/or files: 
[2.013s] -- Added test 'flake8' to check Python code syntax and style conventions
[2.014s] -- Added test 'lint_cmake' to check CMake code style
[2.014s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[2.016s] -- Added test 'uncrustify' to check C / C++ code style
[2.016s] -- Configured uncrustify additional arguments: 
[2.017s] -- Added test 'xmllint' to check XML markup files
[2.020s] -- Configuring done
[2.172s] -- Generating done
[2.177s] -- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[2.188s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[2.189s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[2.250s] [ 50%] [32mBuilding CXX object CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o[0m
[10.954s] [100%] [32m[1mLinking CXX executable circle_mover[0m
[11.571s] [100%] Built target circle_mover
[11.583s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[11.594s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[11.602s] -- Install configuration: ""
[11.602s] -- Execute custom install script
[11.602s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
[11.610s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
[11.618s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
[11.625s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
[11.635s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
[11.668s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
[11.683s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
[11.707s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
[11.716s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
[11.729s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
[11.737s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
[11.745s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
[11.752s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
[11.760s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
[11.769s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
[11.777s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[11.798s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
[11.821s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
[11.830s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
[11.837s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml
[11.850s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

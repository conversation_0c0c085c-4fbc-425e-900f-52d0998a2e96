[0.000000] (-) TimerEvent: {}
[0.003412] (box_gazebo) JobQueued: {'identifier': 'box_gazebo', 'dependencies': OrderedDict()}
[0.006119] (box_gazebo) JobStarted: {'identifier': 'box_gazebo'}
[0.023649] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'cmake'}
[0.024067] (box_gazebo) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/box_gazebo_ws/src/box_gazebo', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo'], 'cwd': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('LC_TIME', 'ar_TN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/box_gazebo_ws/src/box_gazebo/urdf'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'ar_TN.UTF-8'), ('SYSTEMD_EXEC_PID', '1322'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mehdi'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mehdi-virtual-machine:@/tmp/.ICE-unix/1288,unix/mehdi-virtual-machine:/tmp/.ICE-unix/1288'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'ar_TN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/11c037c3_34a6_447d_900d_d0a87ec311ed'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'ar_TN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.JIJN72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'ar_TN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'ar_TN.UTF-8'), ('LC_IDENTIFICATION', 'ar_TN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/box_gazebo_ws/build/box_gazebo'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'ar_TN.UTF-8'), ('LC_PAPER', 'ar_TN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble')]), 'shell': False}
[0.100135] (-) TimerEvent: {}
[0.200850] (-) TimerEvent: {}
[0.235151] (box_gazebo) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.301683] (-) TimerEvent: {}
[0.387678] (box_gazebo) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.402221] (-) TimerEvent: {}
[0.407619] (box_gazebo) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.503015] (-) TimerEvent: {}
[0.525906] (box_gazebo) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.531547] (box_gazebo) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.532554] (box_gazebo) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.533529] (box_gazebo) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.540228] (box_gazebo) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.603258] (-) TimerEvent: {}
[0.647352] (box_gazebo) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.650643] (box_gazebo) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.650916] (box_gazebo) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.651623] (box_gazebo) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.656218] (box_gazebo) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.703748] (-) TimerEvent: {}
[0.804345] (-) TimerEvent: {}
[0.821908] (box_gazebo) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.904870] (-) TimerEvent: {}
[0.913579] (box_gazebo) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.965625] (box_gazebo) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.005381] (-) TimerEvent: {}
[1.041207] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.048316] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.066250] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.086557] (box_gazebo) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.105813] (-) TimerEvent: {}
[1.119412] (box_gazebo) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.206359] (-) TimerEvent: {}
[1.207238] (box_gazebo) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.211681] (box_gazebo) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.307115] (-) TimerEvent: {}
[1.350611] (box_gazebo) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[1.379163] (box_gazebo) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[1.407794] (-) TimerEvent: {}
[1.429465] (box_gazebo) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.447593] (box_gazebo) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.508300] (-) TimerEvent: {}
[1.598770] (box_gazebo) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.599539] (box_gazebo) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.608448] (-) TimerEvent: {}
[1.684629] (box_gazebo) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.685944] (box_gazebo) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.709324] (-) TimerEvent: {}
[1.794158] (box_gazebo) StdoutLine: {'line': b'-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[1.809766] (-) TimerEvent: {}
[1.822058] (box_gazebo) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.910487] (-) TimerEvent: {}
[2.011078] (-) TimerEvent: {}
[2.013699] (box_gazebo) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[2.015233] (box_gazebo) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[2.015373] (box_gazebo) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[2.015446] (box_gazebo) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[2.018080] (box_gazebo) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[2.018159] (box_gazebo) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[2.018914] (box_gazebo) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[2.019786] (box_gazebo) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[2.020531] (box_gazebo) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[2.022122] (box_gazebo) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[2.022194] (box_gazebo) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[2.023120] (box_gazebo) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[2.025649] (box_gazebo) StdoutLine: {'line': b'-- Configuring done\n'}
[2.111711] (-) TimerEvent: {}
[2.177920] (box_gazebo) StdoutLine: {'line': b'-- Generating done\n'}
[2.182834] (box_gazebo) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo\n'}
[2.194297] (box_gazebo) CommandEnded: {'returncode': 0}
[2.194606] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'build'}
[2.194623] (box_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/box_gazebo_ws/build/box_gazebo', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('LC_TIME', 'ar_TN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/box_gazebo_ws/src/box_gazebo/urdf'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'ar_TN.UTF-8'), ('SYSTEMD_EXEC_PID', '1322'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mehdi'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mehdi-virtual-machine:@/tmp/.ICE-unix/1288,unix/mehdi-virtual-machine:/tmp/.ICE-unix/1288'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'ar_TN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/11c037c3_34a6_447d_900d_d0a87ec311ed'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'ar_TN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.JIJN72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'ar_TN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'ar_TN.UTF-8'), ('LC_IDENTIFICATION', 'ar_TN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/box_gazebo_ws/build/box_gazebo'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'ar_TN.UTF-8'), ('LC_PAPER', 'ar_TN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble')]), 'shell': False}
[2.212264] (-) TimerEvent: {}
[2.255990] (box_gazebo) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o\x1b[0m\n'}
[2.312358] (-) TimerEvent: {}
[2.413178] (-) TimerEvent: {}
[2.513719] (-) TimerEvent: {}
[2.614315] (-) TimerEvent: {}
[2.715254] (-) TimerEvent: {}
[2.816103] (-) TimerEvent: {}
[2.916923] (-) TimerEvent: {}
[3.018297] (-) TimerEvent: {}
[3.119246] (-) TimerEvent: {}
[3.219579] (-) TimerEvent: {}
[3.320578] (-) TimerEvent: {}
[3.422573] (-) TimerEvent: {}
[3.524534] (-) TimerEvent: {}
[3.625535] (-) TimerEvent: {}
[3.726625] (-) TimerEvent: {}
[3.827412] (-) TimerEvent: {}
[3.928356] (-) TimerEvent: {}
[4.029477] (-) TimerEvent: {}
[4.144559] (-) TimerEvent: {}
[4.245477] (-) TimerEvent: {}
[4.347486] (-) TimerEvent: {}
[4.449189] (-) TimerEvent: {}
[4.550297] (-) TimerEvent: {}
[4.651232] (-) TimerEvent: {}
[4.752463] (-) TimerEvent: {}
[4.853807] (-) TimerEvent: {}
[4.955185] (-) TimerEvent: {}
[5.056133] (-) TimerEvent: {}
[5.156936] (-) TimerEvent: {}
[5.257877] (-) TimerEvent: {}
[5.358976] (-) TimerEvent: {}
[5.460368] (-) TimerEvent: {}
[5.561017] (-) TimerEvent: {}
[5.662317] (-) TimerEvent: {}
[5.763878] (-) TimerEvent: {}
[5.865615] (-) TimerEvent: {}
[5.967673] (-) TimerEvent: {}
[6.069419] (-) TimerEvent: {}
[6.170115] (-) TimerEvent: {}
[6.271450] (-) TimerEvent: {}
[6.372558] (-) TimerEvent: {}
[6.473130] (-) TimerEvent: {}
[6.574202] (-) TimerEvent: {}
[6.675418] (-) TimerEvent: {}
[6.776626] (-) TimerEvent: {}
[6.877870] (-) TimerEvent: {}
[6.979537] (-) TimerEvent: {}
[7.080483] (-) TimerEvent: {}
[7.181988] (-) TimerEvent: {}
[7.283012] (-) TimerEvent: {}
[7.383560] (-) TimerEvent: {}
[7.484409] (-) TimerEvent: {}
[7.585097] (-) TimerEvent: {}
[7.686263] (-) TimerEvent: {}
[7.786944] (-) TimerEvent: {}
[7.887921] (-) TimerEvent: {}
[7.989084] (-) TimerEvent: {}
[8.090301] (-) TimerEvent: {}
[8.190987] (-) TimerEvent: {}
[8.291749] (-) TimerEvent: {}
[8.392404] (-) TimerEvent: {}
[8.493537] (-) TimerEvent: {}
[8.594780] (-) TimerEvent: {}
[8.696702] (-) TimerEvent: {}
[8.798620] (-) TimerEvent: {}
[8.900217] (-) TimerEvent: {}
[9.001358] (-) TimerEvent: {}
[9.102258] (-) TimerEvent: {}
[9.203148] (-) TimerEvent: {}
[9.304388] (-) TimerEvent: {}
[9.405473] (-) TimerEvent: {}
[9.506232] (-) TimerEvent: {}
[9.607243] (-) TimerEvent: {}
[9.708086] (-) TimerEvent: {}
[9.808960] (-) TimerEvent: {}
[9.910258] (-) TimerEvent: {}
[10.011430] (-) TimerEvent: {}
[10.112266] (-) TimerEvent: {}
[10.213266] (-) TimerEvent: {}
[10.314353] (-) TimerEvent: {}
[10.415230] (-) TimerEvent: {}
[10.516515] (-) TimerEvent: {}
[10.618559] (-) TimerEvent: {}
[10.719512] (-) TimerEvent: {}
[10.820298] (-) TimerEvent: {}
[10.921699] (-) TimerEvent: {}
[10.960357] (box_gazebo) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable circle_mover\x1b[0m\n'}
[11.022574] (-) TimerEvent: {}
[11.123588] (-) TimerEvent: {}
[11.225696] (-) TimerEvent: {}
[11.327289] (-) TimerEvent: {}
[11.428604] (-) TimerEvent: {}
[11.529807] (-) TimerEvent: {}
[11.577010] (box_gazebo) StdoutLine: {'line': b'[100%] Built target circle_mover\n'}
[11.588859] (box_gazebo) CommandEnded: {'returncode': 0}
[11.589714] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'install'}
[11.599704] (box_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/box_gazebo_ws/build/box_gazebo'], 'cwd': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('LC_TIME', 'ar_TN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/box_gazebo_ws/src/box_gazebo/urdf'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'ar_TN.UTF-8'), ('SYSTEMD_EXEC_PID', '1322'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mehdi'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/mehdi-virtual-machine:@/tmp/.ICE-unix/1288,unix/mehdi-virtual-machine:/tmp/.ICE-unix/1288'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'ar_TN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/11c037c3_34a6_447d_900d_d0a87ec311ed'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'ar_TN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.JIJN72'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'ar_TN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'ar_TN.UTF-8'), ('LC_IDENTIFICATION', 'ar_TN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/box_gazebo_ws/build/box_gazebo'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'ar_TN.UTF-8'), ('LC_PAPER', 'ar_TN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble')]), 'shell': False}
[11.607643] (box_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[11.607741] (box_gazebo) StdoutLine: {'line': b'-- Execute custom install script\n'}
[11.607782] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover\n'}
[11.616024] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py\n'}
[11.623831] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro\n'}
[11.630205] (-) TimerEvent: {}
[11.631432] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro\n'}
[11.640773] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf\n'}
[11.674161] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo\n'}
[11.689525] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo\n'}
[11.712827] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh\n'}
[11.722149] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv\n'}
[11.730647] (-) TimerEvent: {}
[11.734573] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh\n'}
[11.743123] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv\n'}
[11.750802] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash\n'}
[11.758084] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh\n'}
[11.766484] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh\n'}
[11.774761] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv\n'}
[11.782641] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv\n'}
[11.804442] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo\n'}
[11.827272] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake\n'}
[11.831441] (-) TimerEvent: {}
[11.835806] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake\n'}
[11.843553] (box_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml\n'}
[11.856501] (box_gazebo) CommandEnded: {'returncode': 0}
[11.890693] (box_gazebo) JobEnded: {'identifier': 'box_gazebo', 'rc': 0}
[11.891841] (-) EventReactorShutdown: {}

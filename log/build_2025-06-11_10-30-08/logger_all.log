[0.686s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'box_gazebo']
[0.687s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['box_gazebo'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7970b944da50>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7970b95cf0a0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7970b95cf0a0>>, mixin_verb=('build',))
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.236s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.236s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/box_gazebo_ws'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ignore', 'ignore_ament_install']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore_ament_install'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_pkg']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_pkg'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_meta']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_meta'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ros']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ros'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['cmake', 'python']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'cmake'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'python'
[1.276s] DEBUG:colcon.colcon_core.package_identification:Package 'gazebo_models-master' with type 'cmake' and name 'gazebo_models'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ignore', 'ignore_ament_install']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore_ament_install'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_pkg']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_pkg'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_meta']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_meta'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ros']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ros'
[1.283s] DEBUG:colcon.colcon_core.package_identification:Package 'src/box_gazebo' with type 'ros.ament_cmake' and name 'box_gazebo'
[1.283s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.283s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.283s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.283s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.283s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.339s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gazebo_models' in 'gazebo_models-master'
[1.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.340s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.347s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[1.349s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 319 installed packages in /opt/ros/humble
[1.351s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.432s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_args' from command line to 'None'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target' from command line to 'None'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[1.433s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.433s] DEBUG:colcon.colcon_core.verb:Building package 'box_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/box_gazebo_ws/install/box_gazebo', 'merge_install': False, 'path': '/home/<USER>/box_gazebo_ws/src/box_gazebo', 'symlink_install': False, 'test_result_base': None}
[1.433s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.435s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.436s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/box_gazebo_ws/src/box_gazebo' with build type 'ament_cmake'
[1.436s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/box_gazebo_ws/src/box_gazebo'
[1.449s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.449s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.449s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.465s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[1.714s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[1.726s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.743s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[1.744s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.760s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[1.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[1.761s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[1.761s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[1.763s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[1.765s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[1.766s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[1.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[1.767s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[1.768s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.768s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[1.770s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[1.772s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[1.773s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[1.774s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[1.776s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[1.777s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[1.777s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[1.777s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[1.777s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[1.778s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[1.778s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[1.779s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[1.779s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[1.780s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.780s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[1.780s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[1.780s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.780s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[1.781s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[1.782s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[1.782s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[1.783s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[1.783s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[1.784s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.784s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.785s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.785s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.791s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.791s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.792s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.817s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.818s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.ps1'
[1.820s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_ps1.py'
[1.822s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.ps1'
[1.824s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.sh'
[1.825s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_sh.py'
[1.826s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.sh'
[1.827s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.bash'
[1.828s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.bash'
[1.830s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.zsh'
[1.831s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.zsh'

[0.363s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'box_gazebo']
[0.363s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=12, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['box_gazebo'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x79dc90751f60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79dc908cb5b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79dc908cb5b0>>, mixin_verb=('build',))
[1.043s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.043s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.044s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.044s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.044s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.044s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.044s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/box_gazebo_ws'
[1.044s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.045s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.046s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.074s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.074s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.074s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ignore', 'ignore_ament_install']
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore_ament_install'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_pkg']
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_pkg'
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_meta']
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_meta'
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ros']
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ros'
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['cmake', 'python']
[1.076s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'cmake'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'python'
[1.128s] DEBUG:colcon.colcon_core.package_identification:Package 'gazebo_models-master' with type 'cmake' and name 'gazebo_models'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ignore', 'ignore_ament_install']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore_ament_install'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_pkg']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_pkg'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_meta']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_meta'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ros']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ros'
[1.137s] DEBUG:colcon.colcon_core.package_identification:Package 'src/box_gazebo' with type 'ros.ament_cmake' and name 'box_gazebo'
[1.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.137s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.198s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gazebo_models' in 'gazebo_models-master'
[1.199s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.199s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.206s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/box_gazebo_ws/install
[1.207s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[1.210s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 319 installed packages in /opt/ros/humble
[1.214s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_args' from command line to 'None'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target' from command line to 'None'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[1.300s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.300s] DEBUG:colcon.colcon_core.verb:Building package 'box_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/box_gazebo_ws/install/box_gazebo', 'merge_install': False, 'path': '/home/<USER>/box_gazebo_ws/src/box_gazebo', 'symlink_install': False, 'test_result_base': None}
[1.301s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.303s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.303s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/box_gazebo_ws/src/box_gazebo' with build type 'ament_cmake'
[1.303s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/box_gazebo_ws/src/box_gazebo'
[1.316s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.316s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.317s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.351s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[1.580s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[1.606s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.633s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[1.635s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.649s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[1.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[1.651s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[1.652s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[1.654s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[1.657s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[1.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[1.661s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.662s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[1.662s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[1.663s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.663s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[1.666s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[1.668s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[1.670s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[1.672s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[1.675s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[1.677s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(box_gazebo)
[1.678s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake module files
[1.680s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo' for CMake config files
[1.681s] Level 1:colcon.colcon_core.shell:create_environment_hook('box_gazebo', 'cmake_prefix_path')
[1.681s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.ps1'
[1.683s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.dsv'
[1.684s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/hook/cmake_prefix_path.sh'
[1.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib'
[1.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.686s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/pkgconfig/box_gazebo.pc'
[1.687s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/python3.10/site-packages'
[1.688s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/box_gazebo_ws/install/box_gazebo/bin'
[1.689s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.ps1'
[1.690s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv'
[1.692s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.sh'
[1.693s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.bash'
[1.695s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.zsh'
[1.697s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/box_gazebo_ws/install/box_gazebo/share/colcon-core/packages/box_gazebo)
[1.699s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.699s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.699s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.699s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.716s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.717s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.717s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.794s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.795s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.ps1'
[1.797s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_ps1.py'
[1.802s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.ps1'
[1.806s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.sh'
[1.808s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_sh.py'
[1.809s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.sh'
[1.812s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.bash'
[1.814s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.bash'
[1.817s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.zsh'
[1.819s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.zsh'

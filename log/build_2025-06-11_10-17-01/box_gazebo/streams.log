[0.011s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.175s] -- The C compiler identification is GNU 11.4.0
[0.262s] -- The CXX compiler identification is GNU 11.4.0
[0.276s] -- Detecting C compiler ABI info
[0.378s] -- Detecting C compiler ABI info - done
[0.394s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.395s] -- Detecting C compile features
[0.395s] -- Detecting C compile features - done
[0.397s] -- Detecting CXX compiler ABI info
[0.530s] -- Detecting CXX compiler ABI info - done
[0.540s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.540s] -- Detecting CXX compile features
[0.541s] -- Detecting CXX compile features - done
[0.544s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.716s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.832s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.870s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.875s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.883s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.894s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.908s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.968s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.970s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.080s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[1.106s] -- Found FastRTPS: /opt/ros/humble/include  
[1.148s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.157s] -- Looking for pthread.h
[1.249s] -- Looking for pthread.h - found
[1.249s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.352s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.354s] -- Found Threads: TRUE  
[1.416s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.435s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.455s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.471s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.559s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.561s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.562s] -- Configured cppcheck include dirs: 
[1.562s] -- Configured cppcheck exclude dirs and/or files: 
[1.564s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.564s] -- Configured cpplint exclude dirs and/or files: 
[1.564s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.565s] -- Added test 'lint_cmake' to check CMake code style
[1.565s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.567s] -- Added test 'uncrustify' to check C / C++ code style
[1.567s] -- Configured uncrustify additional arguments: 
[1.567s] -- Added test 'xmllint' to check XML markup files
[1.569s] -- Configuring done
[1.734s] -- Generating done
[1.747s] -- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.760s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[1.764s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[1.843s] [ 25%] [32mBuilding CXX object CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o[0m
[1.844s] [ 50%] [32mBuilding CXX object CMakeFiles/simple_car_driver.dir/src/simple_car_driver.cpp.o[0m
[7.417s] [ 75%] [32m[1mLinking CXX executable circle_mover[0m
[7.983s] [ 75%] Built target circle_mover
[12.157s] [100%] [32m[1mLinking CXX executable simple_car_driver[0m
[12.959s] [100%] Built target simple_car_driver
[12.973s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[12.989s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[12.999s] -- Install configuration: ""
[12.999s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/simple_car_driver
[13.003s] -- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/simple_car_driver" to ""
[13.003s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch
[13.003s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/autonomous_car.launch.py
[13.003s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf
[13.003s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/autonomous_car.urdf.xacro
[13.004s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds
[13.004s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/street_world.world
[13.004s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[13.008s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

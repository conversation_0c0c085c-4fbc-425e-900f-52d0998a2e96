[0.027s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[0.150s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.366s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.439s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.447s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.469s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.490s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.528s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.599s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.603s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.760s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.867s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.892s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.933s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[0.965s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.039s] Error parsing '/home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml':
[1.121s] Traceback (most recent call last):
[1.121s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 150, in <module>
[1.121s]     main()
[1.121s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 53, in main
[1.121s]     raise e
[1.121s]   File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 49, in main
[1.121s]     package = parse_package_string(
[1.121s]   File "/usr/lib/python3/dist-packages/catkin_pkg/package.py", line 786, in parse_package_string
[1.123s]     raise InvalidPackage('Error(s):%s' % (''.join(['\n- %s' % e for e in errors])), filename)
[1.123s] catkin_pkg.package.InvalidPackage: Error(s) in package '/home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml':
[1.123s] Error(s):
[1.123s] - The generic dependency on 'robot_state_publisher' is redundant with: build_depend, build_export_depend, exec_depend
[1.139s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
[1.139s]   execute_process(/usr/bin/python3
[1.139s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
[1.139s]   /home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml
[1.139s]   /home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_core/package.cmake)
[1.139s]   returned error code 1
[1.140s] Call Stack (most recent call first):
[1.140s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[1.140s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[1.140s]   CMakeLists.txt:36 (ament_lint_auto_find_test_dependencies)
[1.140s] 
[1.140s] [0m
[1.140s] -- Configuring incomplete, errors occurred!
[1.140s] See also "/home/<USER>/box_gazebo_ws/build/box_gazebo/CMakeFiles/CMakeOutput.log".
[1.149s] gmake: *** [Makefile:226: cmake_check_build_system] Error 1
[1.152s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '2': /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12

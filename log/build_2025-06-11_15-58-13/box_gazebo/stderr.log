Error parsing '/home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml':
Traceback (most recent call last):
  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 150, in <module>
    main()
  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 53, in main
    raise e
  File "/opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py", line 49, in main
    package = parse_package_string(
  File "/usr/lib/python3/dist-packages/catkin_pkg/package.py", line 786, in parse_package_string
    raise InvalidPackage('Error(s):%s' % (''.join(['\n- %s' % e for e in errors])), filename)
catkin_pkg.package.InvalidPackage: Error(s) in package '/home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml':
Error(s):
- The generic dependency on 'robot_state_publisher' is redundant with: build_depend, build_export_depend, exec_depend
[31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:95 (message):
  execute_process(/usr/bin/python3
  /opt/ros/humble/share/ament_cmake_core/cmake/core/package_xml_2_cmake.py
  /home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml
  /home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_core/package.cmake)
  returned error code 1
Call Stack (most recent call first):
  /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
  /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
  CMakeLists.txt:36 (ament_lint_auto_find_test_dependencies)

[0m
gmake: *** [Makefile:226: cmake_check_build_system] Error 1

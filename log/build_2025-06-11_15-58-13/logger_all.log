[0.308s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'box_gazebo']
[0.308s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=12, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['box_gazebo'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7ce99c965f60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ce99cadf5b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ce99cadf5b0>>, mixin_verb=('build',))
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.795s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.795s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/box_gazebo_ws'
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.795s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.796s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.796s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.825s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.826s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ignore', 'ignore_ament_install']
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore'
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ignore_ament_install'
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_pkg']
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_pkg'
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['colcon_meta']
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'colcon_meta'
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['ros']
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'ros'
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extensions ['cmake', 'python']
[0.827s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'cmake'
[0.842s] Level 1:colcon.colcon_core.package_identification:_identify(gazebo_models-master) by extension 'python'
[0.842s] DEBUG:colcon.colcon_core.package_identification:Package 'gazebo_models-master' with type 'cmake' and name 'gazebo_models'
[0.842s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.842s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.843s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ignore_ament_install'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_pkg']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_pkg'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['colcon_meta']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'colcon_meta'
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['ros']
[0.844s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'ros'
[0.849s] DEBUG:colcon.colcon_core.package_identification:Found ROS schema reference in package manifest in 'src/box_gazebo'
[0.849s] WARNING:colcon.colcon_core.package_identification:Failed to parse ROS package manifest in 'src/box_gazebo': Error(s) in package 'src/box_gazebo/package.xml':
Error(s):
- The generic dependency on 'robot_state_publisher' is redundant with: build_depend, build_export_depend, exec_depend
[0.849s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extensions ['cmake', 'python']
[0.849s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'cmake'
[0.850s] Level 1:colcon.colcon_core.package_identification:_identify(src/box_gazebo) by extension 'python'
[0.850s] DEBUG:colcon.colcon_core.package_identification:Package 'src/box_gazebo' with type 'cmake' and name 'box_gazebo'
[0.850s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.850s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.850s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.850s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.850s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.906s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'gazebo_models' in 'gazebo_models-master'
[0.908s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.908s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.914s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[0.916s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 319 installed packages in /opt/ros/humble
[0.918s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_args' from command line to 'None'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target' from command line to 'None'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.972s] Level 5:colcon.colcon_core.verb:set package 'box_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.972s] DEBUG:colcon.colcon_core.verb:Building package 'box_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/box_gazebo_ws/install/box_gazebo', 'merge_install': False, 'path': '/home/<USER>/box_gazebo_ws/src/box_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.972s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.974s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.974s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/box_gazebo_ws/src/box_gazebo'
[0.984s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.984s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.984s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.002s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.126s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '2': /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[2.137s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.137s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.137s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[2.137s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.146s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.147s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.147s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.161s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.162s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.ps1'
[2.165s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_ps1.py'
[2.167s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.ps1'
[2.170s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.sh'
[2.172s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/box_gazebo_ws/install/_local_setup_util_sh.py'
[2.173s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.sh'
[2.176s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.bash'
[2.177s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.bash'
[2.179s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/box_gazebo_ws/install/local_setup.zsh'
[2.180s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/box_gazebo_ws/install/setup.zsh'

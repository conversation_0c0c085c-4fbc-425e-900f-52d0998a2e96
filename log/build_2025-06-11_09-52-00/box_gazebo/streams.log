[0.018s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo
[0.035s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.162s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.171s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.199s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.203s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.211s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.224s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.236s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.268s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.268s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.373s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.418s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.434s] [0mCMake Error: File /home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml does not exist.[0m
[0.434s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/stamp.cmake:24 (configure_file):
[0.434s]   configure_file Problem configuring file
[0.434s] Call Stack (most recent call first):
[0.435s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:73 (stamp)
[0.435s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[0.435s]   /opt/ros/humble/share/ament_lint_auto/cmake/ament_lint_auto_find_test_dependencies.cmake:31 (ament_package_xml)
[0.435s]   CMakeLists.txt:38 (ament_lint_auto_find_test_dependencies)
[0.435s] 
[0.435s] [0m
[0.435s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.435s] [0mCMake Error: File /home/<USER>/box_gazebo_ws/src/box_gazebo/package.xml does not exist.[0m
[0.435s] [31mCMake Error at /opt/ros/humble/share/ament_cmake_core/cmake/core/stamp.cmake:24 (configure_file):
[0.435s]   configure_file Problem configuring file
[0.435s] Call Stack (most recent call first):
[0.435s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:73 (stamp)
[0.435s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package_xml.cmake:49 (_ament_package_xml)
[0.435s]   /opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake:63 (ament_package_xml)
[0.435s]   CMakeLists.txt:41 (ament_package)
[0.435s] 
[0.435s] [0m
[0.441s] -- Configuring incomplete, errors occurred!
[0.441s] See also "/home/<USER>/box_gazebo_ws/build/box_gazebo/CMakeFiles/CMakeOutput.log".
[0.453s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '1': /usr/bin/cmake /home/<USER>/box_gazebo_ws/src/box_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/box_gazebo_ws/install/box_gazebo

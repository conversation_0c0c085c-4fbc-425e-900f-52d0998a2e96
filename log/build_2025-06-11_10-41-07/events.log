[0.000000] (-) TimerEvent: {}
[0.000491] (-) JobUnselected: {'identifier': 'gazebo_models'}
[0.000591] (box_gazebo) JobQueued: {'identifier': 'box_gazebo', 'dependencies': OrderedDict()}
[0.001470] (box_gazebo) JobStarted: {'identifier': 'box_gazebo'}
[0.015373] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'cmake'}
[0.016939] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'build'}
[0.017023] (box_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/box_gazebo_ws/build/box_gazebo', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('LC_TIME', 'ar_TN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/box_gazebo_ws'), ('TERM_PROGRAM_VERSION', '1.100.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/195/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'ar_TN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/195/usr/share/code/code'), ('MANAGERPID', '1134'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '1285'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2815'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mehdi'), ('JOURNAL_STREAM', '8:22646'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/mehdi-virtual-machine:@/tmp/.ICE-unix/1248,unix/mehdi-virtual-machine:/tmp/.ICE-unix/1248'), ('GTK_EXE_PREFIX', '/snap/code/195/usr'), ('INVOCATION_ID', '3c5399e82146482693dee13393a8d776'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'ar_TN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/195/usr/lib/locale'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-54367c2115c7711b.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'ar_TN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/195/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.KEN572'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4b5ade1d71.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/snap/code/195/.local/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'ar_TN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'ar_TN.UTF-8'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'ar_TN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/box_gazebo_ws/build/box_gazebo'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/195/.local/share:/home/<USER>/snap/code/195:/snap/code/195/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'ar_TN.UTF-8'), ('LC_PAPER', 'ar_TN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble')]), 'shell': False}
[0.083525] (box_gazebo) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.100866] (-) TimerEvent: {}
[0.202090] (-) TimerEvent: {}
[0.291184] (box_gazebo) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.302685] (-) TimerEvent: {}
[0.359759] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.369278] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.389086] (box_gazebo) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.402915] (-) TimerEvent: {}
[0.410311] (box_gazebo) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.440267] (box_gazebo) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.503500] (-) TimerEvent: {}
[0.526300] (box_gazebo) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.532479] (box_gazebo) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.603644] (-) TimerEvent: {}
[0.701125] (box_gazebo) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.704332] (-) TimerEvent: {}
[0.805309] (-) TimerEvent: {}
[0.813297] (box_gazebo) StdoutLine: {'line': b'-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[0.844002] (box_gazebo) StdoutLine: {'line': b'-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[0.886168] (box_gazebo) StdoutLine: {'line': b'-- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[0.905965] (-) TimerEvent: {}
[0.924229] (box_gazebo) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.006949] (-) TimerEvent: {}
[1.031816] (box_gazebo) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.034987] (box_gazebo) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.036753] (box_gazebo) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[1.036943] (box_gazebo) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.038529] (box_gazebo) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.038667] (box_gazebo) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.039619] (box_gazebo) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.040694] (box_gazebo) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.041680] (box_gazebo) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.044171] (box_gazebo) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.044536] (box_gazebo) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.045204] (box_gazebo) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.048131] (box_gazebo) StdoutLine: {'line': b'-- Configuring done\n'}
[1.107709] (-) TimerEvent: {}
[1.208824] (-) TimerEvent: {}
[1.309709] (-) TimerEvent: {}
[1.395260] (box_gazebo) StdoutLine: {'line': b'-- Generating done\n'}
[1.410784] (-) TimerEvent: {}
[1.416923] (box_gazebo) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo\n'}
[1.457010] (box_gazebo) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target simple_car_driver\x1b[0m\n'}
[1.459920] (box_gazebo) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target circle_mover\x1b[0m\n'}
[1.477319] (box_gazebo) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding CXX object CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.o\x1b[0m\n'}
[1.483949] (box_gazebo) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/auto_mode.dir/src/auto_mode.cpp.o\x1b[0m\n'}
[1.491766] (box_gazebo) StdoutLine: {'line': b'[ 50%] Built target simple_car_driver\n'}
[1.494764] (box_gazebo) StdoutLine: {'line': b'[ 75%] Built target circle_mover\n'}
[1.511525] (-) TimerEvent: {}
[1.612851] (-) TimerEvent: {}
[1.713713] (-) TimerEvent: {}
[1.814587] (-) TimerEvent: {}
[1.915929] (-) TimerEvent: {}
[2.016959] (-) TimerEvent: {}
[2.118210] (-) TimerEvent: {}
[2.219138] (-) TimerEvent: {}
[2.320686] (-) TimerEvent: {}
[2.421575] (-) TimerEvent: {}
[2.522776] (-) TimerEvent: {}
[2.624254] (-) TimerEvent: {}
[2.725444] (-) TimerEvent: {}
[2.826255] (-) TimerEvent: {}
[2.927711] (-) TimerEvent: {}
[3.028553] (-) TimerEvent: {}
[3.129464] (-) TimerEvent: {}
[3.230562] (-) TimerEvent: {}
[3.332084] (-) TimerEvent: {}
[3.433547] (-) TimerEvent: {}
[3.534966] (-) TimerEvent: {}
[3.636475] (-) TimerEvent: {}
[3.737808] (-) TimerEvent: {}
[3.839298] (-) TimerEvent: {}
[3.941292] (-) TimerEvent: {}
[4.042347] (-) TimerEvent: {}
[4.143460] (-) TimerEvent: {}
[4.244643] (-) TimerEvent: {}
[4.345617] (-) TimerEvent: {}
[4.446841] (-) TimerEvent: {}
[4.548082] (-) TimerEvent: {}
[4.648995] (-) TimerEvent: {}
[4.749916] (-) TimerEvent: {}
[4.850784] (-) TimerEvent: {}
[4.953542] (-) TimerEvent: {}
[5.054821] (-) TimerEvent: {}
[5.156558] (-) TimerEvent: {}
[5.258003] (-) TimerEvent: {}
[5.360384] (-) TimerEvent: {}
[5.462547] (-) TimerEvent: {}
[5.563956] (-) TimerEvent: {}
[5.665745] (-) TimerEvent: {}
[5.767583] (-) TimerEvent: {}
[5.868784] (-) TimerEvent: {}
[5.970655] (-) TimerEvent: {}
[6.072186] (-) TimerEvent: {}
[6.174000] (-) TimerEvent: {}
[6.274728] (-) TimerEvent: {}
[6.376021] (-) TimerEvent: {}
[6.477866] (-) TimerEvent: {}
[6.579337] (-) TimerEvent: {}
[6.680500] (-) TimerEvent: {}
[6.781789] (-) TimerEvent: {}
[6.883217] (-) TimerEvent: {}
[6.984710] (-) TimerEvent: {}
[7.086508] (-) TimerEvent: {}
[7.187580] (-) TimerEvent: {}
[7.288854] (-) TimerEvent: {}
[7.389597] (-) TimerEvent: {}
[7.490462] (-) TimerEvent: {}
[7.591788] (-) TimerEvent: {}
[7.692727] (-) TimerEvent: {}
[7.793467] (-) TimerEvent: {}
[7.895310] (-) TimerEvent: {}
[7.996786] (-) TimerEvent: {}
[8.098621] (-) TimerEvent: {}
[8.200253] (-) TimerEvent: {}
[8.301467] (-) TimerEvent: {}
[8.403297] (-) TimerEvent: {}
[8.504524] (-) TimerEvent: {}
[8.605819] (-) TimerEvent: {}
[8.707605] (-) TimerEvent: {}
[8.809317] (-) TimerEvent: {}
[8.910527] (-) TimerEvent: {}
[9.011568] (-) TimerEvent: {}
[9.113323] (-) TimerEvent: {}
[9.146676] (box_gazebo) StdoutLine: {'line': b'[ 87%] \x1b[32m\x1b[1mLinking CXX executable keyboard_controller\x1b[0m\n'}
[9.214141] (-) TimerEvent: {}
[9.316042] (-) TimerEvent: {}
[9.417598] (-) TimerEvent: {}
[9.518769] (-) TimerEvent: {}
[9.619580] (-) TimerEvent: {}
[9.698418] (box_gazebo) StdoutLine: {'line': b'[ 87%] Built target keyboard_controller\n'}
[9.719737] (-) TimerEvent: {}
[9.820886] (-) TimerEvent: {}
[9.922138] (-) TimerEvent: {}
[10.023087] (-) TimerEvent: {}
[10.124197] (-) TimerEvent: {}
[10.225340] (-) TimerEvent: {}
[10.326436] (-) TimerEvent: {}
[10.427649] (-) TimerEvent: {}
[10.530819] (-) TimerEvent: {}
[10.632265] (-) TimerEvent: {}
[10.733969] (-) TimerEvent: {}
[10.837341] (-) TimerEvent: {}
[10.938722] (-) TimerEvent: {}
[11.040377] (-) TimerEvent: {}
[11.142224] (-) TimerEvent: {}
[11.244046] (-) TimerEvent: {}
[11.345918] (-) TimerEvent: {}
[11.448093] (-) TimerEvent: {}
[11.549326] (-) TimerEvent: {}
[11.650509] (-) TimerEvent: {}
[11.751767] (-) TimerEvent: {}
[11.853234] (-) TimerEvent: {}
[11.954642] (-) TimerEvent: {}
[12.056100] (-) TimerEvent: {}
[12.157451] (-) TimerEvent: {}
[12.260798] (-) TimerEvent: {}
[12.361959] (-) TimerEvent: {}
[12.462979] (-) TimerEvent: {}
[12.563733] (-) TimerEvent: {}
[12.667740] (-) TimerEvent: {}
[12.768603] (-) TimerEvent: {}
[12.873741] (-) TimerEvent: {}
[12.976786] (-) TimerEvent: {}
[13.077586] (-) TimerEvent: {}
[13.179073] (-) TimerEvent: {}
[13.281774] (-) TimerEvent: {}
[13.382921] (-) TimerEvent: {}
[13.484046] (-) TimerEvent: {}
[13.585387] (-) TimerEvent: {}
[13.687775] (-) TimerEvent: {}
[13.789993] (-) TimerEvent: {}
[13.891843] (-) TimerEvent: {}
[13.995527] (-) TimerEvent: {}
[14.096679] (-) TimerEvent: {}
[14.197795] (-) TimerEvent: {}
[14.299125] (-) TimerEvent: {}
[14.401215] (-) TimerEvent: {}
[14.502695] (-) TimerEvent: {}
[14.603739] (-) TimerEvent: {}
[14.705534] (-) TimerEvent: {}
[14.807203] (-) TimerEvent: {}
[14.908702] (-) TimerEvent: {}
[15.009459] (-) TimerEvent: {}
[15.110628] (-) TimerEvent: {}
[15.211749] (-) TimerEvent: {}
[15.313551] (-) TimerEvent: {}
[15.345725] (box_gazebo) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable auto_mode\x1b[0m\n'}
[15.414492] (-) TimerEvent: {}
[15.518988] (-) TimerEvent: {}
[15.620779] (-) TimerEvent: {}
[15.722301] (-) TimerEvent: {}
[15.823666] (-) TimerEvent: {}
[15.924627] (-) TimerEvent: {}
[16.026459] (-) TimerEvent: {}
[16.128079] (-) TimerEvent: {}
[16.229501] (-) TimerEvent: {}
[16.330777] (-) TimerEvent: {}
[16.402036] (box_gazebo) StdoutLine: {'line': b'[100%] Built target auto_mode\n'}
[16.423937] (box_gazebo) CommandEnded: {'returncode': 0}
[16.425999] (box_gazebo) JobProgress: {'identifier': 'box_gazebo', 'progress': 'install'}
[16.432160] (-) TimerEvent: {}
[16.450980] (box_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/box_gazebo_ws/build/box_gazebo'], 'cwd': '/home/<USER>/box_gazebo_ws/build/box_gazebo', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('LC_TIME', 'ar_TN.UTF-8'), ('FONTCONFIG_PATH', '/etc/fonts'), ('GIO_MODULE_DIR', '/home/<USER>/snap/code/common/.cache/gio-modules'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('GTK_EXE_PREFIX_VSCODE_SNAP_ORIG', ''), ('GDK_BACKEND_VSCODE_SNAP_ORIG', ''), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('LOCPATH_VSCODE_SNAP_ORIG', ''), ('OLDPWD', '/home/<USER>/box_gazebo_ws'), ('TERM_PROGRAM_VERSION', '1.100.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GTK_PATH', '/snap/code/195/usr/lib/x86_64-linux-gnu/gtk-3.0'), ('XDG_DATA_HOME_VSCODE_SNAP_ORIG', ''), ('GTK_IM_MODULE_FILE', '/home/<USER>/snap/code/common/.cache/immodules/immodules.cache'), ('GIO_LAUNCHED_DESKTOP_FILE', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('GSETTINGS_SCHEMA_DIR_VSCODE_SNAP_ORIG', ''), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/snap/code/195/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'ar_TN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/snap/code/195/usr/share/code/code'), ('MANAGERPID', '1134'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('SYSTEMD_EXEC_PID', '1285'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/libs/debugpy'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '2815'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('COLCON_PREFIX_PATH', '/home/<USER>/ros2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mehdi'), ('JOURNAL_STREAM', '8:22646'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_CONFIG_DIRS_VSCODE_SNAP_ORIG', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_SESSION_CLASS', 'user'), ('XDG_DATA_DIRS_VSCODE_SNAP_ORIG', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts'), ('SESSION_MANAGER', 'local/mehdi-virtual-machine:@/tmp/.ICE-unix/1248,unix/mehdi-virtual-machine:/tmp/.ICE-unix/1248'), ('GTK_EXE_PREFIX', '/snap/code/195/usr'), ('INVOCATION_ID', '3c5399e82146482693dee13393a8d776'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'ar_TN.UTF-8'), ('BAMF_DESKTOP_FILE_HINT', '/var/lib/snapd/desktop/applications/code_code.desktop'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LOCPATH', '/snap/code/195/usr/lib/locale'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-54367c2115c7711b.txt'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'ar_TN.UTF-8'), ('GIO_MODULE_DIR_VSCODE_SNAP_ORIG', ''), ('XDG_DATA_HOME', '/home/<USER>/snap/code/195/.local/share'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.KEN572'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4b5ade1d71.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/home/<USER>/snap/code/195/.local/share/glib-2.0/schemas'), ('AMENT_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'ar_TN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GTK_PATH_VSCODE_SNAP_ORIG', ''), ('FONTCONFIG_FILE', '/etc/fonts/fonts.conf'), ('GTK_IM_MODULE_FILE_VSCODE_SNAP_ORIG', ''), ('LC_MEASUREMENT', 'ar_TN.UTF-8'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('LC_IDENTIFICATION', 'ar_TN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/box_gazebo_ws/build/box_gazebo'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/home/<USER>/snap/code/195/.local/share:/home/<USER>/snap/code/195:/snap/code/195/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'ar_TN.UTF-8'), ('LC_PAPER', 'ar_TN.UTF-8'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/home/<USER>/ros2_ws/install/my_box_bot:/opt/ros/humble')]), 'shell': False}
[16.470719] (box_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[16.471380] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/simple_car_driver\n'}
[16.472601] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller\n'}
[16.475818] (box_gazebo) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller" to ""\n'}
[16.476045] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode\n'}
[16.481699] (box_gazebo) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode" to ""\n'}
[16.482053] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch\n'}
[16.482164] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/interactive_car.launch.py\n'}
[16.482211] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/autonomous_car.launch.py\n'}
[16.482276] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf\n'}
[16.482344] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/small_car.urdf.xacro\n'}
[16.482601] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/autonomous_car.urdf.xacro\n'}
[16.482724] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds\n'}
[16.482796] (box_gazebo) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/street_world.world\n'}
[16.482911] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/interactive_world.world\n'}
[16.483516] (box_gazebo) StdoutLine: {'line': b'-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv\n'}
[16.486068] (box_gazebo) CommandEnded: {'returncode': 0}
[16.529462] (box_gazebo) JobEnded: {'identifier': 'box_gazebo', 'rc': 0}
[16.531806] (-) EventReactorShutdown: {}

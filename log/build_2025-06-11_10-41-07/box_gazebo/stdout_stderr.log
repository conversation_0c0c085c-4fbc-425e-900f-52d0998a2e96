-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[35m[1mConsolidate compiler generated dependencies of target simple_car_driver[0m
[35m[1mConsolidate compiler generated dependencies of target circle_mover[0m
[ 12%] [32mBuilding CXX object CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.o[0m
[ 25%] [32mBuilding CXX object CMakeFiles/auto_mode.dir/src/auto_mode.cpp.o[0m
[ 50%] Built target simple_car_driver
[ 75%] Built target circle_mover
[ 87%] [32m[1mLinking CXX executable keyboard_controller[0m
[ 87%] Built target keyboard_controller
[100%] [32m[1mLinking CXX executable auto_mode[0m
[100%] Built target auto_mode
-- Install configuration: ""
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/simple_car_driver
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller
-- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller" to ""
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode
-- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode" to ""
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/interactive_car.launch.py
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/autonomous_car.launch.py
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/small_car.urdf.xacro
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/autonomous_car.urdf.xacro
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds
-- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/street_world.world
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/interactive_world.world
-- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv

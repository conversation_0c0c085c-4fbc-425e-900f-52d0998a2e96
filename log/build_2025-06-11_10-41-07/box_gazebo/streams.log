[0.020s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[0.082s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.290s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.358s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.368s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.388s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.409s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.439s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.525s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.531s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.700s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.812s] -- Found geometry_msgs: 4.8.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[0.843s] -- Found sensor_msgs: 4.8.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[0.885s] -- Found nav_msgs: 4.8.0 (/opt/ros/humble/share/nav_msgs/cmake)
[0.923s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.030s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.034s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.035s] -- Configured cppcheck include dirs: 
[1.035s] -- Configured cppcheck exclude dirs and/or files: 
[1.037s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.037s] -- Configured cpplint exclude dirs and/or files: 
[1.038s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.039s] -- Added test 'lint_cmake' to check CMake code style
[1.040s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.043s] -- Added test 'uncrustify' to check C / C++ code style
[1.043s] -- Configured uncrustify additional arguments: 
[1.044s] -- Added test 'xmllint' to check XML markup files
[1.047s] -- Configuring done
[1.394s] -- Generating done
[1.416s] -- Build files have been written to: /home/<USER>/box_gazebo_ws/build/box_gazebo
[1.456s] [35m[1mConsolidate compiler generated dependencies of target simple_car_driver[0m
[1.458s] [35m[1mConsolidate compiler generated dependencies of target circle_mover[0m
[1.476s] [ 12%] [32mBuilding CXX object CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.o[0m
[1.483s] [ 25%] [32mBuilding CXX object CMakeFiles/auto_mode.dir/src/auto_mode.cpp.o[0m
[1.490s] [ 50%] Built target simple_car_driver
[1.493s] [ 75%] Built target circle_mover
[9.145s] [ 87%] [32m[1mLinking CXX executable keyboard_controller[0m
[9.697s] [ 87%] Built target keyboard_controller
[15.344s] [100%] [32m[1mLinking CXX executable auto_mode[0m
[16.401s] [100%] Built target auto_mode
[16.423s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[16.452s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[16.469s] -- Install configuration: ""
[16.470s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/simple_car_driver
[16.471s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller
[16.474s] -- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/keyboard_controller" to ""
[16.475s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode
[16.480s] -- Set runtime path of "/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/auto_mode" to ""
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch
[16.481s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/interactive_car.launch.py
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/autonomous_car.launch.py
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf
[16.481s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/small_car.urdf.xacro
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/autonomous_car.urdf.xacro
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds
[16.481s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/street_world.world
[16.481s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/interactive_world.world
[16.482s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[16.485s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

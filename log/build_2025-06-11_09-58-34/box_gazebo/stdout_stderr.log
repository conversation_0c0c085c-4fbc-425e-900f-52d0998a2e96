[35m[1mConsolidate compiler generated dependencies of target circle_mover[0m
[100%] Built target circle_mover
-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
-- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml

[0.018s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[0.078s] [35m[1mConsolidate compiler generated dependencies of target circle_mover[0m
[0.100s] [100%] Built target circle_mover
[0.114s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j8 -l8
[0.126s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[0.134s] -- Install configuration: ""
[0.135s] -- Execute custom install script
[0.135s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
[0.135s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
[0.135s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
[0.136s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
[0.136s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
[0.136s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
[0.136s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
[0.136s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
[0.137s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
[0.138s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
[0.138s] -- Symlinking: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[0.151s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
[0.152s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
[0.152s] -- Up-to-date symlink: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml
[0.154s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

[0.025s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[0.146s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/box_gazebo_ws/build/box_gazebo -- -j12 -l12
[0.162s] Invoking command in '/home/<USER>/box_gazebo_ws/build/box_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo
[0.171s] -- Install configuration: ""
[0.181s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf
[0.181s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/small_car.urdf.xacro
[0.181s] -- Up-to-date: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds
[0.181s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//worlds/indoor_house_realistic.world
[0.181s] -- Installing: /home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
[0.187s] Invoked command in '/home/<USER>/box_gazebo_ws/build/box_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/box_gazebo_ws/build/box_gazebo

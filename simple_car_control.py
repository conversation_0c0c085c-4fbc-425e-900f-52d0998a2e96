#!/usr/bin/env python3

import subprocess
import time
import os

def send_command(linear_x, angular_z):
    """Send movement command to the car"""
    cmd = f'ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{{linear: {{x: {linear_x}, y: 0.0, z: 0.0}}, angular: {{x: 0.0, y: 0.0, z: {angular_z}}}}}"'
    subprocess.run(cmd, shell=True, cwd="/home/<USER>/box_gazebo_ws", 
                   env=dict(os.environ, **{"ROS_DOMAIN_ID": "0"}))

def main():
    print("🚗 Simple Car Control Demo")
    print("=" * 30)
    
    # Change to workspace directory
    os.chdir("/home/<USER>/box_gazebo_ws")
    
    # Source environment
    source_cmd = "source install/setup.bash && "
    
    print("🎮 Demonstrating car movements...")
    print()
    
    # Demo sequence
    movements = [
        ("🔼 Moving Forward", 2.0, 0.0, 3),
        ("⏹️ Stopping", 0.0, 0.0, 1),
        ("◀️ Turning Left", 0.0, 1.5, 2),
        ("⏹️ Stopping", 0.0, 0.0, 1),
        ("🔼 Moving Forward", 2.0, 0.0, 2),
        ("⏹️ Stopping", 0.0, 0.0, 1),
        ("▶️ Turning Right", 0.0, -1.5, 2),
        ("⏹️ Stopping", 0.0, 0.0, 1),
        ("🔽 Moving Backward", -1.5, 0.0, 2),
        ("⏹️ Final Stop", 0.0, 0.0, 1),
    ]
    
    for description, linear, angular, duration in movements:
        print(f"{description} (linear: {linear}, angular: {angular})")
        
        # Send command multiple times during the duration
        for _ in range(duration * 2):  # 2 commands per second
            send_command(linear, angular)
            time.sleep(0.5)
        
        print(f"   ✅ Completed")
        time.sleep(0.5)
    
    print()
    print("🎉 Demo completed!")
    print()
    print("🎮 Manual Control Commands:")
    print("Forward:  ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist \"{linear: {x: 2.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}\"")
    print("Backward: ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist \"{linear: {x: -2.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}\"")
    print("Left:     ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist \"{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 1.5}}\"")
    print("Right:    ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist \"{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -1.5}}\"")
    print("Stop:     ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist \"{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}\"")

if __name__ == '__main__':
    main()

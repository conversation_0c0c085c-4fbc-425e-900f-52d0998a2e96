<?xml version="1.0"?>
<robot name="small_car" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- Constants for small car -->
  <xacro:property name="car_length" value="1.0" />
  <xacro:property name="car_width" value="0.6" />
  <xacro:property name="car_height" value="0.4" />
  <xacro:property name="wheel_radius" value="0.15" />
  <xacro:property name="wheel_width" value="0.08" />
  <xacro:property name="wheelbase" value="0.7" />
  <xacro:property name="track_width" value="0.5" />
  
  <!-- Material definitions -->
  <material name="car_red">
    <color rgba="0.8 0.1 0.1 1.0"/>
  </material>
  
  <material name="car_blue">
    <color rgba="0.1 0.3 0.8 1.0"/>
  </material>
  
  <material name="black">
    <color rgba="0.1 0.1 0.1 1.0"/>
  </material>
  
  <material name="yellow">
    <color rgba="0.9 0.9 0.1 1.0"/>
  </material>

  <!-- Base Link (Car Body) -->
  <link name="base_link">
    <!-- Main body -->
    <visual>
      <origin xyz="0 0 0.2" rpy="0 0 0"/>
      <geometry>
        <box size="${car_length} ${car_width} ${car_height}"/>
      </geometry>
      <material name="car_red"/>
    </visual>
    
    <!-- Front bumper -->
    <visual name="front_bumper">
      <origin xyz="${car_length/2 + 0.05} 0 0.1" rpy="0 0 0"/>
      <geometry>
        <box size="0.1 ${car_width - 0.1} 0.2"/>
      </geometry>
      <material name="black"/>
    </visual>
    
    <!-- Windshield -->
    <visual name="windshield">
      <origin xyz="0.2 0 0.35" rpy="0 0.3 0"/>
      <geometry>
        <box size="0.3 ${car_width - 0.1} 0.1"/>
      </geometry>
      <material name="car_blue"/>
    </visual>
    
    <!-- Roof light -->
    <visual name="roof_light">
      <origin xyz="0 0 0.45" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="yellow"/>
    </visual>
    
    <collision>
      <origin xyz="0 0 0.2" rpy="0 0 0"/>
      <geometry>
        <box size="${car_length} ${car_width} ${car_height}"/>
      </geometry>
    </collision>
    
    <inertial>
      <mass value="50.0"/>
      <origin xyz="0 0 0.2" rpy="0 0 0"/>
      <inertia ixx="${(1/12) * 50.0 * (car_width*car_width + car_height*car_height)}" 
               ixy="0.0" ixz="0.0"
               iyy="${(1/12) * 50.0 * (car_length*car_length + car_height*car_height)}" 
               iyz="0.0"
               izz="${(1/12) * 50.0 * (car_length*car_length + car_width*car_width)}"/>
    </inertial>
  </link>

  <!-- Wheel macro -->
  <xacro:macro name="wheel" params="name x y z">
    <link name="${name}_wheel">
      <visual>
        <origin xyz="0 0 0" rpy="1.5708 0 0"/>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
        <material name="black"/>
      </visual>
      
      <collision>
        <origin xyz="0 0 0" rpy="1.5708 0 0"/>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
      </collision>
      
      <inertial>
        <mass value="5.0"/>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <inertia ixx="${(1/12) * 5.0 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}" 
                 ixy="0.0" ixz="0.0"
                 iyy="${(1/12) * 5.0 * (3*wheel_radius*wheel_radius + wheel_width*wheel_width)}" 
                 iyz="0.0"
                 izz="${0.5 * 5.0 * wheel_radius*wheel_radius}"/>
      </inertial>
    </link>

    <joint name="${name}_wheel_joint" type="continuous">
      <parent link="base_link"/>
      <child link="${name}_wheel"/>
      <origin xyz="${x} ${y} ${z}" rpy="0 0 0"/>
      <axis xyz="0 1 0"/>
    </joint>
  </xacro:macro>

  <!-- Create wheels -->
  <xacro:wheel name="front_left" x="${wheelbase/2}" y="${track_width/2}" z="0"/>
  <xacro:wheel name="front_right" x="${wheelbase/2}" y="${-track_width/2}" z="0"/>
  <xacro:wheel name="rear_left" x="${-wheelbase/2}" y="${track_width/2}" z="0"/>
  <xacro:wheel name="rear_right" x="${-wheelbase/2}" y="${-track_width/2}" z="0"/>

  <!-- Simple camera sensor -->
  <link name="camera_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.05 0.05"/>
      </geometry>
      <material name="black"/>
    </visual>
    
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.05 0.05"/>
      </geometry>
    </collision>
    
    <inertial>
      <mass value="0.1"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0"
               iyy="0.001" iyz="0.0"
               izz="0.001"/>
    </inertial>
  </link>

  <joint name="camera_joint" type="fixed">
    <parent link="base_link"/>
    <child link="camera_link"/>
    <origin xyz="${car_length/2 - 0.1} 0 ${car_height + 0.05}" rpy="0 0 0"/>
  </joint>

  <!-- Lidar sensor -->
  <link name="lidar_link">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="black"/>
    </visual>

    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
    </collision>

    <inertial>
      <mass value="0.2"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="0.001" ixy="0.0" ixz="0.0"
               iyy="0.001" iyz="0.0"
               izz="0.001"/>
    </inertial>
  </link>

  <joint name="lidar_joint" type="fixed">
    <parent link="base_link"/>
    <child link="lidar_link"/>
    <origin xyz="0 0 ${car_height + 0.15}" rpy="0 0 0"/>
  </joint>

  <!-- Gazebo wheel friction properties -->
  <gazebo reference="front_left_wheel">
    <surface>
      <friction>
        <ode>
          <mu>1.0</mu>
          <mu2>1.0</mu2>
          <fdir1>0 0 1</fdir1>
          <slip1>0.0</slip1>
          <slip2>0.0</slip2>
        </ode>
      </friction>
    </surface>
  </gazebo>

  <gazebo reference="front_right_wheel">
    <surface>
      <friction>
        <ode>
          <mu>1.0</mu>
          <mu2>1.0</mu2>
          <fdir1>0 0 1</fdir1>
          <slip1>0.0</slip1>
          <slip2>0.0</slip2>
        </ode>
      </friction>
    </surface>
  </gazebo>

  <gazebo reference="rear_left_wheel">
    <surface>
      <friction>
        <ode>
          <mu>1.0</mu>
          <mu2>1.0</mu2>
          <fdir1>0 0 1</fdir1>
          <slip1>0.0</slip1>
          <slip2>0.0</slip2>
        </ode>
      </friction>
    </surface>
  </gazebo>

  <gazebo reference="rear_right_wheel">
    <surface>
      <friction>
        <ode>
          <mu>1.0</mu>
          <mu2>1.0</mu2>
          <fdir1>0 0 1</fdir1>
          <slip1>0.0</slip1>
          <slip2>0.0</slip2>
        </ode>
      </friction>
    </surface>
  </gazebo>

  <!-- Gazebo plugins -->
  <gazebo>
    <!-- Differential drive plugin -->
    <plugin name="differential_drive_controller" filename="libgazebo_ros_diff_drive.so">
      <ros>
        <namespace>/small_car</namespace>
      </ros>
      
      <!-- Wheel information -->
      <left_joint>rear_left_wheel_joint</left_joint>
      <right_joint>rear_right_wheel_joint</right_joint>
      <wheel_separation>${track_width}</wheel_separation>
      <wheel_diameter>${2*wheel_radius}</wheel_diameter>
      
      <!-- Limits -->
      <max_wheel_torque>50</max_wheel_torque>
      <max_wheel_acceleration>5.0</max_wheel_acceleration>
      
      <!-- Output -->
      <publish_odom>true</publish_odom>
      <publish_odom_tf>true</publish_odom_tf>
      <publish_wheel_tf>true</publish_wheel_tf>
      
      <odometry_frame>odom</odometry_frame>
      <robot_base_frame>base_link</robot_base_frame>
    </plugin>
  </gazebo>

  <!-- Camera plugin -->
  <gazebo reference="camera_link">
    <sensor type="camera" name="front_camera">
      <update_rate>30.0</update_rate>
      <camera name="head">
        <horizontal_fov>1.3962634</horizontal_fov>
        <image>
          <width>640</width>
          <height>480</height>
          <format>R8G8B8</format>
        </image>
        <clip>
          <near>0.02</near>
          <far>100</far>
        </clip>
      </camera>
      <plugin name="camera_controller" filename="libgazebo_ros_camera.so">
        <ros>
          <namespace>/small_car</namespace>
          <remapping>image_raw:=camera/image_raw</remapping>
          <remapping>camera_info:=camera/camera_info</remapping>
        </ros>
        <camera_name>front_camera</camera_name>
        <frame_name>camera_link</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- Lidar sensor plugin -->
  <gazebo reference="lidar_link">
    <sensor type="ray" name="lidar_sensor">
      <pose>0 0 0 0 0 0</pose>
      <visualize>true</visualize>
      <update_rate>10</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>360</samples>
            <resolution>1</resolution>
            <min_angle>-3.14159</min_angle>
            <max_angle>3.14159</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.1</min>
          <max>10.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.01</stddev>
        </noise>
      </ray>
      <plugin name="gazebo_ros_lidar_controller" filename="libgazebo_ros_ray_sensor.so">
        <ros>
          <namespace>/small_car</namespace>
          <remapping>~/out:=scan</remapping>
        </ros>
        <output_type>sensor_msgs/LaserScan</output_type>
        <frame_name>lidar_link</frame_name>
      </plugin>
    </sensor>
  </gazebo>

</robot>

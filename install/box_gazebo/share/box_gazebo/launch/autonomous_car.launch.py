import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import ExecuteProcess, IncludeLaunchDescription, TimerAction, SetEnvironmentVariable
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.actions import Node
import xacro

def generate_launch_description():
    pkg_path = get_package_share_directory('box_gazebo')
    urdf_file = os.path.join(pkg_path, 'urdf', 'autonomous_car.urdf.xacro')
    world_file = os.path.join(pkg_path, 'worlds', 'street_world.world')
    
    # Get the workspace root directory
    workspace_root = os.path.dirname(os.path.dirname(os.path.dirname(pkg_path)))
    gazebo_models_path = os.path.join(workspace_root, 'gazebo_models-master')
    
    # Process XACRO file to get URDF
    robot_desc = xacro.process_file(urdf_file).toxml()
    
    # Set Gazebo model path environment variable
    set_gazebo_model_path = SetEnvironmentVariable(
        name='GAZEBO_MODEL_PATH',
        value=gazebo_models_path
    )
    
    # Start Gazebo with our world
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('gazebo_ros'), 'launch', 'gazebo.launch.py')
        ]),
        launch_arguments={
            'world': world_file,
            'verbose': 'true'
        }.items()
    )
    
    # Robot state publisher
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_desc}]
    )
    
    # Spawn the car in Gazebo
    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-topic', 'robot_description',
            '-entity', 'autonomous_car',
            '-x', '0.0',
            '-y', '0.0', 
            '-z', '0.5',
            '-Y', '0.0'
        ],
        output='screen'
    )
    
    # Simple car driver node
    simple_car_driver = Node(
        package='box_gazebo',
        executable='simple_car_driver',
        name='simple_car_driver',
        output='screen'
    )
    
    # RViz for visualization (optional)
    rviz_config = os.path.join(pkg_path, 'rviz', 'autonomous_car.rviz')
    rviz = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config] if os.path.exists(rviz_config) else [],
        output='screen'
    )
    
    return LaunchDescription([
        # Set environment
        set_gazebo_model_path,
        
        # Start Gazebo
        gazebo,
        
        # Publish robot description
        robot_state_publisher,
        
        # Spawn car in Gazebo (wait for Gazebo to be ready)
        TimerAction(
            period=5.0,
            actions=[spawn_entity]
        ),
        
        # Start simple car driver (wait for car to be spawned)
        TimerAction(
            period=8.0,
            actions=[simple_car_driver]
        ),
        
        # Start RViz (optional)
        TimerAction(
            period=10.0,
            actions=[rviz]
        )
    ])

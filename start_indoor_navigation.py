#!/usr/bin/env python3

import subprocess
import time
import os
import signal
import sys
import threading

def signal_handler(sig, frame):
    print('\n🛑 Shutting down Indoor Navigation System...')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

class IndoorNavigationLauncher:
    def __init__(self):
        self.workspace_dir = "/home/<USER>/box_gazebo_ws"
        self.processes = []
        
    def run_command(self, command, description, wait_time=0):
        """Run a command and track the process"""
        print(f"🚀 {description}...")
        
        full_command = f"cd {self.workspace_dir} && source install/setup.bash && {command}"
        process = subprocess.Popen(full_command, shell=True, executable='/bin/bash')
        self.processes.append((process, description))
        
        if wait_time > 0:
            print(f"⏳ Waiting {wait_time} seconds for {description} to initialize...")
            time.sleep(wait_time)
            
        return process
        
    def check_dependencies(self):
        """Check if required dependencies are available"""
        print("🔍 Checking dependencies...")
        
        # Check if ROS2 is available
        try:
            result = subprocess.run("ros2 --version", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ ROS2 is available")
            else:
                print("❌ ROS2 not found. Please install ROS2 first.")
                return False
        except:
            print("❌ ROS2 not found. Please install ROS2 first.")
            return False
            
        # Check if workspace is built
        if not os.path.exists(os.path.join(self.workspace_dir, "install")):
            print("❌ Workspace not built. Building now...")
            self.build_workspace()
            
        return True
        
    def build_workspace(self):
        """Build the ROS2 workspace"""
        print("🔨 Building workspace...")
        build_command = f"cd {self.workspace_dir} && colcon build --packages-select box_gazebo"
        result = subprocess.run(build_command, shell=True)
        
        if result.returncode == 0:
            print("✅ Workspace built successfully")
        else:
            print("❌ Failed to build workspace")
            sys.exit(1)
            
    def start_simulation(self):
        """Start Gazebo simulation with indoor house"""
        print("\n" + "="*60)
        print("🏠 STARTING INDOOR NAVIGATION SYSTEM")
        print("="*60)
        
        # Start Gazebo with indoor house
        gazebo_cmd = "ros2 launch gazebo_ros gazebo.launch.py world:=src/box_gazebo/worlds/indoor_house.world"
        self.run_command(gazebo_cmd, "Starting Gazebo with indoor house", 5)
        
        # Start robot state publisher
        robot_desc_cmd = 'ros2 run robot_state_publisher robot_state_publisher --ros-args -p robot_description:="$(xacro src/box_gazebo/urdf/small_car.urdf.xacro)"'
        self.run_command(robot_desc_cmd, "Starting robot state publisher", 2)
        
        # Spawn the enhanced car with lidar
        spawn_cmd = "ros2 run gazebo_ros spawn_entity.py -topic robot_description -entity small_car -x -2 -y -2 -z 0.2"
        spawn_process = self.run_command(spawn_cmd, "Spawning enhanced car with lidar", 3)
        spawn_process.wait()  # Wait for spawn to complete
        
        print("✅ Simulation environment ready!")
        
    def start_navigation_system(self):
        """Start the navigation system components"""
        print("\n🧭 Starting Navigation System...")
        
        # Start indoor navigation system
        nav_cmd = "python3 indoor_navigation_system.py"
        self.run_command(nav_cmd, "Starting autonomous navigation system", 2)
        
        # Start dual control interface
        control_cmd = "python3 dual_control_interface.py"
        self.run_command(control_cmd, "Starting dual control interface", 1)
        
        print("✅ Navigation system ready!")
        
    def start_monitoring(self):
        """Start monitoring and visualization"""
        print("\n📊 Starting Monitoring System...")
        
        # Start navigation monitor (optional - requires matplotlib)
        try:
            monitor_cmd = "python3 navigation_monitor.py"
            self.run_command(monitor_cmd, "Starting navigation monitor", 1)
            print("✅ Visual monitoring available!")
        except:
            print("⚠️ Visual monitoring not available (matplotlib required)")
            
    def show_system_info(self):
        """Show system information and instructions"""
        print("\n" + "="*70)
        print("🎉 INDOOR NAVIGATION SYSTEM IS READY!")
        print("="*70)
        print("🏠 Environment: 4-room house with furniture and objects")
        print("🤖 Robot: Enhanced small car with lidar sensor")
        print("🧭 Navigation: A* pathfinding with obstacle avoidance")
        print("")
        print("🎮 CONTROL MODES:")
        print("   MANUAL MODE (Default):")
        print("     • W/Z - Forward    • S - Backward")
        print("     • A/Q - Turn Left  • D - Turn Right")
        print("     • X - Stop         • ESC - Quit")
        print("")
        print("   AUTO MODE:")
        print("     • Type 'auto' to switch to autonomous navigation")
        print("     • Type room names: kitchen, bedroom, living_room, office")
        print("     • Type 'manual' to return to manual control")
        print("")
        print("📍 AVAILABLE ROOMS:")
        print("   • kitchen      - Top-left (green)")
        print("   • bedroom      - Top-right (purple)")
        print("   • living_room  - Bottom-left (yellow)")
        print("   • office       - Bottom-right (blue)")
        print("")
        print("📋 USEFUL COMMANDS:")
        print("   • 'status' - Show current status")
        print("   • 'rooms'  - List available rooms")
        print("   • 'stop'   - Stop current navigation")
        print("   • 'help'   - Show help information")
        print("")
        print("📡 ROS2 TOPICS:")
        print("   • /small_car/cmd_vel - Movement commands")
        print("   • /small_car/scan - Lidar data")
        print("   • /navigation_status - Navigation status")
        print("   • /navigation_path - Planned path")
        print("")
        print("="*70)
        print("🚗 Ready to navigate! The car is in the hallway.")
        print("🎮 Use the dual control interface to drive manually or autonomously!")
        print("="*70)
        
    def cleanup(self):
        """Clean up all processes"""
        print("\n🧹 Cleaning up processes...")
        for process, description in self.processes:
            try:
                process.terminate()
                print(f"🛑 Stopped {description}")
            except:
                pass
                
        # Wait a bit for graceful shutdown
        time.sleep(2)
        
        # Force kill if needed
        for process, description in self.processes:
            try:
                if process.poll() is None:
                    process.kill()
            except:
                pass
                
    def run(self):
        """Run the complete indoor navigation system"""
        try:
            # Check dependencies
            if not self.check_dependencies():
                return
                
            # Start simulation
            self.start_simulation()
            
            # Start navigation system
            self.start_navigation_system()
            
            # Start monitoring (optional)
            self.start_monitoring()
            
            # Show system info
            self.show_system_info()
            
            # Keep running
            print("\n🔄 System running... Press Ctrl+C to stop")
            while True:
                time.sleep(1)
                # Check if any critical process died
                for process, description in self.processes[:3]:  # Check first 3 critical processes
                    if process.poll() is not None:
                        print(f"❌ Critical process '{description}' died. Restarting system...")
                        return
                        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")
        finally:
            self.cleanup()

def main():
    launcher = IndoorNavigationLauncher()
    launcher.run()

if __name__ == '__main__':
    main()

#!/usr/bin/env python3

import subprocess
import sys
import termios
import tty
import select
import os
import time

class SimpleKeyboardControl:
    def __init__(self):
        # Setup keyboard
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
        # Movement speeds
        self.linear_speed = 2.0
        self.angular_speed = 1.5
        
        # Workspace path
        self.workspace = "/home/<USER>/box_gazebo_ws"
        
        self.print_instructions()
        
    def print_instructions(self):
        print("\n" + "="*60)
        print("🎮 SIMPLE KEYBOARD CAR CONTROL")
        print("="*60)
        print("🚗 Press these keys to control your car:")
        print("")
        print("   W or Z  - Move Forward")
        print("   S       - Move Backward") 
        print("   A or Q  - Turn Left")
        print("   D       - Turn Right")
        print("   X       - Stop")
        print("   ESC     - Quit")
        print("")
        print("💡 Press and release keys for movement")
        print("="*60)
        print("🚗 Ready! Start pressing keys...")
        print("")
        
    def send_command(self, linear_x, angular_z):
        """Send movement command to the car"""
        cmd = f'cd {self.workspace} && source install/setup.bash && ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{{linear: {{x: {linear_x}, y: 0.0, z: 0.0}}, angular: {{x: 0.0, y: 0.0, z: {angular_z}}}}}"'
        subprocess.run(cmd, shell=True, executable='/bin/bash', 
                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    def run(self):
        try:
            while True:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1)
                    
                    if ord(key) == 27:  # ESC key
                        print("\n👋 Goodbye!")
                        self.send_command(0.0, 0.0)  # Stop car
                        break
                        
                    key_lower = key.lower()
                    
                    if key_lower in ['w', 'z']:
                        print("🔼 Forward")
                        self.send_command(self.linear_speed, 0.0)
                        
                    elif key_lower == 's':
                        print("🔽 Backward")
                        self.send_command(-self.linear_speed, 0.0)
                        
                    elif key_lower in ['a', 'q']:
                        print("◀️ Left")
                        self.send_command(0.0, self.angular_speed)
                        
                    elif key_lower == 'd':
                        print("▶️ Right")
                        self.send_command(0.0, -self.angular_speed)
                        
                    elif key_lower == 'x' or key == ' ':
                        print("⏹️ Stop")
                        self.send_command(0.0, 0.0)
                        
        except KeyboardInterrupt:
            print("\n🛑 Interrupted")
        finally:
            # Restore terminal settings
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
            # Stop the car
            self.send_command(0.0, 0.0)

def main():
    controller = SimpleKeyboardControl()
    controller.run()

if __name__ == '__main__':
    main()

<?xml version="1.0" ?>
<sdf version="1.6">
  <world name="hospital_simulation">
    
    <!-- Physics settings optimized for hospital environment -->
    <physics name="default_physics" default="0" type="ode">
      <gravity>0 0 -9.8066</gravity>
      <ode>
        <solver>
          <type>quick</type>
          <iters>150</iters>
          <sor>1.3</sor>
        </solver>
        <constraints>
          <cfm>0.00001</cfm>
          <erp>0.2</erp>
          <contact_max_correcting_vel>1000</contact_max_correcting_vel>
          <contact_surface_layer>0.01</contact_surface_layer>
        </constraints>
      </ode>
      <max_step_size>0.004</max_step_size>
      <real_time_factor>1.0</real_time_factor>
      <real_time_update_rate>250</real_time_update_rate>
    </physics>

    <!-- Scene settings for hospital environment -->
    <scene>
      <ambient>0.8 0.8 0.8 1</ambient>
      <background>0.95 0.95 0.95 1</background>
      <shadows>true</shadows>
      <grid>false</grid>
    </scene>

    <!-- Lighting for hospital environment -->
    <include>
      <uri>model://sun</uri>
    </include>

    <!-- Additional hospital lighting -->
    <light name="hospital_light_1" type="point">
      <pose>0 0 4 0 0 0</pose>
      <diffuse>0.9 0.9 0.9 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>false</cast_shadows>
    </light>

    <light name="hospital_light_2" type="point">
      <pose>15 15 4 0 0 0</pose>
      <diffuse>0.9 0.9 0.9 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>false</cast_shadows>
    </light>

    <light name="hospital_light_3" type="point">
      <pose>-15 15 4 0 0 0</pose>
      <diffuse>0.9 0.9 0.9 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>false</cast_shadows>
    </light>

    <light name="hospital_light_4" type="point">
      <pose>15 -15 4 0 0 0</pose>
      <diffuse>0.9 0.9 0.9 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>false</cast_shadows>
    </light>

    <light name="hospital_light_5" type="point">
      <pose>-15 -15 4 0 0 0</pose>
      <diffuse>0.9 0.9 0.9 1</diffuse>
      <specular>0.1 0.1 0.1 1</specular>
      <attenuation>
        <range>20</range>
        <constant>0.5</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <cast_shadows>false</cast_shadows>
    </light>

    <!-- Ground plane -->
    <include>
      <uri>model://ground_plane</uri>
    </include>

    <!-- HOSPITAL BUILDING STRUCTURE -->
    
    <!-- Exterior Walls -->
    <!-- North Wall -->
    <model name="north_wall">
      <pose>0 25 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- South Wall -->
    <model name="south_wall">
      <pose>0 -25 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>50 0.3 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- East Wall -->
    <model name="east_wall">
      <pose>25 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- West Wall -->
    <model name="west_wall">
      <pose>-25 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.85 1</ambient>
            <diffuse>0.9 0.9 0.85 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.3 50 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- MAIN CORRIDOR WALLS -->
    
    <!-- Main Horizontal Corridor Wall (North) -->
    <model name="main_corridor_wall_north">
      <pose>0 8 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Main Horizontal Corridor Wall (South) -->
    <model name="main_corridor_wall_south">
      <pose>0 -8 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>40 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Main Vertical Corridor Wall (East) -->
    <model name="main_corridor_wall_east">
      <pose>8 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Main Vertical Corridor Wall (West) -->
    <model name="main_corridor_wall_west">
      <pose>-8 0 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
            <diffuse>0.95 0.95 0.9 1</diffuse>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 16 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- EMERGENCY ROOM (North-West: -16, 16) -->

    <!-- Emergency Room Walls -->
    <model name="er_wall_north">
      <pose>-16 20 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="er_wall_south">
      <pose>-16 12 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="er_wall_east">
      <pose>-8 16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="er_wall_west">
      <pose>-24 16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Emergency Room Furniture -->
    <include>
      <uri>model://table</uri>
      <pose>-18 18 0 0 0 0</pose>
      <name>er_examination_table_1</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-14 18 0 0 0 1.57</pose>
      <name>er_examination_table_2</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-22 19 0 0 0 0</pose>
      <name>er_medical_cabinet_1</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-10 19 0 0 0 1.57</pose>
      <name>er_medical_cabinet_2</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-20 14 0 0 0 0</pose>
      <name>er_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-16 14 0 0 0 0</pose>
      <name>er_chair_2</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-12 14 0 0 0 0</pose>
      <name>er_chair_3</name>
    </include>

    <!-- Emergency Room Staff -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-15 16 0 0 0 0</pose>
      <name>er_doctor</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-19 15 0 0 0 1.57</pose>
      <name>er_nurse</name>
    </include>

    <!-- Emergency Room Equipment -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>-22 15 0 0 0 0</pose>
      <name>er_medical_supplies_1</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>-10 15 0 0 0 0</pose>
      <name>er_medical_supplies_2</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>-18 18.3 0.8 0 0 0</pose>
      <name>er_medical_bowl_1</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>-14 18.3 0.8 0 0 0</pose>
      <name>er_medical_bowl_2</name>
    </include>

    <!-- OPERATING ROOM 1 (North-East: 16, 16) -->

    <!-- Operating Room 1 Walls -->
    <model name="or1_wall_north">
      <pose>16 20 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or1_wall_south">
      <pose>16 12 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or1_wall_east">
      <pose>24 16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or1_wall_west">
      <pose>8 16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Operating Room 1 Furniture -->
    <include>
      <uri>model://table_marble</uri>
      <pose>16 16 0 0 0 0</pose>
      <name>or1_operating_table</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>22 18 0 0 0 0</pose>
      <name>or1_instrument_cabinet_1</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>10 18 0 0 0 1.57</pose>
      <name>or1_instrument_cabinet_2</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>20 14 0 0 0 0</pose>
      <name>or1_instrument_table</name>
    </include>

    <!-- Operating Room 1 Staff -->
    <include>
      <uri>model://person_standing</uri>
      <pose>14 18 0 0 0 -1.57</pose>
      <name>or1_surgeon</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>18 18 0 0 0 -1.57</pose>
      <name>or1_nurse</name>
    </include>

    <!-- Operating Room 1 Equipment -->
    <include>
      <uri>model://bowl</uri>
      <pose>20 14.3 0.8 0 0 0</pose>
      <name>or1_surgical_bowl</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>12 14 0 0 0 0</pose>
      <name>or1_medical_supplies</name>
    </include>

    <!-- OPERATING ROOM 2 (South-East: 16, -16) -->

    <!-- Operating Room 2 Walls -->
    <model name="or2_wall_north">
      <pose>16 -12 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or2_wall_south">
      <pose>16 -20 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or2_wall_east">
      <pose>24 -16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="or2_wall_west">
      <pose>8 -16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.9 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Operating Room 2 Furniture -->
    <include>
      <uri>model://table_marble</uri>
      <pose>16 -16 0 0 0 0</pose>
      <name>or2_operating_table</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>22 -14 0 0 0 0</pose>
      <name>or2_instrument_cabinet_1</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>10 -14 0 0 0 1.57</pose>
      <name>or2_instrument_cabinet_2</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>20 -18 0 0 0 0</pose>
      <name>or2_instrument_table</name>
    </include>

    <!-- Operating Room 2 Staff -->
    <include>
      <uri>model://person_standing</uri>
      <pose>14 -14 0 0 0 1.57</pose>
      <name>or2_surgeon</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>18 -14 0 0 0 1.57</pose>
      <name>or2_nurse</name>
    </include>

    <!-- Operating Room 2 Equipment -->
    <include>
      <uri>model://bowl</uri>
      <pose>20 -17.7 0.8 0 0 0</pose>
      <name>or2_surgical_bowl</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>12 -18 0 0 0 0</pose>
      <name>or2_medical_supplies</name>
    </include>

    <!-- PATIENT ROOM 1 (West Side: -16, 4) -->

    <!-- Patient Room 1 Walls -->
    <model name="pr1_wall_north">
      <pose>-16 6 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr1_wall_south">
      <pose>-16 2 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr1_wall_east">
      <pose>-8 4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr1_wall_west">
      <pose>-24 4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Patient Room 1 Furniture -->
    <include>
      <uri>model://table_marble</uri>
      <pose>-18 4 0 0 0 0</pose>
      <name>pr1_hospital_bed</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-14 5 0 0 0 0</pose>
      <name>pr1_visitor_chair</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-22 5 0 0 0 0</pose>
      <name>pr1_bedside_table</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-22 3 0 0 0 1.57</pose>
      <name>pr1_medical_cabinet</name>
    </include>

    <!-- Patient Room 1 People -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-16 5 0 0 0 0</pose>
      <name>pr1_patient</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-14 3 0 0 0 1.57</pose>
      <name>pr1_visitor</name>
    </include>

    <!-- Patient Room 1 Equipment -->
    <include>
      <uri>model://bowl</uri>
      <pose>-22 5.3 0.8 0 0 0</pose>
      <name>pr1_water_bowl</name>
    </include>

    <include>
      <uri>model://coke_can</uri>
      <pose>-21.7 5.3 0.8 0 0 0</pose>
      <name>pr1_medicine_bottle</name>
    </include>

    <!-- PATIENT ROOM 2 (West Side: -16, -4) -->

    <!-- Patient Room 2 Walls -->
    <model name="pr2_wall_north">
      <pose>-16 -2 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr2_wall_south">
      <pose>-16 -6 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr2_wall_east">
      <pose>-8 -4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pr2_wall_west">
      <pose>-24 -4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.9 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Patient Room 2 Furniture -->
    <include>
      <uri>model://table_marble</uri>
      <pose>-18 -4 0 0 0 0</pose>
      <name>pr2_hospital_bed</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-14 -3 0 0 0 0</pose>
      <name>pr2_visitor_chair</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-22 -3 0 0 0 0</pose>
      <name>pr2_bedside_table</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-22 -5 0 0 0 1.57</pose>
      <name>pr2_medical_cabinet</name>
    </include>

    <!-- Patient Room 2 People -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-16 -3 0 0 0 0</pose>
      <name>pr2_patient</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-14 -5 0 0 0 -1.57</pose>
      <name>pr2_visitor</name>
    </include>

    <!-- Patient Room 2 Equipment -->
    <include>
      <uri>model://bowl</uri>
      <pose>-22 -2.7 0.8 0 0 0</pose>
      <name>pr2_water_bowl</name>
    </include>

    <include>
      <uri>model://coke_can</uri>
      <pose>-21.7 -2.7 0.8 0 0 0</pose>
      <name>pr2_medicine_bottle</name>
    </include>

    <!-- WAITING AREA (East Side: 16, 4) -->

    <!-- Waiting Area Walls -->
    <model name="wa_wall_north">
      <pose>16 6 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="wa_wall_south">
      <pose>16 2 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="wa_wall_east">
      <pose>24 4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="wa_wall_west">
      <pose>8 4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.95 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Waiting Area Furniture -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>12 4 0 0 0 1.57</pose>
      <name>wa_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>14 4 0 0 0 1.57</pose>
      <name>wa_chair_2</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>16 4 0 0 0 1.57</pose>
      <name>wa_chair_3</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>18 4 0 0 0 1.57</pose>
      <name>wa_chair_4</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>20 4 0 0 0 1.57</pose>
      <name>wa_chair_5</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>22 4 0 0 0 1.57</pose>
      <name>wa_chair_6</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>17 5 0 0 0 0</pose>
      <name>wa_coffee_table</name>
    </include>

    <!-- Waiting Area People -->
    <include>
      <uri>model://person_standing</uri>
      <pose>13 3 0 0 0 0</pose>
      <name>wa_patient_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>19 3 0 0 0 0</pose>
      <name>wa_patient_2</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>21 5 0 0 0 1.57</pose>
      <name>wa_visitor</name>
    </include>

    <!-- Waiting Area Items -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>10 5 0 0 0 0</pose>
      <name>wa_magazine_rack</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>17 5.3 0.8 0 0 0</pose>
      <name>wa_water_dispenser_cup</name>
    </include>

    <!-- RECEPTION/NURSE STATION (East Side: 16, -4) -->

    <!-- Reception Walls -->
    <model name="rec_wall_north">
      <pose>16 -2 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="rec_wall_south">
      <pose>16 -6 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="rec_wall_east">
      <pose>24 -4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="rec_wall_west">
      <pose>8 -4 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.95 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 4 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Reception Furniture -->
    <include>
      <uri>model://table</uri>
      <pose>16 -4 0 0 0 0</pose>
      <name>rec_reception_desk</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>16 -3 0 0 0 0</pose>
      <name>rec_receptionist_chair</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>22 -3 0 0 0 0</pose>
      <name>rec_filing_cabinet_1</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>22 -5 0 0 0 0</pose>
      <name>rec_filing_cabinet_2</name>
    </include>

    <include>
      <uri>model://bookshelf</uri>
      <pose>10 -5 0 0 0 1.57</pose>
      <name>rec_medical_records</name>
    </include>

    <!-- Reception Staff -->
    <include>
      <uri>model://person_standing</uri>
      <pose>15 -4 0 0 0 1.57</pose>
      <name>rec_receptionist</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>20 -4 0 0 0 -1.57</pose>
      <name>rec_nurse</name>
    </include>

    <!-- Reception Equipment -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>12 -3 0 0 0 0</pose>
      <name>rec_supplies</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>16 -3.7 0.8 0 0 0</pose>
      <name>rec_pen_holder</name>
    </include>

    <include>
      <uri>model://coke_can</uri>
      <pose>15.7 -3.7 0.8 0 0 0</pose>
      <name>rec_coffee_cup</name>
    </include>

    <!-- PHARMACY (South-West: -16, -16) -->

    <!-- Pharmacy Walls -->
    <model name="pharm_wall_north">
      <pose>-16 -12 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pharm_wall_south">
      <pose>-16 -20 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>16 0.2 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pharm_wall_east">
      <pose>-8 -16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <model name="pharm_wall_west">
      <pose>-24 -16 2 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <visual name="visual">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
          <material>
            <ambient>0.9 0.95 0.9 1</ambient>
          </material>
        </visual>
        <collision name="collision">
          <geometry>
            <box>
              <size>0.2 8 4</size>
            </box>
          </geometry>
        </collision>
      </link>
    </model>

    <!-- Pharmacy Furniture -->
    <include>
      <uri>model://bookshelf</uri>
      <pose>-22 -14 0 0 0 0</pose>
      <name>pharm_medicine_shelf_1</name>
    </include>

    <include>
      <uri>model://bookshelf</uri>
      <pose>-22 -18 0 0 0 0</pose>
      <name>pharm_medicine_shelf_2</name>
    </include>

    <include>
      <uri>model://bookshelf</uri>
      <pose>-10 -14 0 0 0 1.57</pose>
      <name>pharm_medicine_shelf_3</name>
    </include>

    <include>
      <uri>model://table</uri>
      <pose>-16 -16 0 0 0 0</pose>
      <name>pharm_counter</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-16 -15 0 0 0 0</pose>
      <name>pharm_pharmacist_chair</name>
    </include>

    <include>
      <uri>model://cabinet</uri>
      <pose>-14 -18 0 0 0 0</pose>
      <name>pharm_storage_cabinet</name>
    </include>

    <!-- Pharmacy Staff -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-15 -16 0 0 0 1.57</pose>
      <name>pharm_pharmacist</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-18 -14 0 0 0 0</pose>
      <name>pharm_customer</name>
    </include>

    <!-- Pharmacy Equipment -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>-20 -18 0 0 0 0</pose>
      <name>pharm_medicine_box_1</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>-12 -18 0 0 0 0</pose>
      <name>pharm_medicine_box_2</name>
    </include>

    <include>
      <uri>model://bowl</uri>
      <pose>-16 -15.7 0.8 0 0 0</pose>
      <name>pharm_pill_counter</name>
    </include>

    <include>
      <uri>model://coke_can</uri>
      <pose>-15.7 -15.7 0.8 0 0 0</pose>
      <name>pharm_medicine_bottle</name>
    </include>

    <!-- MAIN CORRIDOR FURNITURE AND PEOPLE -->

    <!-- Corridor Seating -->
    <include>
      <uri>model://cafe_table</uri>
      <pose>-4 0 0 0 0 1.57</pose>
      <name>corridor_chair_1</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>-2 0 0 0 0 1.57</pose>
      <name>corridor_chair_2</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>2 0 0 0 0 -1.57</pose>
      <name>corridor_chair_3</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>4 0 0 0 0 -1.57</pose>
      <name>corridor_chair_4</name>
    </include>

    <!-- Corridor Information Desk -->
    <include>
      <uri>model://table</uri>
      <pose>0 -4 0 0 0 0</pose>
      <name>corridor_info_desk</name>
    </include>

    <include>
      <uri>model://cafe_table</uri>
      <pose>0 -3 0 0 0 0</pose>
      <name>corridor_info_chair</name>
    </include>

    <!-- Corridor People -->
    <include>
      <uri>model://person_standing</uri>
      <pose>-3 2 0 0 0 0</pose>
      <name>corridor_visitor_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>3 2 0 0 0 3.14</pose>
      <name>corridor_visitor_2</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>0 -2 0 0 0 1.57</pose>
      <name>corridor_info_staff</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>-1 6 0 0 0 -1.57</pose>
      <name>corridor_doctor_1</name>
    </include>

    <include>
      <uri>model://person_standing</uri>
      <pose>1 -6 0 0 0 1.57</pose>
      <name>corridor_nurse_1</name>
    </include>

    <!-- Corridor Equipment and Supplies -->
    <include>
      <uri>model://cardboard_box</uri>
      <pose>-6 6 0 0 0 0</pose>
      <name>corridor_supply_box_1</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>6 6 0 0 0 0</pose>
      <name>corridor_supply_box_2</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>-6 -6 0 0 0 0</pose>
      <name>corridor_supply_box_3</name>
    </include>

    <include>
      <uri>model://cardboard_box</uri>
      <pose>6 -6 0 0 0 0</pose>
      <name>corridor_supply_box_4</name>
    </include>

    <!-- Corridor Amenities -->
    <include>
      <uri>model://bowl</uri>
      <pose>0 -3.7 0.8 0 0 0</pose>
      <name>corridor_water_cup</name>
    </include>

    <include>
      <uri>model://wood_cube_5cm</uri>
      <pose>-5 0 0 0 0 0</pose>
      <name>corridor_medical_cart_1</name>
    </include>

    <include>
      <uri>model://wood_cube_7_5cm</uri>
      <pose>5 0 0 0 0 0</pose>
      <name>corridor_medical_cart_2</name>
    </include>

  </world>
</sdf>

# 🔧 Hospital Simulation Modification Guide

Learn how to customize and extend the hospital simulation environment to meet your specific research needs.

## 🎯 **Overview**

This guide covers:
- Adding new rooms and departments
- Modifying existing room layouts
- Adding furniture and medical equipment
- Integrating new Gazebo models
- Customizing lighting and physics
- Adding interactive elements

## 🏗️ **Understanding the World File Structure**

The main hospital world is defined in `src/hospital_gazebo/worlds/hospital_simulation.world`. The structure follows this pattern:

```xml
<sdf version="1.6">
  <world name="hospital_simulation">
    <!-- Physics and lighting settings -->
    <!-- Building structure (walls) -->
    <!-- Room-specific furniture and equipment -->
    <!-- People and interactive objects -->
  </world>
</sdf>
```

## 🏠 **Adding New Rooms**

### **Step 1: Plan the Room Layout**

First, decide on the room's:
- **Location**: Choose coordinates that fit the hospital layout
- **Size**: Determine dimensions (recommended: 8m x 8m)
- **Purpose**: Medical function (lab, ICU, radiology, etc.)
- **Equipment**: Required furniture and medical devices

### **Step 2: Add Room Walls**

Add walls to the world file. Example for a new Laboratory at `(0, 16)`:

```xml
<!-- LABORATORY (North-Center: 0, 16) -->

<!-- Laboratory Walls -->
<model name="lab_wall_north">
  <pose>0 20 2 0 0 0</pose>
  <static>true</static>
  <link name="link">
    <visual name="visual">
      <geometry>
        <box>
          <size>8 0.2 4</size>
        </box>
      </geometry>
      <material>
        <ambient>0.9 0.9 0.95 1</ambient>
      </material>
    </visual>
    <collision name="collision">
      <geometry>
        <box>
          <size>8 0.2 4</size>
        </box>
      </geometry>
    </collision>
  </link>
</model>

<!-- Repeat for south, east, west walls -->
```

### **Step 3: Add Room Furniture**

Add appropriate furniture using Gazebo models:

```xml
<!-- Laboratory Equipment -->
<include>
  <uri>model://table</uri>
  <pose>0 18 0 0 0 0</pose>
  <name>lab_work_table</name>
</include>

<include>
  <uri>model://cabinet</uri>
  <pose>-3 19 0 0 0 0</pose>
  <name>lab_storage_cabinet</name>
</include>

<include>
  <uri>model://bookshelf</uri>
  <pose>3 19 0 0 0 1.57</pose>
  <name>lab_equipment_shelf</name>
</include>
```

## 🪑 **Modifying Existing Rooms**

### **Changing Room Size**

To resize a room, modify the wall dimensions and positions:

```xml
<!-- Original wall -->
<size>16 0.2 4</size>

<!-- Larger wall -->
<size>20 0.2 4</size>
```

### **Adding Furniture to Existing Rooms**

Find the room section in the world file and add new furniture:

```xml
<!-- Add to Emergency Room -->
<include>
  <uri>model://table</uri>
  <pose>-20 17 0 0 0 0</pose>
  <name>er_additional_table</name>
</include>
```

### **Removing Furniture**

Simply delete or comment out the furniture block:

```xml
<!-- Remove this equipment -->
<!--
<include>
  <uri>model://cabinet</uri>
  <pose>-22 19 0 0 0 0</pose>
  <name>er_medical_cabinet_1</name>
</include>
-->
```

## 🎨 **Customizing Appearance**

### **Changing Wall Colors**

Modify the material ambient values:

```xml
<material>
  <ambient>0.9 0.9 0.95 1</ambient>  <!-- Light blue -->
  <!-- OR -->
  <ambient>0.95 0.9 0.9 1</ambient>  <!-- Light pink -->
  <!-- OR -->
  <ambient>0.9 0.95 0.9 1</ambient>  <!-- Light green -->
</material>
```

### **Adding Room Labels**

Create visual labels for rooms:

```xml
<model name="lab_label">
  <pose>0 16 3 0 0 0</pose>
  <static>true</static>
  <link name="link">
    <visual name="visual">
      <geometry>
        <box>
          <size>2 0.1 0.5</size>
        </box>
      </geometry>
      <material>
        <ambient>0.2 0.8 0.2 1</ambient>
      </material>
    </visual>
  </link>
</model>
```

## 💡 **Lighting Customization**

### **Adding Room-Specific Lighting**

```xml
<light name="lab_light" type="point">
  <pose>0 16 3 0 0 0</pose>
  <diffuse>0.9 0.9 1.0 1</diffuse>
  <specular>0.1 0.1 0.1 1</specular>
  <attenuation>
    <range>15</range>
    <constant>0.5</constant>
    <linear>0.01</linear>
    <quadratic>0.001</quadratic>
  </attenuation>
  <cast_shadows>false</cast_shadows>
</light>
```

### **Adjusting Ambient Lighting**

Modify the scene settings:

```xml
<scene>
  <ambient>0.8 0.8 0.8 1</ambient>  <!-- Brighter -->
  <background>0.95 0.95 0.95 1</background>
  <shadows>true</shadows>
</scene>
```

## 🤖 **Adding Interactive Elements**

### **Adding People**

```xml
<include>
  <uri>model://person_standing</uri>
  <pose>0 15 0 0 0 0</pose>
  <name>lab_technician</name>
</include>
```

### **Adding Medical Equipment**

```xml
<!-- Medical cart -->
<include>
  <uri>model://wood_cube_5cm</uri>
  <pose>-2 16 0 0 0 0</pose>
  <name>lab_equipment_cart</name>
</include>

<!-- Medical supplies -->
<include>
  <uri>model://cardboard_box</uri>
  <pose>2 17 0 0 0 0</pose>
  <name>lab_supply_box</name>
</include>
```

## 🔧 **Advanced Modifications**

### **Creating Custom Models**

If you need custom furniture, create a new model directory:

```bash
mkdir -p ~/hospital_simulation_ws/gazebo_models-master/custom_hospital_bed
```

Create `model.config`:
```xml
<?xml version="1.0"?>
<model>
  <name>custom_hospital_bed</name>
  <version>1.0</version>
  <sdf version="1.6">model.sdf</sdf>
  <description>Custom hospital bed model</description>
</model>
```

Create `model.sdf` with your custom geometry.

### **Adding Sensors**

Add lidar or camera sensors to the environment:

```xml
<model name="security_camera">
  <pose>0 0 3 0 0.5 0</pose>
  <static>true</static>
  <link name="link">
    <sensor name="camera" type="camera">
      <camera>
        <horizontal_fov>1.047</horizontal_fov>
        <image>
          <width>640</width>
          <height>480</height>
        </image>
        <clip>
          <near>0.1</near>
          <far>100</far>
        </clip>
      </camera>
      <always_on>1</always_on>
      <update_rate>30</update_rate>
    </sensor>
  </link>
</model>
```

### **Physics Customization**

Modify physics settings for specific needs:

```xml
<physics name="default_physics" default="0" type="ode">
  <gravity>0 0 -9.8066</gravity>
  <ode>
    <solver>
      <type>quick</type>
      <iters>150</iters>
      <sor>1.3</sor>
    </solver>
  </ode>
  <max_step_size>0.001</max_step_size>  <!-- Higher precision -->
  <real_time_factor>1.0</real_time_factor>
</physics>
```

## 📐 **Coordinate System Guidelines**

### **Room Placement Rules**
- **X-axis**: East (+) / West (-)
- **Y-axis**: North (+) / South (-)
- **Z-axis**: Up (+) / Down (-)
- **Room centers**: Use multiples of 8 for easy navigation
- **Corridor width**: Maintain 4m minimum for robot navigation

### **Recommended Coordinates for New Rooms**
```
Available locations for 8x8m rooms:
- North wing: (0, 16), (-32, 16), (32, 16)
- South wing: (0, -16), (-32, -16), (32, -16)
- East wing: (32, 0), (32, 8), (32, -8)
- West wing: (-32, 0), (-32, 8), (-32, -8)
```

## 🧪 **Testing Your Modifications**

### **Step 1: Validate World File**
```bash
# Check for XML syntax errors
xmllint --noout src/hospital_gazebo/worlds/hospital_simulation.world
```

### **Step 2: Test in Gazebo**
```bash
# Launch with your modifications
roslaunch hospital_gazebo hospital_simulation.launch
```

### **Step 3: Check Collision Detection**
```bash
# Enable wireframe mode in Gazebo to see collision boundaries
# View -> Wireframe
```

## 📋 **Modification Checklist**

Before finalizing changes:

- [ ] **XML Syntax**: Validate world file syntax
- [ ] **Coordinates**: Verify all positions are reasonable
- [ ] **Collisions**: Test that walls and furniture have proper collision detection
- [ ] **Lighting**: Ensure adequate lighting in new areas
- [ ] **Navigation**: Verify robot navigation paths are clear
- [ ] **Performance**: Test that modifications don't impact simulation performance
- [ ] **Documentation**: Update coordinate lists and room descriptions

## 🎯 **Common Modification Examples**

### **Adding an ICU**
```xml
<!-- ICU at (32, 0) -->
<!-- Add walls, beds, monitoring equipment, medical staff -->
```

### **Creating a Radiology Department**
```xml
<!-- Radiology at (-32, 0) -->
<!-- Add imaging equipment, control room, patient preparation area -->
```

### **Expanding the Pharmacy**
```xml
<!-- Larger pharmacy with more shelves and storage -->
<!-- Add consultation room, prescription pickup area -->
```

---

**🏥 With these modification techniques, you can create a hospital simulation perfectly tailored to your research needs!**

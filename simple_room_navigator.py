#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import String
import math
import time

class SimpleRoomNavigator(Node):
    def __init__(self):
        super().__init__('simple_room_navigator')
        
        # Publisher for movement commands
        self.cmd_vel_pub = self.create_publisher(Twist, '/small_car/cmd_vel', 10)
        
        # Room positions (center coordinates)
        self.rooms = {
            'kitchen': (-4.0, 4.0),
            'bedroom': (4.0, 4.0),
            'living_room': (-4.0, -4.0),
            'office': (4.0, -4.0),
            'hallway': (0.0, 0.0)
        }
        
        # Current position (starting in hallway)
        self.current_position = [0.0, 0.0]
        self.current_angle = 0.0
        
        # Movement parameters
        self.linear_speed = 1.5
        self.angular_speed = 1.0
        self.goal_tolerance = 1.0
        
        self.get_logger().info('🏠 Simple Room Navigator started!')
        self.get_logger().info('📍 Available rooms: kitchen, bedroom, living_room, office')
        
    def navigate_to_room(self, room_name):
        """Navigate to a specific room using simple point-to-point movement"""
        if room_name not in self.rooms:
            self.get_logger().error(f"Unknown room: {room_name}")
            return False
            
        target_x, target_y = self.rooms[room_name]
        self.get_logger().info(f"🎯 Navigating to {room_name} at ({target_x}, {target_y})")
        
        # Calculate distance and angle to target
        dx = target_x - self.current_position[0]
        dy = target_y - self.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        target_angle = math.atan2(dy, dx)
        
        # Turn towards target
        self.turn_to_angle(target_angle)
        
        # Move towards target
        self.move_distance(distance)
        
        # Update current position
        self.current_position[0] = target_x
        self.current_position[1] = target_y
        
        self.get_logger().info(f"✅ Arrived at {room_name}!")
        return True
        
    def turn_to_angle(self, target_angle):
        """Turn to face a specific angle"""
        angle_diff = target_angle - self.current_angle
        
        # Normalize angle difference
        while angle_diff > math.pi:
            angle_diff -= 2 * math.pi
        while angle_diff < -math.pi:
            angle_diff += 2 * math.pi
            
        if abs(angle_diff) < 0.1:  # Already facing the right direction
            return
            
        self.get_logger().info(f"🔄 Turning {math.degrees(angle_diff):.1f} degrees")
        
        # Calculate turn time
        turn_time = abs(angle_diff) / self.angular_speed
        
        # Create turn command
        cmd = Twist()
        cmd.angular.z = self.angular_speed if angle_diff > 0 else -self.angular_speed
        
        # Execute turn
        start_time = time.time()
        while time.time() - start_time < turn_time:
            self.cmd_vel_pub.publish(cmd)
            time.sleep(0.1)
            
        # Stop turning
        cmd.angular.z = 0.0
        self.cmd_vel_pub.publish(cmd)
        
        # Update current angle
        self.current_angle = target_angle
        
    def move_distance(self, distance):
        """Move forward a specific distance"""
        if distance < 0.1:  # Already at target
            return
            
        self.get_logger().info(f"🔼 Moving forward {distance:.1f} meters")
        
        # Calculate move time
        move_time = distance / self.linear_speed
        
        # Create move command
        cmd = Twist()
        cmd.linear.x = self.linear_speed
        
        # Execute move
        start_time = time.time()
        while time.time() - start_time < move_time:
            self.cmd_vel_pub.publish(cmd)
            time.sleep(0.1)
            
        # Stop moving
        cmd.linear.x = 0.0
        self.cmd_vel_pub.publish(cmd)
        
    def stop_robot(self):
        """Stop the robot"""
        cmd = Twist()
        self.cmd_vel_pub.publish(cmd)
        
    def demo_navigation(self):
        """Demonstrate navigation to all rooms"""
        self.get_logger().info("🎬 Starting navigation demo...")
        
        demo_sequence = ['kitchen', 'bedroom', 'office', 'living_room', 'hallway']
        
        for room in demo_sequence:
            self.get_logger().info(f"📍 Next destination: {room}")
            time.sleep(2)  # Pause between movements
            self.navigate_to_room(room)
            time.sleep(3)  # Pause at each room
            
        self.get_logger().info("🎉 Demo completed!")
        
    def interactive_mode(self):
        """Interactive mode for manual room selection"""
        self.get_logger().info("🎮 Interactive mode started!")
        print("\n" + "="*50)
        print("🏠 SIMPLE ROOM NAVIGATOR")
        print("="*50)
        print("Available commands:")
        print("  kitchen      - Go to kitchen")
        print("  bedroom      - Go to bedroom")
        print("  living_room  - Go to living room")
        print("  office       - Go to office")
        print("  hallway      - Go to hallway")
        print("  demo         - Run demo sequence")
        print("  stop         - Stop robot")
        print("  quit         - Exit program")
        print("="*50)
        
        while rclpy.ok():
            try:
                command = input("\nEnter command: ").strip().lower()
                
                if command == 'quit':
                    break
                elif command == 'demo':
                    self.demo_navigation()
                elif command == 'stop':
                    self.stop_robot()
                    print("🛑 Robot stopped")
                elif command in self.rooms:
                    self.navigate_to_room(command)
                elif command == 'help':
                    print("Available rooms:", ", ".join(self.rooms.keys()))
                else:
                    print(f"❌ Unknown command: {command}")
                    print("Type 'help' for available commands")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
                
        self.stop_robot()
        self.get_logger().info("👋 Goodbye!")

def main(args=None):
    rclpy.init(args=args)
    
    navigator = SimpleRoomNavigator()
    
    try:
        # Check command line arguments
        import sys
        if len(sys.argv) > 1:
            if sys.argv[1] == 'demo':
                navigator.demo_navigation()
            elif sys.argv[1] in navigator.rooms:
                navigator.navigate_to_room(sys.argv[1])
            else:
                print(f"Usage: {sys.argv[0]} [room_name|demo]")
                print(f"Available rooms: {', '.join(navigator.rooms.keys())}")
        else:
            # Interactive mode
            navigator.interactive_mode()
            
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    finally:
        navigator.stop_robot()
        navigator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

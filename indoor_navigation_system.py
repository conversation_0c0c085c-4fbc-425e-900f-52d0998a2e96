#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, PoseStamped, Point
from sensor_msgs.msg import LaserScan
from nav_msgs.msg import OccupancyGrid, Path
from std_msgs.msg import String, Bool
import numpy as np
import math
import heapq
import threading
import time
from collections import deque

class IndoorNavigationSystem(Node):
    def __init__(self):
        super().__init__('indoor_navigation_system')
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/small_car/cmd_vel', 10)
        self.path_pub = self.create_publisher(Path, '/navigation_path', 10)
        self.status_pub = self.create_publisher(String, '/navigation_status', 10)
        self.map_pub = self.create_publisher(OccupancyGrid, '/occupancy_grid', 10)
        
        # Subscribers
        self.scan_sub = self.create_subscription(LaserScan, '/small_car/scan', self.scan_callback, 10)
        self.goal_sub = self.create_subscription(String, '/navigation_goal', self.goal_callback, 10)
        self.mode_sub = self.create_subscription(Bool, '/manual_mode', self.mode_callback, 10)
        
        # Room definitions (center coordinates)
        self.rooms = {
            'kitchen': (-4.0, 4.0),
            'bedroom': (4.0, 4.0),
            'living_room': (-4.0, -4.0),
            'office': (4.0, -4.0),
            'hallway': (0.0, 0.0)
        }
        
        # Navigation parameters
        self.current_position = [-2.0, -2.0]  # Start in clear hallway area
        self.current_goal = None
        self.path = []
        self.path_index = 0
        self.manual_mode = False
        self.navigation_active = False
        self.stuck_counter = 0
        self.max_stuck_count = 50
        
        # Map parameters
        self.map_width = 160  # 16m / 0.1m resolution
        self.map_height = 160  # 16m / 0.1m resolution
        self.map_resolution = 0.1  # 10cm per cell
        self.map_origin_x = -8.0
        self.map_origin_y = -8.0
        self.occupancy_grid = np.zeros((self.map_height, self.map_width), dtype=np.int8)
        
        # Movement parameters
        self.linear_speed = 1.0
        self.angular_speed = 1.0
        self.goal_tolerance = 0.5
        self.obstacle_threshold = 0.8  # meters
        
        # Initialize static map with walls
        self.initialize_static_map()
        
        # Control timer
        self.control_timer = self.create_timer(0.1, self.control_loop)
        
        # Map publishing timer
        self.map_timer = self.create_timer(1.0, self.publish_map)
        
        self.get_logger().info('🏠 Indoor Navigation System started!')
        self.get_logger().info('📍 Available rooms: kitchen, bedroom, living_room, office')
        
    def initialize_static_map(self):
        """Initialize the occupancy grid with known walls and furniture"""
        # Mark walls as occupied (100 = occupied)
        
        # Exterior walls
        self.occupancy_grid[0:5, :] = 100  # North wall
        self.occupancy_grid[-5:, :] = 100  # South wall
        self.occupancy_grid[:, 0:5] = 100  # West wall
        self.occupancy_grid[:, -5:] = 100  # East wall
        
        # Interior walls (simplified)
        center_x = self.map_width // 2
        center_y = self.map_height // 2
        
        # Central walls with doorways
        self.occupancy_grid[center_y-2:center_y+2, center_x-30:center_x-10] = 100
        self.occupancy_grid[center_y-2:center_y+2, center_x+10:center_x+30] = 100
        self.occupancy_grid[center_y-30:center_y-10, center_x-2:center_x+2] = 100
        self.occupancy_grid[center_y+10:center_y+30, center_x-2:center_x+2] = 100
        
        # Add furniture as obstacles (simplified)
        self.add_furniture_to_map()
        
    def add_furniture_to_map(self):
        """Add furniture positions to the occupancy grid"""
        furniture_positions = [
            # Kitchen furniture
            (-5.0, 5.0, 1.5, 1.0),  # Kitchen table
            (-6.5, 6.5, 2.0, 1.0),  # Kitchen cabinet
            
            # Bedroom furniture
            (5.5, 6.0, 2.0, 1.5),   # Bed
            (3.5, 6.5, 1.0, 0.5),   # Dresser
            
            # Living room furniture
            (-5.5, -5.5, 1.0, 2.5), # Sofa
            (-4.0, -5.5, 1.2, 0.8), # Coffee table
            
            # Office furniture
            (5.5, -5.5, 1.5, 0.8),  # Desk
            (6.5, -6.5, 1.0, 0.3),  # Bookshelf
        ]
        
        for x, y, width, height in furniture_positions:
            map_x = int((x - self.map_origin_x) / self.map_resolution)
            map_y = int((y - self.map_origin_y) / self.map_resolution)
            w_cells = int(width / self.map_resolution)
            h_cells = int(height / self.map_resolution)
            
            # Mark furniture area as occupied
            x_start = max(0, map_x - w_cells//2)
            x_end = min(self.map_width, map_x + w_cells//2)
            y_start = max(0, map_y - h_cells//2)
            y_end = min(self.map_height, map_y + h_cells//2)
            
            self.occupancy_grid[y_start:y_end, x_start:x_end] = 100
    
    def scan_callback(self, msg):
        """Process lidar scan data for dynamic obstacle detection"""
        if not self.navigation_active:
            return
            
        # Simple obstacle detection
        min_distance = min(msg.ranges)
        if min_distance < self.obstacle_threshold:
            self.stuck_counter += 1
        else:
            self.stuck_counter = 0
            
        # Update current position estimate (simplified)
        # In a real system, this would use odometry or SLAM
        
    def goal_callback(self, msg):
        """Handle navigation goal commands"""
        command = msg.data.lower().strip()
        
        if command in self.rooms:
            self.set_navigation_goal(command)
        elif command == 'stop':
            self.stop_navigation()
        elif command == 'list':
            self.list_rooms()
        else:
            self.get_logger().warn(f"Unknown command: {command}")
            
    def mode_callback(self, msg):
        """Handle manual/auto mode switching"""
        self.manual_mode = msg.data
        if self.manual_mode:
            self.stop_navigation()
            self.get_logger().info("🎮 Switched to MANUAL mode")
        else:
            self.get_logger().info("🤖 Switched to AUTO mode")
            
    def set_navigation_goal(self, room_name):
        """Set navigation goal to a specific room"""
        if room_name not in self.rooms:
            self.get_logger().error(f"Unknown room: {room_name}")
            return
            
        goal_x, goal_y = self.rooms[room_name]
        self.current_goal = (goal_x, goal_y)
        
        # Plan path using A*
        self.path = self.plan_path(self.current_position, [goal_x, goal_y])
        
        if self.path:
            self.path_index = 0
            self.navigation_active = True
            self.stuck_counter = 0
            self.publish_status(f"Navigating to {room_name}")
            self.publish_path()
            self.get_logger().info(f"🎯 Navigating to {room_name} at ({goal_x:.1f}, {goal_y:.1f})")
        else:
            self.publish_status(f"Failed to plan path to {room_name}")
            self.get_logger().error(f"❌ Failed to plan path to {room_name}")
            
    def plan_path(self, start, goal):
        """A* pathfinding algorithm"""
        start_cell = self.world_to_grid(start[0], start[1])
        goal_cell = self.world_to_grid(goal[0], goal[1])
        
        if not self.is_valid_cell(start_cell[0], start_cell[1]) or \
           not self.is_valid_cell(goal_cell[0], goal_cell[1]):
            return []
            
        # A* algorithm
        open_set = [(0, start_cell)]
        came_from = {}
        g_score = {start_cell: 0}
        f_score = {start_cell: self.heuristic(start_cell, goal_cell)}
        
        while open_set:
            current = heapq.heappop(open_set)[1]
            
            if current == goal_cell:
                # Reconstruct path
                path = []
                while current in came_from:
                    world_pos = self.grid_to_world(current[0], current[1])
                    path.append(world_pos)
                    current = came_from[current]
                path.reverse()
                return path
                
            for dx, dy in [(-1,0), (1,0), (0,-1), (0,1), (-1,-1), (-1,1), (1,-1), (1,1)]:
                neighbor = (current[0] + dx, current[1] + dy)
                
                if not self.is_valid_cell(neighbor[0], neighbor[1]):
                    continue
                    
                tentative_g = g_score[current] + math.sqrt(dx*dx + dy*dy)
                
                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score[neighbor] = tentative_g + self.heuristic(neighbor, goal_cell)
                    heapq.heappush(open_set, (f_score[neighbor], neighbor))
                    
        return []  # No path found
        
    def heuristic(self, a, b):
        """Euclidean distance heuristic"""
        return math.sqrt((a[0] - b[0])**2 + (a[1] - b[1])**2)
        
    def world_to_grid(self, x, y):
        """Convert world coordinates to grid coordinates"""
        grid_x = int((x - self.map_origin_x) / self.map_resolution)
        grid_y = int((y - self.map_origin_y) / self.map_resolution)
        return (grid_x, grid_y)
        
    def grid_to_world(self, grid_x, grid_y):
        """Convert grid coordinates to world coordinates"""
        x = grid_x * self.map_resolution + self.map_origin_x
        y = grid_y * self.map_resolution + self.map_origin_y
        return (x, y)
        
    def is_valid_cell(self, x, y):
        """Check if grid cell is valid and not occupied"""
        if x < 0 or x >= self.map_width or y < 0 or y >= self.map_height:
            return False
        return self.occupancy_grid[y, x] < 50  # Free space
        
    def control_loop(self):
        """Main control loop for navigation"""
        if self.manual_mode or not self.navigation_active:
            return
            
        if not self.path or self.path_index >= len(self.path):
            self.stop_navigation()
            return
            
        # Check if stuck
        if self.stuck_counter > self.max_stuck_count:
            self.handle_stuck_situation()
            return
            
        # Get current target waypoint
        target = self.path[self.path_index]
        
        # Calculate distance to target
        dx = target[0] - self.current_position[0]
        dy = target[1] - self.current_position[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # Check if reached current waypoint
        if distance < self.goal_tolerance:
            self.path_index += 1
            if self.path_index >= len(self.path):
                self.arrive_at_goal()
                return
            target = self.path[self.path_index]
            dx = target[0] - self.current_position[0]
            dy = target[1] - self.current_position[1]
            
        # Calculate control commands
        target_angle = math.atan2(dy, dx)
        current_angle = 0.0  # Simplified - would use odometry in real system
        
        angle_diff = target_angle - current_angle
        while angle_diff > math.pi:
            angle_diff -= 2 * math.pi
        while angle_diff < -math.pi:
            angle_diff += 2 * math.pi
            
        # Create and publish movement command
        cmd = Twist()
        
        if abs(angle_diff) > 0.2:  # Need to turn
            cmd.angular.z = self.angular_speed if angle_diff > 0 else -self.angular_speed
            cmd.linear.x = 0.0
        else:  # Move forward
            cmd.linear.x = self.linear_speed
            cmd.angular.z = angle_diff * 2.0  # Proportional turning
            
        self.cmd_vel_pub.publish(cmd)
        
        # Update position estimate (simplified)
        self.current_position[0] += cmd.linear.x * 0.1 * math.cos(current_angle)
        self.current_position[1] += cmd.linear.x * 0.1 * math.sin(current_angle)

    def handle_stuck_situation(self):
        """Handle when robot gets stuck"""
        self.get_logger().warn("🚫 Robot appears stuck, attempting recovery...")
        self.publish_status("Stuck - attempting recovery")

        # Simple recovery: reverse and try alternate path
        cmd = Twist()
        cmd.linear.x = -0.5  # Reverse
        self.cmd_vel_pub.publish(cmd)

        # Reset stuck counter and try to replan
        self.stuck_counter = 0
        if self.current_goal:
            goal_x, goal_y = self.current_goal
            self.path = self.plan_path(self.current_position, [goal_x, goal_y])
            if self.path:
                self.path_index = 0
                self.get_logger().info("🔄 Replanned path after getting stuck")
            else:
                self.stop_navigation()
                self.get_logger().error("❌ Failed to replan path")

    def arrive_at_goal(self):
        """Handle arrival at goal"""
        self.stop_navigation()
        room_name = self.get_current_room()
        self.publish_status(f"Arrived at {room_name}")
        self.get_logger().info(f"🎯 Arrived at {room_name}!")

    def stop_navigation(self):
        """Stop current navigation"""
        self.navigation_active = False
        self.current_goal = None
        self.path = []
        self.path_index = 0

        # Stop the robot
        cmd = Twist()
        self.cmd_vel_pub.publish(cmd)

        self.publish_status("Navigation stopped")

    def list_rooms(self):
        """List available rooms"""
        rooms_list = ", ".join(self.rooms.keys())
        self.get_logger().info(f"📍 Available rooms: {rooms_list}")
        self.publish_status(f"Available rooms: {rooms_list}")

    def get_current_room(self):
        """Determine which room the robot is currently in"""
        min_distance = float('inf')
        current_room = "unknown"

        for room_name, (room_x, room_y) in self.rooms.items():
            distance = math.sqrt((self.current_position[0] - room_x)**2 +
                               (self.current_position[1] - room_y)**2)
            if distance < min_distance:
                min_distance = distance
                current_room = room_name

        return current_room

    def publish_status(self, status):
        """Publish navigation status"""
        msg = String()
        msg.data = status
        self.status_pub.publish(msg)

    def publish_path(self):
        """Publish the planned path for visualization"""
        if not self.path:
            return

        path_msg = Path()
        path_msg.header.frame_id = "map"
        path_msg.header.stamp = self.get_clock().now().to_msg()

        for point in self.path:
            pose = PoseStamped()
            pose.header.frame_id = "map"
            pose.pose.position.x = point[0]
            pose.pose.position.y = point[1]
            pose.pose.position.z = 0.0
            pose.pose.orientation.w = 1.0
            path_msg.poses.append(pose)

        self.path_pub.publish(path_msg)

    def publish_map(self):
        """Publish the occupancy grid map"""
        map_msg = OccupancyGrid()
        map_msg.header.frame_id = "map"
        map_msg.header.stamp = self.get_clock().now().to_msg()

        map_msg.info.resolution = self.map_resolution
        map_msg.info.width = self.map_width
        map_msg.info.height = self.map_height
        map_msg.info.origin.position.x = self.map_origin_x
        map_msg.info.origin.position.y = self.map_origin_y
        map_msg.info.origin.position.z = 0.0
        map_msg.info.origin.orientation.w = 1.0

        # Flatten the occupancy grid
        map_msg.data = self.occupancy_grid.flatten().tolist()

        self.map_pub.publish(map_msg)

def main(args=None):
    rclpy.init(args=args)
    navigation_system = IndoorNavigationSystem()
    
    try:
        rclpy.spin(navigation_system)
    except KeyboardInterrupt:
        pass
    finally:
        navigation_system.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

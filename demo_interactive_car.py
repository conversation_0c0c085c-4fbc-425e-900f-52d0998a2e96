#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import <PERSON>ol
import time
import threading
import sys
import select
import termios
import tty

class InteractiveCarDemo(Node):
    def __init__(self):
        super().__init__('interactive_car_demo')
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/small_car/cmd_vel', 10)
        self.auto_mode_pub = self.create_publisher(Bool, '/small_car/auto_mode', 10)
        
        # Parameters
        self.linear_speed = 2.0
        self.angular_speed = 2.0
        
        # State
        self.auto_mode = False
        
        # Setup keyboard
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
        self.get_logger().info('🚗 Interactive Car Demo started!')
        self.print_instructions()
        
        # Start keyboard listener in separate thread
        self.keyboard_thread = threading.Thread(target=self.keyboard_listener)
        self.keyboard_thread.daemon = True
        self.keyboard_thread.start()
        
    def print_instructions(self):
        print("\n" + "="*50)
        print("🎮 INTERACTIVE CAR CONTROLS")
        print("="*50)
        print("Movement:")
        print("  W/Z - Forward")
        print("  S   - Backward") 
        print("  A/Q - Turn Left")
        print("  D   - Turn Right")
        print("  X   - Stop")
        print("")
        print("Modes:")
        print("  T   - Toggle Auto Mode")
        print("  ESC - Quit")
        print("="*50)
        print("🚗 Ready to drive! Current mode: MANUAL")
        print("")
        
    def keyboard_listener(self):
        while rclpy.ok():
            if select.select([sys.stdin], [], [], 0.1)[0]:
                key = sys.stdin.read(1)
                self.handle_key(key)
    
    def handle_key(self, key):
        cmd = Twist()
        
        if key.lower() in ['w', 'z']:
            cmd.linear.x = self.linear_speed
            cmd.angular.z = 0.0
            self.get_logger().info("🔼 Moving Forward")
            
        elif key.lower() == 's':
            cmd.linear.x = -self.linear_speed
            cmd.angular.z = 0.0
            self.get_logger().info("🔽 Moving Backward")
            
        elif key.lower() in ['a', 'q']:
            cmd.linear.x = 0.0
            cmd.angular.z = self.angular_speed
            self.get_logger().info("◀️ Turning Left")
            
        elif key.lower() == 'd':
            cmd.linear.x = 0.0
            cmd.angular.z = -self.angular_speed
            self.get_logger().info("▶️ Turning Right")
            
        elif key.lower() == 'x':
            cmd.linear.x = 0.0
            cmd.angular.z = 0.0
            self.get_logger().info("⏹️ Stopping")
            
        elif key.lower() == 't':
            self.toggle_auto_mode()
            return
            
        elif ord(key) == 27:  # ESC
            self.get_logger().info("👋 Goodbye!")
            rclpy.shutdown()
            return
            
        else:
            return  # Unknown key, don't publish
            
        # Publish command
        self.cmd_vel_pub.publish(cmd)
        
    def toggle_auto_mode(self):
        self.auto_mode = not self.auto_mode
        auto_msg = Bool()
        auto_msg.data = self.auto_mode
        self.auto_mode_pub.publish(auto_msg)
        
        mode_str = "AUTO" if self.auto_mode else "MANUAL"
        self.get_logger().info(f"🔄 Switched to {mode_str} mode")
        
    def __del__(self):
        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
        except:
            pass

def main(args=None):
    rclpy.init(args=args)
    
    demo = InteractiveCarDemo()
    
    try:
        rclpy.spin(demo)
    except KeyboardInterrupt:
        pass
    finally:
        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, demo.old_settings)
        except:
            pass
        
    demo.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()

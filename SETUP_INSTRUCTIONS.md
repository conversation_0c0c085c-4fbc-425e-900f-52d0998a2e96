# 🔧 Hospital Simulation Setup Instructions

Complete step-by-step guide to set up the hospital simulation environment on your system.

## 🖥️ **System Requirements**

### **Operating System**
- **Ubuntu 20.04 LTS** (Recommended)
- **Ubuntu 18.04 LTS** (Compatible)
- **Other Linux distributions** (May require additional configuration)

### **Hardware Requirements**
- **RAM**: Minimum 4GB, Recommended 8GB+
- **GPU**: Dedicated graphics card recommended for smooth Gazebo rendering
- **Storage**: 2GB free space for installation
- **CPU**: Multi-core processor recommended

## 🚀 **Installation Steps**

### **Step 1: Install ROS1 Noetic**

```bash
# Set up ROS repository
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'

# Add ROS key
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654

# Update package list
sudo apt update

# Install ROS Noetic Desktop Full
sudo apt install ros-noetic-desktop-full

# Initialize rosdep
sudo rosdep init
rosdep update

# Set up environment
echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### **Step 2: Install Dependencies**

```bash
# Install Gazebo packages
sudo apt install ros-noetic-gazebo-ros-pkgs
sudo apt install ros-noetic-gazebo-ros-control
sudo apt install ros-noetic-gazebo-plugins

# Install build tools
sudo apt install python3-catkin-tools
sudo apt install python3-rosdep
sudo apt install python3-rosinstall
sudo apt install python3-rosinstall-generator
sudo apt install python3-wstool

# Install additional dependencies
sudo apt install git
sudo apt install python3-pip
```

### **Step 3: Set Up Workspace**

```bash
# Create workspace directory
mkdir -p ~/hospital_simulation_ws/src
cd ~/hospital_simulation_ws

# Initialize catkin workspace
catkin_make

# Source the workspace
echo "source ~/hospital_simulation_ws/devel/setup.bash" >> ~/.bashrc
source ~/.bashrc
```

### **Step 4: Install Hospital Simulation**

```bash
# Copy the hospital_gazebo package to src directory
# (Assuming you have the hospital_simulation_ws folder)
cp -r /path/to/hospital_simulation_ws/src/hospital_gazebo ~/hospital_simulation_ws/src/

# Copy the Gazebo models
cp -r /path/to/hospital_simulation_ws/gazebo_models-master ~/hospital_simulation_ws/

# Copy test script
cp /path/to/hospital_simulation_ws/test_hospital_simulation.py ~/hospital_simulation_ws/
```

### **Step 5: Build the Project**

```bash
# Navigate to workspace
cd ~/hospital_simulation_ws

# Build the project
catkin_make

# Source the workspace
source devel/setup.bash
```

## ✅ **Verification**

### **Test 1: Check ROS Installation**
```bash
# Check ROS version
rosversion -d
# Should output: noetic

# Check if roscore works
roscore &
# Should start without errors
killall roscore
```

### **Test 2: Check Gazebo**
```bash
# Test Gazebo launch
gazebo --version
# Should show Gazebo version

# Test empty world
gazebo &
# Should open Gazebo GUI
killall gzserver gzclient
```

### **Test 3: Test Hospital Simulation**
```bash
# Navigate to workspace
cd ~/hospital_simulation_ws

# Test launch file
roslaunch hospital_gazebo hospital_simulation.launch
# Should open Gazebo with hospital environment

# Alternative: Use test script
python3 test_hospital_simulation.py
```

## 🔧 **Configuration**

### **Environment Variables**
Add these to your `~/.bashrc` for convenience:

```bash
# ROS environment
source /opt/ros/noetic/setup.bash
source ~/hospital_simulation_ws/devel/setup.bash

# Gazebo model path
export GAZEBO_MODEL_PATH=~/hospital_simulation_ws/gazebo_models-master:$GAZEBO_MODEL_PATH

# Workspace alias
alias hospital_ws='cd ~/hospital_simulation_ws'
alias hospital_launch='roslaunch hospital_gazebo hospital_simulation.launch'
```

### **Gazebo Configuration**
Create `~/.gazebo/gui.ini` for optimal settings:

```ini
[geometry]
x=0
y=0
width=1200
height=800

[rendering]
shadows=1
grid=0
```

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Issue 1: "Package 'hospital_gazebo' not found"**
```bash
# Solution: Check package path and rebuild
cd ~/hospital_simulation_ws
catkin_make
source devel/setup.bash
```

#### **Issue 2: "Gazebo models not loading"**
```bash
# Solution: Set Gazebo model path
export GAZEBO_MODEL_PATH=~/hospital_simulation_ws/gazebo_models-master:$GAZEBO_MODEL_PATH
```

#### **Issue 3: "roslaunch command not found"**
```bash
# Solution: Source ROS environment
source /opt/ros/noetic/setup.bash
```

#### **Issue 4: Gazebo crashes or runs slowly**
```bash
# Solution: Check graphics drivers and reduce quality
# For NVIDIA:
sudo apt install nvidia-driver-470

# For Intel/AMD:
sudo apt install mesa-utils
```

#### **Issue 5: "Permission denied" errors**
```bash
# Solution: Fix permissions
sudo chown -R $USER:$USER ~/hospital_simulation_ws
chmod +x ~/hospital_simulation_ws/test_hospital_simulation.py
```

### **Performance Optimization**

#### **For Low-End Systems:**
```bash
# Launch with reduced graphics
roslaunch hospital_gazebo hospital_simulation.launch gui:=false

# Or use headless mode
export DISPLAY=:0.0
roslaunch hospital_gazebo hospital_simulation.launch headless:=true
```

#### **For High-End Systems:**
```bash
# Enable all visual effects
roslaunch hospital_gazebo hospital_simulation.launch verbose:=true
```

## 📞 **Getting Help**

### **Log Files**
Check these locations for error logs:
- `~/.ros/log/` - ROS log files
- `/tmp/` - Gazebo temporary files
- Terminal output during launch

### **Useful Commands**
```bash
# Check ROS topics
rostopic list

# Check ROS nodes
rosnode list

# Check Gazebo models
gz model --list

# Monitor system resources
htop
```

### **Reset Environment**
If something goes wrong, reset with:
```bash
# Kill all ROS processes
killall roscore rosmaster gzserver gzclient

# Clean workspace
cd ~/hospital_simulation_ws
catkin_make clean
catkin_make

# Restart
source devel/setup.bash
```

## 🎯 **Next Steps**

After successful installation:

1. **Explore the Environment**: Launch the simulation and navigate through rooms
2. **Add Robots**: Integrate your robot models for navigation testing
3. **Customize Layout**: Modify rooms and add equipment as needed
4. **Develop Applications**: Build navigation, SLAM, or medical robotics applications

## 📚 **Additional Resources**

- **ROS Tutorials**: http://wiki.ros.org/ROS/Tutorials
- **Gazebo Tutorials**: http://gazebosim.org/tutorials
- **Hospital Simulation Documentation**: See `HOSPITAL_README.md`
- **Modification Guide**: See `MODIFICATION_GUIDE.md`

---

**🏥 Your hospital simulation environment should now be ready for medical robotics research!**

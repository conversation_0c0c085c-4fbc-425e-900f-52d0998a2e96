#!/usr/bin/env python3

"""
Hospital Simulation Test Script - ROS1
Launches the complete hospital environment for testing and development
"""

import subprocess
import time
import os
import signal
import sys

def signal_handler(sig, frame):
    print('\n🛑 Shutting down hospital simulation...')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

def main():
    workspace_dir = "/home/<USER>/hospital_simulation_ws"
    
    print("🏥 HOSPITAL SIMULATION TEST - ROS1")
    print("=" * 50)
    
    # Change to workspace directory
    os.chdir(workspace_dir)
    
    print("📦 Step 1: Building hospital simulation package...")
    build_cmd = "source /opt/ros/noetic/setup.bash && catkin_make"
    build_process = subprocess.Popen(build_cmd, shell=True, executable='/bin/bash')
    build_process.wait()
    
    if build_process.returncode != 0:
        print("❌ Build failed! Please check for errors.")
        return
    
    print("✅ Build successful!")
    
    print("\n📦 Step 2: Starting ROS Master...")
    roscore_cmd = "source /opt/ros/noetic/setup.bash && roscore"
    roscore_process = subprocess.Popen(roscore_cmd, shell=True, executable='/bin/bash')
    
    # Wait for roscore to start
    time.sleep(3)
    
    print("🌍 Step 3: Launching Hospital Simulation...")
    gazebo_cmd = f"""
    source /opt/ros/noetic/setup.bash && 
    source devel/setup.bash && 
    export GAZEBO_MODEL_PATH={workspace_dir}/gazebo_models-master:$GAZEBO_MODEL_PATH && 
    roslaunch hospital_gazebo hospital_simulation.launch
    """
    gazebo_process = subprocess.Popen(gazebo_cmd, shell=True, executable='/bin/bash')
    
    # Wait for Gazebo to start
    print("⏳ Waiting for hospital environment to load...")
    time.sleep(10)
    
    print("\n✅ Hospital Simulation Environment Ready!")
    print("\n🏥 HOSPITAL ENVIRONMENT FEATURES:")
    print("   • 🚨 Emergency Room with medical equipment")
    print("   • 🔬 2 Operating Rooms with surgical tables")
    print("   • 🛏️ 2 Patient Rooms with beds and visitors")
    print("   • 🪑 Waiting Area with comfortable seating")
    print("   • 📋 Reception/Nurse Station")
    print("   • 💊 Pharmacy with medicine shelves")
    print("   • 🚶 20+ people (staff, patients, visitors)")
    print("   • 🪑 50+ furniture pieces and medical equipment")
    
    print("\n🎯 NAVIGATION COORDINATES:")
    print("   Emergency Room:    (-16,  16)")
    print("   Operating Room 1:  ( 16,  16)")
    print("   Operating Room 2:  ( 16, -16)")
    print("   Patient Room 1:    (-16,   4)")
    print("   Patient Room 2:    (-16,  -4)")
    print("   Waiting Area:      ( 16,   4)")
    print("   Reception:         ( 16,  -4)")
    print("   Pharmacy:          (-16, -16)")
    print("   Main Corridor:     (  0,   0)")
    
    print("\n🚀 NEXT STEPS:")
    print("   1. Hospital environment is now running")
    print("   2. Add robots or navigation systems as needed")
    print("   3. Use coordinates above for navigation targets")
    print("   4. All rooms have proper collision detection")
    print("   5. Environment is ready for medical robotics research")
    
    print("\n📡 AVAILABLE ROS1 TOPICS:")
    print("   • /gazebo/model_states - Model positions")
    print("   • /gazebo/link_states - Link information")
    print("   • /clock - Simulation time")
    
    print("\n🔄 Simulation running... Press Ctrl+C to stop")
    
    try:
        # Keep the processes running
        gazebo_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping hospital simulation...")
        roscore_process.terminate()
        gazebo_process.terminate()
        
        print("👋 Hospital simulation stopped!")

if __name__ == '__main__':
    main()

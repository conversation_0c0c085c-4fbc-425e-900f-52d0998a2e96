================================================================================
🚗 INTERACTIVE SMALL CAR ROBOT PROJECT - HOW TO RUN
================================================================================

This project contains a small car robot that can be controlled with keyboard keys
in a Gazebo simulation environment with interactive objects (humans, trees, balls).

================================================================================
📋 REQUIREMENTS
================================================================================

- Ubuntu 20.04 or 22.04
- ROS2 (Humble, Galactic, or Foxy)
- Gazebo (comes with ROS2)
- Python 3

================================================================================
🚀 QUICK START - STEP BY STEP
================================================================================

1. OPEN TERMINAL 1 - Start the Simulation:
   
   cd /home/<USER>/box_gazebo_ws
   python3 start_car_simulation.py

   Wait until you see: "✅ Car simulation is ready!"

2. OPEN TERMINAL 2 - Start Keyboard Control:

   cd /home/<USER>/box_gazebo_ws
   python3 simple_keyboard_control.py

3. USE YOUR KEYBOARD TO CONTROL THE CAR:
   
   W or Z  = Move Forward
   S       = Move Backward
   A or Q  = Turn Left
   D       = Turn Right
   X       = Stop
   ESC     = Quit

================================================================================
🎮 ALTERNATIVE CONTROL METHODS
================================================================================

METHOD 1: Manual Commands (if keyboard doesn't work)
----------------------------------------------------
After starting simulation, use these commands in Terminal 2:

cd /home/<USER>/box_gazebo_ws
source install/setup.bash

# Forward:
ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{linear: {x: 2.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}"

# Backward:
ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{linear: {x: -2.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}"

# Turn Left:
ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 1.5}}"

# Turn Right:
ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: -1.5}}"

# Stop:
ros2 topic pub --once /small_car/cmd_vel geometry_msgs/msg/Twist "{linear: {x: 0.0, y: 0.0, z: 0.0}, angular: {x: 0.0, y: 0.0, z: 0.0}}"

METHOD 2: Automatic Demo
------------------------
cd /home/<USER>/box_gazebo_ws
source install/setup.bash
python3 simple_car_control.py

This will automatically demonstrate all car movements.

================================================================================
🌍 ENVIRONMENT OBJECTS
================================================================================

The simulation includes:
- 🌳 Trees (2 different types)
- 👥 Human figures (2 people)
- ⚽ Balls (red and blue, can be pushed)
- 📦 Boxes (obstacles)
- 🏠 Boundary walls (20m x 20m area)

================================================================================
🔧 BUILDING THE PROJECT (if needed)
================================================================================

If you need to rebuild the project:

cd /home/<USER>/box_gazebo_ws
colcon build --packages-select box_gazebo
source install/setup.bash

================================================================================
📡 USEFUL COMMANDS
================================================================================

Check if simulation is running:
ros2 topic list

Monitor car position:
ros2 topic echo /small_car/odom

View camera feed:
ros2 topic echo /small_car/front_camera/image_raw

================================================================================
🛠️ TROUBLESHOOTING
================================================================================

Problem: Keyboard control doesn't work
Solution: Make sure the terminal with keyboard control is active (clicked)

Problem: Car doesn't move
Solution: Check if simulation is running first, then try manual commands

Problem: Gazebo doesn't start
Solution: Close all terminals and restart from step 1

Problem: "No module named rclpy"
Solution: Make sure ROS2 is installed and sourced

================================================================================
📁 PROJECT FILES
================================================================================

Essential files in this project:
- start_car_simulation.py     - Starts Gazebo simulation
- simple_keyboard_control.py  - Keyboard control interface
- simple_car_control.py       - Automatic demo
- src/box_gazebo/urdf/small_car.urdf.xacro - Car robot model
- src/box_gazebo/worlds/interactive_world.world - Environment with objects
- gazebo_models-master/       - 3D models for objects

================================================================================
🎯 SUMMARY
================================================================================

1. Run: python3 start_car_simulation.py
2. Run: python3 simple_keyboard_control.py  
3. Use W/A/S/D keys to drive around
4. Press ESC to quit

Enjoy driving your small car robot! 🚗🎮

================================================================================

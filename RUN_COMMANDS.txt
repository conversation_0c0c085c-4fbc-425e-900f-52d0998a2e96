🏠 REALISTIC INDOOR NAVIGATION - RUN COMMANDS
=============================================

📦 STEP 1: START SIMULATION
---------------------------
cd /home/<USER>/box_gazebo_ws
python3 test_indoor_navigation.py

📍 STEP 2: NAVIGATE TO ROOMS (in new terminal)
----------------------------------------------
cd /home/<USER>/box_gazebo_ws
source install/setup.bash

# Navigate to specific rooms:
python3 simple_room_navigator.py kitchen
python3 simple_room_navigator.py bedroom
python3 simple_room_navigator.py living_room
python3 simple_room_navigator.py office

# Full house tour:
python3 simple_room_navigator.py demo

🔧 TROUBLESHOOTING
------------------
# If models don't load:
export GAZEBO_MODEL_PATH=/home/<USER>/box_gazebo_ws/gazebo_models-master:$GAZEBO_MODEL_PATH

# Rebuild if needed:
cd /home/<USER>/box_gazebo_ws
colcon build --packages-select box_gazebo
source install/setup.bash

📁 ESSENTIAL FILES
------------------
✅ src/box_gazebo/worlds/indoor_house_realistic.world  # Realistic world
✅ src/box_gazebo/urdf/small_car.urdf.xacro           # Enhanced robot
✅ simple_room_navigator.py                           # Navigation system
✅ test_indoor_navigation.py                          # Launch script
✅ gazebo_models-master/                              # Model library

🎯 FEATURES
-----------
✅ Realistic Gazebo models (tables, chairs, cabinets, people)
✅ 4-room house with proper furniture
✅ Enhanced robot physics with wheel friction
✅ Autonomous navigation between rooms
✅ Interactive objects and realistic environment
✅ Clean project structure with only essential files

🏠 ROOM COORDINATES
-------------------
Kitchen:     (-4,  4)  # Top-Left
Bedroom:     ( 4,  4)  # Top-Right
Living Room: (-4, -4)  # Bottom-Left
Office:      ( 4, -4)  # Bottom-Right

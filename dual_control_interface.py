#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import String, Bool
import sys
import termios
import tty
import select
import threading
import time

class DualControlInterface(Node):
    def __init__(self):
        super().__init__('dual_control_interface')
        
        # Publishers
        self.cmd_vel_pub = self.create_publisher(Twist, '/small_car/cmd_vel', 10)
        self.goal_pub = self.create_publisher(String, '/navigation_goal', 10)
        self.mode_pub = self.create_publisher(Bool, '/manual_mode', 10)
        
        # Subscribers
        self.status_sub = self.create_subscription(String, '/navigation_status', 
                                                 self.status_callback, 10)
        
        # Control parameters
        self.linear_speed = 1.5
        self.angular_speed = 1.5
        self.manual_mode = True
        
        # Available rooms
        self.rooms = ['kitchen', 'bedroom', 'living_room', 'office']
        
        # Setup keyboard
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
        # Current status
        self.current_status = "Ready"
        
        # Start keyboard listener
        self.running = True
        self.keyboard_thread = threading.Thread(target=self.keyboard_listener)
        self.keyboard_thread.daemon = True
        self.keyboard_thread.start()
        
        # Start command interface
        self.command_thread = threading.Thread(target=self.command_interface)
        self.command_thread.daemon = True
        self.command_thread.start()
        
        # Publish initial mode
        self.publish_mode()
        
        self.print_instructions()
        
    def print_instructions(self):
        print("\n" + "="*70)
        print("🏠 INDOOR NAVIGATION - DUAL CONTROL INTERFACE")
        print("="*70)
        print("🎮 MANUAL MODE (Current):")
        print("   W/Z - Move Forward    |  S - Move Backward")
        print("   A/Q - Turn Left       |  D - Turn Right")
        print("   X   - Stop            |  ESC - Quit")
        print("")
        print("🤖 AUTO MODE:")
        print("   Type 'auto' to switch to autonomous navigation")
        print("   Then type room names: kitchen, bedroom, living_room, office")
        print("   Type 'manual' to return to manual control")
        print("")
        print("📋 COMMANDS:")
        print("   'auto'     - Switch to autonomous mode")
        print("   'manual'   - Switch to manual mode")
        print("   'status'   - Show current status")
        print("   'rooms'    - List available rooms")
        print("   'stop'     - Stop current navigation")
        print("   'help'     - Show this help")
        print("   'quit'     - Exit program")
        print("")
        print("="*70)
        print(f"🎮 Current Mode: {'MANUAL' if self.manual_mode else 'AUTO'}")
        print(f"📍 Status: {self.current_status}")
        print("="*70)
        print("")
        
    def keyboard_listener(self):
        """Listen for keyboard input in manual mode"""
        while self.running and rclpy.ok():
            if self.manual_mode and select.select([sys.stdin], [], [], 0.1)[0]:
                key = sys.stdin.read(1)
                self.handle_manual_key(key)
                
    def command_interface(self):
        """Handle text commands for autonomous navigation"""
        while self.running and rclpy.ok():
            if not self.manual_mode:
                try:
                    # In auto mode, listen for text commands
                    print(f"\n🤖 AUTO MODE - Current: {self.current_status}")
                    print("Type room name (kitchen/bedroom/living_room/office) or command:")
                    print("> ", end="", flush=True)
                    
                    # Wait for input with timeout
                    if select.select([sys.stdin], [], [], 1.0)[0]:
                        command = input().strip().lower()
                        self.handle_auto_command(command)
                except:
                    pass
            time.sleep(0.5)
            
    def handle_manual_key(self, key):
        """Handle keyboard input for manual control"""
        if ord(key) == 27:  # ESC key
            print("\n👋 Goodbye!")
            self.running = False
            rclpy.shutdown()
            return
            
        cmd = Twist()
        key_lower = key.lower()
        
        if key_lower in ['w', 'z']:
            cmd.linear.x = self.linear_speed
            print("🔼 Forward", end="\r")
            
        elif key_lower == 's':
            cmd.linear.x = -self.linear_speed
            print("🔽 Backward", end="\r")
            
        elif key_lower in ['a', 'q']:
            cmd.angular.z = self.angular_speed
            print("◀️ Left", end="\r")
            
        elif key_lower == 'd':
            cmd.angular.z = -self.angular_speed
            print("▶️ Right", end="\r")
            
        elif key_lower == 'x' or key == ' ':
            cmd.linear.x = 0.0
            cmd.angular.z = 0.0
            print("⏹️ Stop", end="\r")
            
        else:
            return  # Unknown key
            
        self.cmd_vel_pub.publish(cmd)
        
    def handle_auto_command(self, command):
        """Handle text commands for autonomous navigation"""
        if command in self.rooms:
            self.send_navigation_goal(command)
            
        elif command == 'manual':
            self.switch_to_manual()
            
        elif command == 'stop':
            self.send_stop_command()
            
        elif command == 'status':
            print(f"📍 Current Status: {self.current_status}")
            
        elif command == 'rooms':
            print(f"📍 Available rooms: {', '.join(self.rooms)}")
            
        elif command == 'help':
            self.print_help()
            
        elif command == 'quit':
            print("\n👋 Goodbye!")
            self.running = False
            rclpy.shutdown()
            
        elif command == 'auto':
            print("🤖 Already in AUTO mode")
            
        else:
            print(f"❌ Unknown command: {command}")
            print("Type 'help' for available commands")
            
    def send_navigation_goal(self, room):
        """Send navigation goal to the navigation system"""
        msg = String()
        msg.data = room
        self.goal_pub.publish(msg)
        print(f"🎯 Navigating to {room}...")
        
    def send_stop_command(self):
        """Send stop command to navigation system"""
        msg = String()
        msg.data = 'stop'
        self.goal_pub.publish(msg)
        print("⏹️ Stopping navigation...")
        
    def switch_to_manual(self):
        """Switch to manual control mode"""
        self.manual_mode = True
        self.publish_mode()
        print("\n🎮 Switched to MANUAL mode")
        print("Use WASD keys to control the robot")
        print("Type 'auto' + Enter to switch back to autonomous mode")
        
    def switch_to_auto(self):
        """Switch to autonomous control mode"""
        self.manual_mode = False
        self.publish_mode()
        print("\n🤖 Switched to AUTO mode")
        
    def publish_mode(self):
        """Publish current control mode"""
        msg = Bool()
        msg.data = self.manual_mode
        self.mode_pub.publish(msg)
        
    def status_callback(self, msg):
        """Handle navigation status updates"""
        self.current_status = msg.data
        if not self.manual_mode:
            print(f"\n📍 Navigation Status: {self.current_status}")
            
    def print_help(self):
        """Print help information"""
        print("\n🤖 AUTO MODE COMMANDS:")
        print("  Room names: kitchen, bedroom, living_room, office")
        print("  manual  - Switch to manual control")
        print("  stop    - Stop current navigation")
        print("  status  - Show current status")
        print("  rooms   - List available rooms")
        print("  help    - Show this help")
        print("  quit    - Exit program")
        
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
        except:
            pass

def main(args=None):
    rclpy.init(args=args)
    
    interface = DualControlInterface()
    
    try:
        # Main loop to handle mode switching
        while interface.running and rclpy.ok():
            rclpy.spin_once(interface, timeout_sec=0.1)
            
            # Check for mode switch command in manual mode
            if interface.manual_mode and select.select([sys.stdin], [], [], 0.0)[0]:
                # Check if it's a text command (not single key)
                line = ""
                while select.select([sys.stdin], [], [], 0.0)[0]:
                    char = sys.stdin.read(1)
                    if char == '\n':
                        break
                    line += char
                    
                if line.strip().lower() == 'auto':
                    interface.switch_to_auto()
                elif line.strip().lower() == 'quit':
                    break
                elif line.strip().lower() == 'help':
                    interface.print_instructions()
                elif line.strip().lower() == 'status':
                    print(f"📍 Current Status: {interface.current_status}")
                elif line.strip().lower() == 'rooms':
                    print(f"📍 Available rooms: {', '.join(interface.rooms)}")
                    
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    finally:
        # Restore terminal settings
        try:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, interface.old_settings)
        except:
            pass
            
        # Stop the robot
        cmd = Twist()
        interface.cmd_vel_pub.publish(cmd)
        
        interface.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()

{"backtraceGraph": {"commands": ["install", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_environment_hooks", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 35, "parent": 0}, {"command": 0, "file": 0, "line": 44, "parent": 0}, {"command": 0, "file": 0, "line": 50, "parent": 0}, {"command": 0, "file": 0, "line": 56, "parent": 0}, {"command": 5, "file": 0, "line": 66, "parent": 0}, {"command": 4, "file": 4, "line": 66, "parent": 5}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 2, "parent": 7}, {"command": 2, "file": 2, "line": 47, "parent": 8}, {"command": 1, "file": 2, "line": 29, "parent": 9}, {"command": 0, "file": 1, "line": 105, "parent": 10}, {"command": 6, "file": 2, "line": 48, "parent": 8}, {"command": 1, "file": 2, "line": 43, "parent": 12}, {"command": 0, "file": 1, "line": 105, "parent": 13}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 6, "parent": 15}, {"command": 7, "file": 6, "line": 20, "parent": 16}, {"command": 0, "file": 5, "line": 70, "parent": 17}, {"command": 0, "file": 5, "line": 87, "parent": 17}, {"command": 0, "file": 5, "line": 70, "parent": 17}, {"command": 0, "file": 5, "line": 87, "parent": 17}, {"command": 8, "file": 6, "line": 26, "parent": 16}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 107, "parent": 22}, {"command": 0, "file": 7, "line": 119, "parent": 22}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 9, "parent": 28}, {"command": 9, "file": 9, "line": 16, "parent": 29}, {"command": 1, "file": 8, "line": 29, "parent": 30}, {"command": 0, "file": 1, "line": 105, "parent": 31}, {"command": 10, "file": 4, "line": 68, "parent": 5}, {"command": 0, "file": 4, "line": 150, "parent": 33}, {"command": 0, "file": 4, "line": 157, "parent": 33}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib/box_gazebo", "paths": ["circle_mover"], "targetId": "circle_mover::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib/box_gazebo", "paths": ["simple_car_driver"], "targetId": "simple_car_driver::@6890427a1f51a3e7e1df", "targetIndex": 4, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib/box_gazebo", "paths": ["keyboard_controller"], "targetId": "keyboard_controller::@6890427a1f51a3e7e1df", "targetIndex": 3, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib/box_gazebo", "paths": ["auto_mode"], "targetId": "auto_mode::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "share/box_gazebo/", "paths": ["launch"], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "share/box_gazebo/", "paths": ["urdf"], "type": "directory"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/box_gazebo/", "paths": ["worlds"], "type": "directory"}, {"backtrace": 11, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/box_gazebo"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/box_gazebo"], "type": "file"}, {"backtrace": 18, "component": "Unspecified", "destination": "share/box_gazebo/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "share/box_gazebo/environment", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "share/box_gazebo/environment", "paths": ["/opt/ros/humble/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "share/box_gazebo/environment", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 23, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 24, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 25, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 27, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_index/share/ament_index/resource_index/packages/box_gazebo"], "type": "file"}, {"backtrace": 34, "component": "Unspecified", "destination": "share/box_gazebo/cmake", "paths": ["/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_core/box_gazeboConfig.cmake", "/home/<USER>/box_gazebo_ws/build/box_gazebo/ament_cmake_core/box_gazeboConfig-version.cmake"], "type": "file"}, {"backtrace": 35, "component": "Unspecified", "destination": "share/box_gazebo", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}
{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-bcb9e26871c49158872e.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "box_gazebo", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "auto_mode::@6890427a1f51a3e7e1df", "jsonFile": "target-auto_mode-937bf8cd1ad7200d3d99.json", "name": "auto_mode", "projectIndex": 0}, {"directoryIndex": 0, "id": "box_gazebo_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-box_gazebo_uninstall-79ffd4d4022ec2a63f3e.json", "name": "box_gazebo_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "circle_mover::@6890427a1f51a3e7e1df", "jsonFile": "target-circle_mover-05df251bdd6e937bcf67.json", "name": "circle_mover", "projectIndex": 0}, {"directoryIndex": 0, "id": "keyboard_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-keyboard_controller-9ee844ab9ebb8f91d5e3.json", "name": "keyboard_controller", "projectIndex": 0}, {"directoryIndex": 0, "id": "simple_car_driver::@6890427a1f51a3e7e1df", "jsonFile": "target-simple_car_driver-e4a4452322a8041ab8f1.json", "name": "simple_car_driver", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-8d1b45d5c638d3ab3a48.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/box_gazebo_ws/build/box_gazebo", "source": "/home/<USER>/box_gazebo_ws/src/box_gazebo"}, "version": {"major": 2, "minor": 3}}
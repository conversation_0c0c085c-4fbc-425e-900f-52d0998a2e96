{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-0ea937c00a1d626e6f10.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "box_gazebo", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "box_gazebo_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-box_gazebo_uninstall-79ffd4d4022ec2a63f3e.json", "name": "box_gazebo_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "circle_mover::@6890427a1f51a3e7e1df", "jsonFile": "target-circle_mover-e65d945c339b2181963f.json", "name": "circle_mover", "projectIndex": 0}, {"directoryIndex": 0, "id": "simple_car_driver::@6890427a1f51a3e7e1df", "jsonFile": "target-simple_car_driver-7e7c14f8d1dd84f05b5d.json", "name": "simple_car_driver", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-8d1b45d5c638d3ab3a48.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/box_gazebo_ws/build/box_gazebo", "source": "/home/<USER>/box_gazebo_ws/src/box_gazebo"}, "version": {"major": 2, "minor": 3}}
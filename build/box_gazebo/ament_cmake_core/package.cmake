set(_AMENT_PACKAGE_NAME "box_gazebo")
set(box_gazebo_VERSION "0.0.0")
set(box_gazebo_MAINTAINER "Mehdi <<EMAIL>>")
set(box_gazebo_BUILD_DEPENDS "rclcpp" "gazebo_ros" "urdf" "xacro" "geometry_msgs" "sensor_msgs" "nav_msgs" "std_msgs" "robot_state_publisher" "joint_state_publisher_gui" "gazebo_plugins" "rviz2")
set(box_gazebo_BUILDTOOL_DEPENDS "ament_cmake")
set(box_gazebo_BUILD_EXPORT_DEPENDS "rclcpp" "gazebo_ros" "urdf" "xacro" "geometry_msgs" "sensor_msgs" "nav_msgs" "std_msgs" "robot_state_publisher" "joint_state_publisher_gui" "gazebo_plugins" "rviz2")
set(box_gazebo_BUILDTOOL_EXPORT_DEPENDS )
set(box_gazebo_EXEC_DEPENDS "rclcpp" "gazebo_ros" "urdf" "xacro" "geometry_msgs" "sensor_msgs" "nav_msgs" "std_msgs" "robot_state_publisher" "joint_state_publisher_gui" "gazebo_plugins" "rviz2")
set(box_gazebo_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(box_gazebo_GROUP_DEPENDS )
set(box_gazebo_MEMBER_OF_GROUPS )
set(box_gazebo_DEPRECATED "")
set(box_gazebo_EXPORT_TAGS)
list(APPEND box_gazebo_EXPORT_TAGS "<build_type>ament_cmake</build_type>")

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/box_gazebo_ws/src/box_gazebo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/box_gazebo_ws/build/box_gazebo

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/box_gazebo_ws/build/box_gazebo/CMakeFiles /home/<USER>/box_gazebo_ws/build/box_gazebo//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/box_gazebo_ws/build/box_gazebo/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named box_gazebo_uninstall

# Build rule for target.
box_gazebo_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 box_gazebo_uninstall
.PHONY : box_gazebo_uninstall

# fast build rule for target.
box_gazebo_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/box_gazebo_uninstall.dir/build.make CMakeFiles/box_gazebo_uninstall.dir/build
.PHONY : box_gazebo_uninstall/fast

#=============================================================================
# Target rules for targets named circle_mover

# Build rule for target.
circle_mover: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 circle_mover
.PHONY : circle_mover

# fast build rule for target.
circle_mover/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/circle_mover.dir/build.make CMakeFiles/circle_mover.dir/build
.PHONY : circle_mover/fast

#=============================================================================
# Target rules for targets named simple_car_driver

# Build rule for target.
simple_car_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 simple_car_driver
.PHONY : simple_car_driver

# fast build rule for target.
simple_car_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_car_driver.dir/build.make CMakeFiles/simple_car_driver.dir/build
.PHONY : simple_car_driver/fast

#=============================================================================
# Target rules for targets named keyboard_controller

# Build rule for target.
keyboard_controller: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 keyboard_controller
.PHONY : keyboard_controller

# fast build rule for target.
keyboard_controller/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keyboard_controller.dir/build.make CMakeFiles/keyboard_controller.dir/build
.PHONY : keyboard_controller/fast

#=============================================================================
# Target rules for targets named auto_mode

# Build rule for target.
auto_mode: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 auto_mode
.PHONY : auto_mode

# fast build rule for target.
auto_mode/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/auto_mode.dir/build.make CMakeFiles/auto_mode.dir/build
.PHONY : auto_mode/fast

src/auto_mode.o: src/auto_mode.cpp.o
.PHONY : src/auto_mode.o

# target to build an object file
src/auto_mode.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/auto_mode.dir/build.make CMakeFiles/auto_mode.dir/src/auto_mode.cpp.o
.PHONY : src/auto_mode.cpp.o

src/auto_mode.i: src/auto_mode.cpp.i
.PHONY : src/auto_mode.i

# target to preprocess a source file
src/auto_mode.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/auto_mode.dir/build.make CMakeFiles/auto_mode.dir/src/auto_mode.cpp.i
.PHONY : src/auto_mode.cpp.i

src/auto_mode.s: src/auto_mode.cpp.s
.PHONY : src/auto_mode.s

# target to generate assembly for a file
src/auto_mode.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/auto_mode.dir/build.make CMakeFiles/auto_mode.dir/src/auto_mode.cpp.s
.PHONY : src/auto_mode.cpp.s

src/circle_mover.o: src/circle_mover.cpp.o
.PHONY : src/circle_mover.o

# target to build an object file
src/circle_mover.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/circle_mover.dir/build.make CMakeFiles/circle_mover.dir/src/circle_mover.cpp.o
.PHONY : src/circle_mover.cpp.o

src/circle_mover.i: src/circle_mover.cpp.i
.PHONY : src/circle_mover.i

# target to preprocess a source file
src/circle_mover.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/circle_mover.dir/build.make CMakeFiles/circle_mover.dir/src/circle_mover.cpp.i
.PHONY : src/circle_mover.cpp.i

src/circle_mover.s: src/circle_mover.cpp.s
.PHONY : src/circle_mover.s

# target to generate assembly for a file
src/circle_mover.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/circle_mover.dir/build.make CMakeFiles/circle_mover.dir/src/circle_mover.cpp.s
.PHONY : src/circle_mover.cpp.s

src/keyboard_controller.o: src/keyboard_controller.cpp.o
.PHONY : src/keyboard_controller.o

# target to build an object file
src/keyboard_controller.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keyboard_controller.dir/build.make CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.o
.PHONY : src/keyboard_controller.cpp.o

src/keyboard_controller.i: src/keyboard_controller.cpp.i
.PHONY : src/keyboard_controller.i

# target to preprocess a source file
src/keyboard_controller.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keyboard_controller.dir/build.make CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.i
.PHONY : src/keyboard_controller.cpp.i

src/keyboard_controller.s: src/keyboard_controller.cpp.s
.PHONY : src/keyboard_controller.s

# target to generate assembly for a file
src/keyboard_controller.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keyboard_controller.dir/build.make CMakeFiles/keyboard_controller.dir/src/keyboard_controller.cpp.s
.PHONY : src/keyboard_controller.cpp.s

src/simple_car_driver.o: src/simple_car_driver.cpp.o
.PHONY : src/simple_car_driver.o

# target to build an object file
src/simple_car_driver.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_car_driver.dir/build.make CMakeFiles/simple_car_driver.dir/src/simple_car_driver.cpp.o
.PHONY : src/simple_car_driver.cpp.o

src/simple_car_driver.i: src/simple_car_driver.cpp.i
.PHONY : src/simple_car_driver.i

# target to preprocess a source file
src/simple_car_driver.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_car_driver.dir/build.make CMakeFiles/simple_car_driver.dir/src/simple_car_driver.cpp.i
.PHONY : src/simple_car_driver.cpp.i

src/simple_car_driver.s: src/simple_car_driver.cpp.s
.PHONY : src/simple_car_driver.s

# target to generate assembly for a file
src/simple_car_driver.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_car_driver.dir/build.make CMakeFiles/simple_car_driver.dir/src/simple_car_driver.cpp.s
.PHONY : src/simple_car_driver.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... box_gazebo_uninstall"
	@echo "... uninstall"
	@echo "... auto_mode"
	@echo "... circle_mover"
	@echo "... keyboard_controller"
	@echo "... simple_car_driver"
	@echo "... src/auto_mode.o"
	@echo "... src/auto_mode.i"
	@echo "... src/auto_mode.s"
	@echo "... src/circle_mover.o"
	@echo "... src/circle_mover.i"
	@echo "... src/circle_mover.s"
	@echo "... src/keyboard_controller.o"
	@echo "... src/keyboard_controller.i"
	@echo "... src/keyboard_controller.s"
	@echo "... src/simple_car_driver.o"
	@echo "... src/simple_car_driver.i"
	@echo "... src/simple_car_driver.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


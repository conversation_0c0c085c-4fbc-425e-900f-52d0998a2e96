/home/<USER>/box_gazebo_ws/install/box_gazebo/lib/box_gazebo/circle_mover
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//launch/spawn_box.launch.py
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.urdf.xacro
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/circle_bot.xacro
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo//urdf/simple_box_gazebo.urdf
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/package_run_dependencies/box_gazebo
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/parent_prefix_path/box_gazebo
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.sh
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/ament_prefix_path.dsv
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.sh
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/environment/path.dsv
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.bash
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.sh
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.zsh
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/local_setup.dsv
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.dsv
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/ament_index/resource_index/packages/box_gazebo
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig.cmake
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/cmake/box_gazeboConfig-version.cmake
/home/<USER>/box_gazebo_ws/install/box_gazebo/share/box_gazebo/package.xml

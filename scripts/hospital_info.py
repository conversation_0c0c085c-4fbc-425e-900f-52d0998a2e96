#!/usr/bin/env python

"""
Hospital Simulation Information Display
ROS1 Node for displaying hospital environment information
"""

import rospy
import time

class HospitalInfo:
    def __init__(self):
        rospy.init_node('hospital_info', anonymous=True)
        self.display_hospital_info()
    
    def display_hospital_info(self):
        """Display comprehensive hospital simulation information"""
        
        print("\n" + "="*80)
        print("🏥 HOSPITAL SIMULATION ENVIRONMENT - ROS1")
        print("="*80)
        
        print("\n🏗️ HOSPITAL LAYOUT:")
        print("   📍 Coordinates: 50m x 50m facility")
        print("   🚪 Main Corridor: Central cross-shaped hallway")
        print("   🏠 Total Rooms: 8 specialized medical areas")
        
        print("\n🏥 MEDICAL DEPARTMENTS:")
        print("   🚨 Emergency Room (North-West: -16, 16)")
        print("      • 2 examination tables with medical equipment")
        print("      • Medical cabinets and emergency supplies")
        print("      • Doctor and nurse staff")
        print("      • Patient seating area")
        
        print("\n   🔬 Operating Room 1 (North-East: 16, 16)")
        print("      • Marble operating table")
        print("      • Surgical instrument cabinets")
        print("      • Surgeon and nurse staff")
        print("      • Sterile equipment setup")
        
        print("\n   🔬 Operating Room 2 (South-East: 16, -16)")
        print("      • Secondary surgical suite")
        print("      • Complete surgical equipment")
        print("      • Medical staff and supplies")
        print("      • Backup operating facility")
        
        print("\n   🛏️ Patient Room 1 (West: -16, 4)")
        print("      • Hospital bed with bedside table")
        print("      • Visitor chair and medical cabinet")
        print("      • Patient and visitor")
        print("      • Medical supplies and water")
        
        print("\n   🛏️ Patient Room 2 (West: -16, -4)")
        print("      • Second patient accommodation")
        print("      • Complete patient care setup")
        print("      • Medical equipment and supplies")
        print("      • Patient and family area")
        
        print("\n   🪑 Waiting Area (East: 16, 4)")
        print("      • 6 comfortable waiting chairs")
        print("      • Coffee table with magazines")
        print("      • Water dispenser area")
        print("      • Patient and visitor seating")
        
        print("\n   📋 Reception/Nurse Station (East: 16, -4)")
        print("      • Reception desk with staff")
        print("      • Filing cabinets and medical records")
        print("      • Nurse station equipment")
        print("      • Patient check-in area")
        
        print("\n   💊 Pharmacy (South-West: -16, -16)")
        print("      • Medicine shelves and storage")
        print("      • Pharmacist counter")
        print("      • Prescription processing area")
        print("      • Medical supply storage")
        
        print("\n🚶 HOSPITAL STAFF & PATIENTS:")
        print("   👨‍⚕️ Medical Staff: 8 doctors and nurses")
        print("   🤒 Patients: 6 patients in various areas")
        print("   👥 Visitors: 4 family members and visitors")
        print("   📋 Support Staff: 2 reception/admin staff")
        
        print("\n🪑 FURNITURE & EQUIPMENT:")
        print("   🛏️ Hospital Beds: 3 marble tables (patient beds)")
        print("   🪑 Seating: 15 chairs and waiting area furniture")
        print("   🗄️ Storage: 12 cabinets and medical storage units")
        print("   📚 Shelving: 4 bookshelves for records and medicines")
        print("   🍽️ Tables: 8 tables for various medical purposes")
        
        print("\n📦 INTERACTIVE OBJECTS:")
        print("   📦 Supply Boxes: 12 cardboard boxes with medical supplies")
        print("   🥤 Medical Items: 8 bowls and containers")
        print("   💊 Medicine: 6 bottles and medical containers")
        print("   🧊 Equipment: 2 wooden medical carts")
        
        print("\n🌟 SPECIAL FEATURES:")
        print("   💡 Realistic Lighting: 5 hospital-grade light sources")
        print("   🎨 Color Coding: Different room colors for easy identification")
        print("   🏗️ Modular Design: Easy to modify and extend rooms")
        print("   🔧 Physics Ready: Proper collision detection for navigation")
        
        print("\n🎯 NAVIGATION COORDINATES:")
        print("   Emergency Room:    (-16,  16)")
        print("   Operating Room 1:  ( 16,  16)")
        print("   Operating Room 2:  ( 16, -16)")
        print("   Patient Room 1:    (-16,   4)")
        print("   Patient Room 2:    (-16,  -4)")
        print("   Waiting Area:      ( 16,   4)")
        print("   Reception:         ( 16,  -4)")
        print("   Pharmacy:          (-16, -16)")
        print("   Main Corridor:     (  0,   0)")
        
        print("\n🚀 USAGE INSTRUCTIONS:")
        print("   1. Environment is ready for robot navigation")
        print("   2. All rooms have proper collision detection")
        print("   3. Coordinate system: X=East/West, Y=North/South")
        print("   4. Room centers are optimal navigation targets")
        print("   5. Corridors provide clear pathways between rooms")
        
        print("\n🔧 TECHNICAL SPECIFICATIONS:")
        print("   📐 World Size: 50m x 50m")
        print("   🏠 Room Size: ~8m x 8m each")
        print("   🚪 Corridor Width: 4m")
        print("   📏 Wall Height: 4m")
        print("   ⚖️ Physics: ODE with realistic settings")
        
        print("\n✅ HOSPITAL SIMULATION READY!")
        print("   🏥 All medical departments operational")
        print("   👥 Staff and patients positioned")
        print("   🪑 Furniture and equipment placed")
        print("   🤖 Ready for robot navigation integration")
        
        print("\n" + "="*80)
        print("🏥 Hospital Environment Successfully Loaded!")
        print("="*80 + "\n")

def main():
    try:
        hospital_info = HospitalInfo()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()

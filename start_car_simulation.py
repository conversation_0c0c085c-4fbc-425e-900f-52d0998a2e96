#!/usr/bin/env python3

import subprocess
import time
import os
import signal
import sys

def signal_handler(sig, frame):
    print('\n👋 Shutting down simulation...')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

def main():
    print("🚗 Starting Interactive Car Simulation")
    print("=" * 40)
    
    # Change to workspace directory
    workspace_dir = "/home/<USER>/box_gazebo_ws"
    os.chdir(workspace_dir)
    
    # Source the workspace
    source_cmd = "source install/setup.bash"
    
    print("📦 Step 1: Starting Gazebo...")
    gazebo_cmd = f"{source_cmd} && ros2 launch gazebo_ros gazebo.launch.py world:=src/box_gazebo/worlds/interactive_world.world"
    gazebo_process = subprocess.Popen(gazebo_cmd, shell=True, executable='/bin/bash')
    
    # Wait for Gazebo to start
    print("⏳ Waiting for Gazebo to initialize...")
    time.sleep(5)
    
    print("🤖 Step 2: Publishing robot description...")
    robot_desc_cmd = f"{source_cmd} && ros2 run robot_state_publisher robot_state_publisher --ros-args -p robot_description:=\"$(xacro src/box_gazebo/urdf/small_car.urdf.xacro)\""
    robot_desc_process = subprocess.Popen(robot_desc_cmd, shell=True, executable='/bin/bash')
    
    time.sleep(2)
    
    print("🚀 Step 3: Spawning car in Gazebo...")
    spawn_cmd = f"{source_cmd} && ros2 run gazebo_ros spawn_entity.py -topic robot_description -entity small_car -x 0 -y 0 -z 0.2"
    spawn_process = subprocess.Popen(spawn_cmd, shell=True, executable='/bin/bash')
    
    # Wait for spawn to complete
    spawn_process.wait()
    
    print("✅ Car simulation is ready!")
    print("\n🎮 Now you can:")
    print("1. Run the interactive demo: python3 demo_interactive_car.py")
    print("2. Or control manually with: ros2 topic pub /small_car/cmd_vel geometry_msgs/msg/Twist ...")
    print("3. View topics with: ros2 topic list")
    print("\n📡 Available topics:")
    print("   /small_car/cmd_vel - Send movement commands")
    print("   /small_car/odom - Get robot position")
    print("   /small_car/front_camera/image_raw - Camera feed")
    
    print("\n🔄 Simulation running... Press Ctrl+C to stop")
    
    try:
        # Keep the processes running
        gazebo_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping simulation...")
        gazebo_process.terminate()
        robot_desc_process.terminate()
        
        # Wait for processes to finish
        gazebo_process.wait()
        robot_desc_process.wait()
        
        print("👋 Simulation stopped!")

if __name__ == '__main__':
    main()

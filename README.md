# 🚗 Interactive Small Car Robot

A ROS2 + Gazebo simulation of a small car robot that can be controlled with keyboard keys in an interactive environment with humans, trees, and balls.

## 🎮 Quick Start

1. **Start Simulation:**
   ```bash
   cd /home/<USER>/box_gazebo_ws
   python3 start_car_simulation.py
   ```

2. **Control with Keyboard:**
   ```bash
   python3 simple_keyboard_control.py
   ```

3. **Drive the Car:**
   - `W/Z` - Forward
   - `S` - Backward  
   - `A/Q` - Turn Left
   - `D` - Turn Right
   - `X` - Stop
   - `ESC` - Quit

## 🌍 Features

- **Small Car Robot** - Compact design with realistic physics
- **Interactive Environment** - Trees, humans, balls, and obstacles
- **Keyboard Control** - Real-time control with WASD/ZQSD keys
- **Automatic Demo** - Watch the car move automatically
- **Camera Feed** - First-person view from the car

## 📁 Files

- `HOW_TO_RUN.txt` - Detailed instructions
- `start_car_simulation.py` - Launch simulation
- `simple_keyboard_control.py` - Keyboard control
- `simple_car_control.py` - Automatic demo

## 🛠️ Requirements

- Ubuntu 20.04/22.04
- ROS2 (Humble/Galactic/Foxy)
- Gazebo
- Python 3

---
**For detailed instructions, see `HOW_TO_RUN.txt`**

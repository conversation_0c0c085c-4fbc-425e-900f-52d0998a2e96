# 🏥 Hospital Simulation Environment - ROS1

A comprehensive hospital simulation environment built for ROS1 (Noetic) with realistic medical facility layout, equipment, and staff for robotics research and development.

## 🎯 **Project Overview**

This project provides a complete hospital simulation environment featuring:
- **8 specialized medical departments** with realistic layouts
- **50+ furniture pieces** and medical equipment using Gazebo models
- **20+ people** including doctors, nurses, patients, and visitors
- **Modular design** for easy modification and extension
- **Navigation-ready** coordinate system for robot path planning

## 🏗️ **Hospital Layout**

```
                    HOSPITAL FLOOR PLAN (50m x 50m)
    
    (-25,25) ┌─────────────────────────────────────────────┐ (25,25)
             │                                             │
             │  🚨 EMERGENCY ROOM     🔬 OPERATING ROOM 1  │
             │     (-16, 16)             (16, 16)          │
             │                                             │
    (-25,8)  ├─────────────┬─────────────┬─────────────────┤ (25,8)
             │             │             │                 │
             │ 🛏️ PATIENT  │ 🚪 MAIN     │ 🪑 WAITING      │
             │   ROOM 1    │ CORRIDOR    │   AREA          │
             │  (-16, 4)   │   (0, 0)    │  (16, 4)        │
             │             │             │                 │
    (-25,-8) ├─────────────┼─────────────┼─────────────────┤ (25,-8)
             │             │             │                 │
             │ 🛏️ PATIENT  │ 🚪 MAIN     │ 📋 RECEPTION/   │
             │   ROOM 2    │ CORRIDOR    │   NURSE STATION │
             │  (-16, -4)  │   (0, 0)    │  (16, -4)       │
             │             │             │                 │
    (-25,-25)├─────────────┴─────────────┴─────────────────┤ (25,-25)
             │                                             │
             │  💊 PHARMACY           🔬 OPERATING ROOM 2  │
             │   (-16, -16)              (16, -16)         │
             │                                             │
    (-25,-25)└─────────────────────────────────────────────┘ (25,-25)
```

## 🚀 **Quick Start Guide**

### **Prerequisites**
```bash
# Install ROS1 Noetic (Ubuntu 20.04)
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654
sudo apt update
sudo apt install ros-noetic-desktop-full

# Install Gazebo and dependencies
sudo apt install ros-noetic-gazebo-ros-pkgs ros-noetic-gazebo-ros-control
sudo apt install python3-catkin-tools
```

### **Installation**
```bash
# Clone or copy the hospital_simulation_ws directory
cd ~/hospital_simulation_ws

# Build the project
source /opt/ros/noetic/setup.bash
catkin_make

# Source the workspace
source devel/setup.bash
```

### **Launch Hospital Simulation**
```bash
# Method 1: Using launch file
roslaunch hospital_gazebo hospital_simulation.launch

# Method 2: Using test script
python3 test_hospital_simulation.py
```

## 🎯 **Navigation Coordinates**

Perfect for robot navigation systems:

| Department | Coordinates | Description |
|------------|-------------|-------------|
| Emergency Room | `(-16, 16)` | Emergency medical care |
| Operating Room 1 | `(16, 16)` | Primary surgical suite |
| Operating Room 2 | `(16, -16)` | Secondary surgical suite |
| Patient Room 1 | `(-16, 4)` | Patient accommodation |
| Patient Room 2 | `(-16, -4)` | Patient accommodation |
| Waiting Area | `(16, 4)` | Patient waiting |
| Reception | `(16, -4)` | Check-in and administration |
| Pharmacy | `(-16, -16)` | Pharmaceutical services |
| Main Corridor | `(0, 0)` | Central navigation hub |

## 📁 **Project Structure**

```
hospital_simulation_ws/
├── src/
│   └── hospital_gazebo/
│       ├── package.xml              # ROS1 package configuration
│       ├── CMakeLists.txt           # Catkin build configuration
│       ├── worlds/
│       │   └── hospital_simulation.world  # Main hospital world file
│       ├── launch/
│       │   └── hospital_simulation.launch # ROS1 launch file
│       └── scripts/
│           └── hospital_info.py     # Information display node
├── gazebo_models-master/            # Gazebo model library
├── test_hospital_simulation.py     # Test and launch script
└── HOSPITAL_README.md              # This documentation
```

## 🔧 **Technical Specifications**

- **World Size**: 50m × 50m
- **Room Size**: ~8m × 8m each
- **Corridor Width**: 4m
- **Wall Height**: 4m
- **Physics Engine**: ODE with realistic settings
- **Lighting**: 5 hospital-grade light sources
- **Models Used**: 50+ realistic Gazebo models

## 🪑 **Furniture & Equipment Inventory**

### **Medical Equipment**
- 3 Hospital beds (marble tables)
- 2 Operating tables
- 12 Medical cabinets
- 4 Medicine shelves (bookshelves)
- 8 Medical supply boxes

### **Furniture**
- 15 Chairs (cafe tables)
- 8 Tables for various purposes
- 4 Reception/office desks
- 6 Waiting area chairs

### **Interactive Objects**
- 12 Cardboard boxes (medical supplies)
- 8 Bowls (medical containers)
- 6 Bottles/cans (medicine containers)
- 2 Wooden medical carts

## 👥 **People in the Environment**

- **8 Medical Staff**: Doctors, nurses, surgeons
- **6 Patients**: In various treatment areas
- **4 Visitors**: Family members and companions
- **2 Administrative Staff**: Reception and support

## 🌟 **Key Features**

- ✅ **Realistic Hospital Layout** with proper medical workflow
- ✅ **Modular Room Design** for easy modification
- ✅ **Navigation-Ready Coordinates** for robot path planning
- ✅ **Collision Detection** on all furniture and walls
- ✅ **Proper Lighting** for realistic simulation
- ✅ **Self-Contained** with included model library
- ✅ **ROS1 Compatible** with standard topics and services

---

**🏥 Ready to revolutionize medical robotics research with this comprehensive hospital simulation environment!**

🏥 HOSPITAL SIMULATION - ROS1 RUN COMMANDS
=============================================

📦 PREREQUISITES
-----------------
# Install ROS1 Noetic (Ubuntu 20.04)
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
sudo apt-key adv --keyserver 'hkp://keyserver.ubuntu.com:80' --recv-key C1CF6E31E6BADE8868B172B4F42ED6FBAB17C654
sudo apt update
sudo apt install ros-noetic-desktop-full

# Install Gazebo packages
sudo apt install ros-noetic-gazebo-ros-pkgs ros-noetic-gazebo-ros-control
sudo apt install python3-catkin-tools

🔧 SETUP WORKSPACE
-------------------
# Navigate to workspace
cd ~/hospital_simulation_ws

# Build the project
source /opt/ros/noetic/setup.bash
catkin_make

# Source the workspace
source devel/setup.bash

# Set Gazebo model path
export GAZEBO_MODEL_PATH=~/hospital_simulation_ws/gazebo_models-master:$GAZEBO_MODEL_PATH

🚀 LAUNCH HOSPITAL SIMULATION
------------------------------
# Method 1: Using ROS launch file
roslaunch hospital_gazebo hospital_simulation.launch

# Method 2: Using test script
python3 test_hospital_simulation.py

# Method 3: Manual launch (step by step)
# Terminal 1: Start ROS master
roscore

# Terminal 2: Launch Gazebo with hospital world
export GAZEBO_MODEL_PATH=~/hospital_simulation_ws/gazebo_models-master:$GAZEBO_MODEL_PATH
roslaunch gazebo_ros empty_world.launch world_name:=src/hospital_gazebo/worlds/hospital_simulation.world

# Terminal 3: Launch hospital info node
rosrun hospital_gazebo hospital_info.py

🎯 NAVIGATION COORDINATES
-------------------------
Emergency Room:    (-16,  16)
Operating Room 1:  ( 16,  16)
Operating Room 2:  ( 16, -16)
Patient Room 1:    (-16,   4)
Patient Room 2:    (-16,  -4)
Waiting Area:      ( 16,   4)
Reception:         ( 16,  -4)
Pharmacy:          (-16, -16)
Main Corridor:     (  0,   0)

📡 USEFUL ROS COMMANDS
----------------------
# List all topics
rostopic list

# List all nodes
rosnode list

# Check Gazebo model states
rostopic echo /gazebo/model_states

# Monitor system
htop

🔧 TROUBLESHOOTING
------------------
# If models don't load:
export GAZEBO_MODEL_PATH=~/hospital_simulation_ws/gazebo_models-master:$GAZEBO_MODEL_PATH

# If build fails:
cd ~/hospital_simulation_ws
catkin_make clean
catkin_make

# If ROS commands not found:
source /opt/ros/noetic/setup.bash
source ~/hospital_simulation_ws/devel/setup.bash

# Kill all processes:
killall roscore rosmaster gzserver gzclient

📁 PROJECT STRUCTURE
---------------------
hospital_simulation_ws/
├── src/hospital_gazebo/           # Main package
│   ├── worlds/hospital_simulation.world  # Hospital world file
│   ├── launch/hospital_simulation.launch # Launch file
│   └── scripts/hospital_info.py          # Info display
├── gazebo_models-master/          # Gazebo models
├── test_hospital_simulation.py   # Test script
├── HOSPITAL_README.md            # Main documentation
├── SETUP_INSTRUCTIONS.md         # Setup guide
├── MODIFICATION_GUIDE.md         # Customization guide
└── HOSPITAL_RUN_COMMANDS.txt     # This file

🏥 HOSPITAL FEATURES
--------------------
✅ 8 Medical Departments (ER, OR, Patient Rooms, etc.)
✅ 50+ Realistic Furniture & Equipment
✅ 20+ People (Doctors, Nurses, Patients, Visitors)
✅ Modular Design for Easy Modification
✅ Navigation-Ready Coordinate System
✅ Proper Collision Detection
✅ Hospital-Grade Lighting
✅ Self-Contained Model Library

🌟 ENVIRONMENT HIGHLIGHTS
--------------------------
• Emergency Room: 2 examination tables, medical cabinets, staff
• Operating Rooms: Surgical tables, instrument cabinets, surgical teams
• Patient Rooms: Hospital beds, visitor chairs, medical equipment
• Waiting Area: 6 chairs, coffee table, magazine rack
• Reception: Desk, filing cabinets, administrative staff
• Pharmacy: Medicine shelves, pharmacist counter, storage
• Main Corridor: Central navigation hub with seating

🚀 NEXT STEPS
--------------
1. Launch the simulation using commands above
2. Explore the hospital environment in Gazebo
3. Add robots for navigation testing
4. Customize rooms using MODIFICATION_GUIDE.md
5. Develop medical robotics applications

📞 SUPPORT
-----------
• Check SETUP_INSTRUCTIONS.md for detailed installation
• See MODIFICATION_GUIDE.md for customization
• Review HOSPITAL_README.md for complete documentation
• Check terminal output for error messages
• Verify all dependencies are installed

🎯 READY FOR MEDICAL ROBOTICS RESEARCH!
========================================

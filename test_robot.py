#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from nav_msgs.msg import Odometry
import math

class RobotTester(Node):
    def __init__(self):
        super().__init__('robot_tester')
        
        # Subscribe to odometry to track robot position
        self.odom_subscription = self.create_subscription(
            Odometry,
            '/circle_bot/odom',
            self.odom_callback,
            10)
        
        # Subscribe to cmd_vel to see commands being sent
        self.cmd_subscription = self.create_subscription(
            Twist,
            '/circle_bot/cmd_vel',
            self.cmd_callback,
            10)
        
        self.get_logger().info('Robot Tester started! Monitoring robot movement...')
        
        # Store initial position
        self.initial_x = None
        self.initial_y = None
        self.positions = []
        
    def odom_callback(self, msg):
        x = msg.pose.pose.position.x
        y = msg.pose.pose.position.y
        
        if self.initial_x is None:
            self.initial_x = x
            self.initial_y = y
            self.get_logger().info(f'Initial position: x={x:.3f}, y={y:.3f}')
        
        # Store position for circle analysis
        self.positions.append((x, y))
        
        # Calculate distance from start
        distance = math.sqrt((x - self.initial_x)**2 + (y - self.initial_y)**2)
        
        # Log position every few seconds
        if len(self.positions) % 20 == 0:  # Every 2 seconds at 10Hz
            self.get_logger().info(f'Position: x={x:.3f}, y={y:.3f}, distance from start={distance:.3f}')
            
            # Check if robot is moving in a circle
            if len(self.positions) > 50:  # After 5 seconds
                self.analyze_circular_motion()
    
    def cmd_callback(self, msg):
        # Log the first few commands to verify they're being sent
        if not hasattr(self, 'cmd_count'):
            self.cmd_count = 0
        
        self.cmd_count += 1
        if self.cmd_count <= 5:
            self.get_logger().info(f'Command received: linear.x={msg.linear.x:.2f}, angular.z={msg.angular.z:.2f}')
    
    def analyze_circular_motion(self):
        if len(self.positions) < 50:
            return
            
        # Take last 50 positions
        recent_positions = self.positions[-50:]
        
        # Calculate center of circle (rough approximation)
        avg_x = sum(pos[0] for pos in recent_positions) / len(recent_positions)
        avg_y = sum(pos[1] for pos in recent_positions) / len(recent_positions)
        
        # Calculate average radius
        radii = [math.sqrt((pos[0] - avg_x)**2 + (pos[1] - avg_y)**2) for pos in recent_positions]
        avg_radius = sum(radii) / len(radii)
        
        self.get_logger().info(f'Circle analysis: center=({avg_x:.3f}, {avg_y:.3f}), avg_radius={avg_radius:.3f}')

def main(args=None):
    rclpy.init(args=args)
    
    robot_tester = RobotTester()
    
    try:
        rclpy.spin(robot_tester)
    except KeyboardInterrupt:
        pass
    
    robot_tester.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()

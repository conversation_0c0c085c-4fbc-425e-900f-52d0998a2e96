#!/usr/bin/env python3

import subprocess
import time
import os
import signal
import sys

def signal_handler(sig, frame):
    print('\n🛑 Shutting down test...')
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

def main():
    workspace_dir = "/home/<USER>/box_gazebo_ws"
    
    print("🏠 Testing Indoor Navigation System")
    print("=" * 40)
    
    # Change to workspace directory
    os.chdir(workspace_dir)
    
    print("📦 Step 1: Starting Gazebo with indoor house...")
    gazebo_cmd = "source install/setup.bash && ros2 launch gazebo_ros gazebo.launch.py world:=src/box_gazebo/worlds/indoor_house.world"
    gazebo_process = subprocess.Popen(gazebo_cmd, shell=True, executable='/bin/bash')
    
    # Wait for Gazebo to start
    print("⏳ Waiting for Gazebo to initialize...")
    time.sleep(8)
    
    print("🤖 Step 2: Publishing robot description...")
    robot_desc_cmd = 'source install/setup.bash && ros2 run robot_state_publisher robot_state_publisher --ros-args -p robot_description:="$(xacro src/box_gazebo/urdf/small_car.urdf.xacro)"'
    robot_desc_process = subprocess.Popen(robot_desc_cmd, shell=True, executable='/bin/bash')
    
    time.sleep(3)
    
    print("🚀 Step 3: Spawning enhanced car with lidar...")
    spawn_cmd = "source install/setup.bash && ros2 run gazebo_ros spawn_entity.py -topic robot_description -entity small_car -x 0 -y 0 -z 0.2"
    spawn_process = subprocess.Popen(spawn_cmd, shell=True, executable='/bin/bash')
    
    # Wait for spawn to complete
    spawn_process.wait()
    
    print("✅ Indoor navigation environment is ready!")
    print()
    print("🏠 ENVIRONMENT FEATURES:")
    print("   • 4-room house with furniture")
    print("   • Kitchen (top-left) with table, chairs, cabinets")
    print("   • Bedroom (top-right) with bed, dresser, nightstand")
    print("   • Living Room (bottom-left) with sofa, coffee table, TV")
    print("   • Office (bottom-right) with desk, chair, bookshelf")
    print("   • Interactive objects: humans and moveable boxes")
    print()
    print("🤖 ROBOT FEATURES:")
    print("   • Enhanced small car with lidar sensor")
    print("   • 360-degree laser scanner for obstacle detection")
    print("   • Camera for visual feedback")
    print("   • Differential drive for precise movement")
    print()
    print("🎮 NEXT STEPS:")
    print("   1. Open a new terminal")
    print("   2. Run: cd /home/<USER>/box_gazebo_ws")
    print("   3. Run: source install/setup.bash")
    print("   4. Run: python3 simple_room_navigator.py")
    print("   5. Try commands like: kitchen, bedroom, living_room, office")
    print()
    print("📡 AVAILABLE ROS2 TOPICS:")
    print("   • /small_car/cmd_vel - Movement commands")
    print("   • /small_car/scan - Lidar data")
    print("   • /small_car/front_camera/image_raw - Camera feed")
    print("   • /small_car/odom - Robot odometry")
    print()
    print("🔄 Simulation running... Press Ctrl+C to stop")
    
    try:
        # Keep the processes running
        gazebo_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping simulation...")
        gazebo_process.terminate()
        robot_desc_process.terminate()
        
        # Wait for processes to finish
        gazebo_process.wait()
        robot_desc_process.wait()
        
        print("👋 Simulation stopped!")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Bool
from nav_msgs.msg import Path, OccupancyGrid
from geometry_msgs.msg import PoseStamped
from sensor_msgs.msg import LaserScan
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation
import numpy as np
import threading
import time

class NavigationMonitor(Node):
    def __init__(self):
        super().__init__('navigation_monitor')
        
        # Subscribers
        self.status_sub = self.create_subscription(String, '/navigation_status', 
                                                 self.status_callback, 10)
        self.path_sub = self.create_subscription(Path, '/navigation_path', 
                                               self.path_callback, 10)
        self.mode_sub = self.create_subscription(Bool, '/manual_mode', 
                                               self.mode_callback, 10)
        self.map_sub = self.create_subscription(OccupancyGrid, '/occupancy_grid', 
                                              self.map_callback, 10)
        self.scan_sub = self.create_subscription(LaserScan, '/small_car/scan', 
                                               self.scan_callback, 10)
        
        # Data storage
        self.current_status = "Ready"
        self.current_mode = "MANUAL"
        self.current_path = []
        self.robot_position = [0.0, 0.0]
        self.occupancy_grid = None
        self.scan_data = None
        
        # Room definitions
        self.rooms = {
            'kitchen': (-4.0, 4.0, 'green'),
            'bedroom': (4.0, 4.0, 'purple'),
            'living_room': (-4.0, -4.0, 'yellow'),
            'office': (4.0, -4.0, 'blue'),
            'hallway': (0.0, 0.0, 'gray')
        }
        
        # Setup matplotlib
        self.setup_visualization()
        
        # Start monitoring
        self.get_logger().info('📊 Navigation Monitor started!')
        self.print_status()
        
    def setup_visualization(self):
        """Setup matplotlib visualization"""
        plt.ion()  # Interactive mode
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(15, 7))
        
        # Main map view
        self.ax1.set_xlim(-8, 8)
        self.ax1.set_ylim(-8, 8)
        self.ax1.set_aspect('equal')
        self.ax1.set_title('🏠 Indoor Navigation Map')
        self.ax1.grid(True, alpha=0.3)
        
        # Status view
        self.ax2.set_xlim(0, 10)
        self.ax2.set_ylim(0, 10)
        self.ax2.set_title('📊 Navigation Status')
        self.ax2.axis('off')
        
        # Draw house layout
        self.draw_house_layout()
        
        # Draw room labels
        self.draw_room_labels()
        
        plt.tight_layout()
        plt.show()
        
    def draw_house_layout(self):
        """Draw the house layout on the map"""
        # Exterior walls
        exterior = patches.Rectangle((-8, -8), 16, 16, linewidth=3, 
                                   edgecolor='black', facecolor='lightgray', alpha=0.3)
        self.ax1.add_patch(exterior)
        
        # Interior walls
        # Vertical wall
        wall1 = patches.Rectangle((-0.1, -6), 0.2, 12, linewidth=2, 
                                edgecolor='black', facecolor='black')
        self.ax1.add_patch(wall1)
        
        # Horizontal wall
        wall2 = patches.Rectangle((-6, -0.1), 12, 0.2, linewidth=2, 
                                edgecolor='black', facecolor='black')
        self.ax1.add_patch(wall2)
        
        # Room boundaries (dashed lines for clarity)
        self.ax1.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
        self.ax1.axvline(x=0, color='gray', linestyle='--', alpha=0.5)
        
    def draw_room_labels(self):
        """Draw room labels and boundaries"""
        for room_name, (x, y, color) in self.rooms.items():
            if room_name != 'hallway':
                # Draw room area
                if room_name == 'kitchen':
                    room_rect = patches.Rectangle((-8, 0), 8, 8, alpha=0.2, 
                                                facecolor=color)
                elif room_name == 'bedroom':
                    room_rect = patches.Rectangle((0, 0), 8, 8, alpha=0.2, 
                                                facecolor=color)
                elif room_name == 'living_room':
                    room_rect = patches.Rectangle((-8, -8), 8, 8, alpha=0.2, 
                                                facecolor=color)
                elif room_name == 'office':
                    room_rect = patches.Rectangle((0, -8), 8, 8, alpha=0.2, 
                                                facecolor=color)
                
                self.ax1.add_patch(room_rect)
                
            # Add room label
            self.ax1.text(x, y, room_name.replace('_', '\n'), 
                         ha='center', va='center', fontsize=10, 
                         bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7))
                         
    def status_callback(self, msg):
        """Handle navigation status updates"""
        self.current_status = msg.data
        self.update_display()
        
    def path_callback(self, msg):
        """Handle path updates"""
        self.current_path = []
        for pose in msg.poses:
            self.current_path.append([pose.pose.position.x, pose.pose.position.y])
        self.update_display()
        
    def mode_callback(self, msg):
        """Handle mode changes"""
        self.current_mode = "MANUAL" if msg.data else "AUTO"
        self.update_display()
        
    def map_callback(self, msg):
        """Handle occupancy grid updates"""
        self.occupancy_grid = msg
        # Could visualize the occupancy grid here
        
    def scan_callback(self, msg):
        """Handle lidar scan updates"""
        self.scan_data = msg
        # Could visualize lidar data here
        
    def update_display(self):
        """Update the visualization display"""
        # Clear previous path
        for line in self.ax1.lines:
            if line.get_color() == 'red':
                line.remove()
                
        # Draw current path
        if self.current_path and len(self.current_path) > 1:
            path_x = [point[0] for point in self.current_path]
            path_y = [point[1] for point in self.current_path]
            self.ax1.plot(path_x, path_y, 'r-', linewidth=3, alpha=0.7, label='Planned Path')
            
            # Draw waypoints
            self.ax1.scatter(path_x, path_y, c='red', s=50, alpha=0.8, zorder=5)
            
        # Update robot position (simplified - would use odometry in real system)
        if hasattr(self, 'robot_marker'):
            self.robot_marker.remove()
            
        self.robot_marker = self.ax1.scatter(self.robot_position[0], self.robot_position[1], 
                                           c='blue', s=200, marker='o', 
                                           edgecolors='black', linewidth=2, 
                                           label='Robot', zorder=10)
        
        # Update status display
        self.ax2.clear()
        self.ax2.set_xlim(0, 10)
        self.ax2.set_ylim(0, 10)
        self.ax2.set_title('📊 Navigation Status')
        self.ax2.axis('off')
        
        # Status text
        status_text = f"""
🎮 Control Mode: {self.current_mode}

📍 Current Status: {self.current_status}

🤖 Robot Position: ({self.robot_position[0]:.1f}, {self.robot_position[1]:.1f})

🎯 Path Waypoints: {len(self.current_path)}

🏠 Available Rooms:
   • Kitchen (Green)
   • Bedroom (Purple)  
   • Living Room (Yellow)
   • Office (Blue)

📋 Commands:
   • Manual: WASD keys
   • Auto: Type room names
   • Switch: 'auto'/'manual'
        """
        
        self.ax2.text(0.1, 0.9, status_text, transform=self.ax2.transAxes, 
                     fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        # Add legend to map
        self.ax1.legend(loc='upper right', bbox_to_anchor=(1, 1))
        
        plt.draw()
        plt.pause(0.01)
        
    def print_status(self):
        """Print status to console"""
        print("\n" + "="*60)
        print("📊 NAVIGATION MONITOR")
        print("="*60)
        print(f"🎮 Control Mode: {self.current_mode}")
        print(f"📍 Status: {self.current_status}")
        print(f"🤖 Robot Position: ({self.robot_position[0]:.1f}, {self.robot_position[1]:.1f})")
        print(f"🎯 Path Waypoints: {len(self.current_path)}")
        print("="*60)
        
    def run_monitor(self):
        """Run the monitoring loop"""
        while rclpy.ok():
            rclpy.spin_once(self, timeout_sec=0.1)
            time.sleep(0.1)

def main(args=None):
    rclpy.init(args=args)
    
    monitor = NavigationMonitor()
    
    try:
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=monitor.run_monitor)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # Keep matplotlib window open
        plt.show(block=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Monitor stopped")
    finally:
        monitor.destroy_node()
        rclpy.shutdown()
        plt.close('all')

if __name__ == '__main__':
    main()

<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>ColeB</author>
      <authoring_tool>OpenCOLLADA for 3ds Max;  Version: 1.6;  Revision: 24</authoring_tool>
    </contributor>
    <created>2016-08-14T16:30:24</created>
    <modified>2016-08-14T16:30:24</modified>
    <unit name="millimeter" meter="0.001"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="_3_-_Default">
      <profile_COMMON>
        <newparam sid="TrayParts_png-surface">
          <surface type="2D">
            <init_from>TrayParts_png</init_from>
          </surface>
        </newparam>
        <newparam sid="TrayParts_png-sampler">
          <sampler2D>
            <source>TrayParts_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.5882353 0.5882353 0.5882353 1</color>
            </ambient>
            <diffuse>
              <texture texture="TrayParts_png-sampler" texcoord="CHANNEL1"/>
            </diffuse>
            <specular>
              <color>0 0 0 1</color>
            </specular>
            <shininess>
              <float>9.999999</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <opacity_type sid="opacity_type" type="int">0</opacity_type>
            <falloff_type sid="falloff_type" type="int">0</falloff_type>
            <falloff sid="falloff" type="float">0</falloff>
            <index_of_refraction sid="index_of_refraction" type="float">1.5</index_of_refraction>
            <wire_size sid="wire_size" type="float">1</wire_size>
            <wire_units sid="wire_units" type="int">0</wire_units>
            <apply_reflection_dimming sid="apply_reflection_dimming" type="bool">0</apply_reflection_dimming>
            <dim_level sid="dim_level" type="float">0</dim_level>
            <reflection_level sid="reflection_level" type="float">3</reflection_level>
          </extended_shader>
          <shader>
            <ambient_diffuse_texture_lock sid="ambient_diffuse_texture_lock" type="bool">1</ambient_diffuse_texture_lock>
            <ambient_diffuse_lock sid="ambient_diffuse_lock" type="bool">1</ambient_diffuse_lock>
            <diffuse_specular_lock sid="diffuse_specular_lock" type="bool">0</diffuse_specular_lock>
            <use_self_illum_color sid="use_self_illum_color" type="bool">0</use_self_illum_color>
            <self_illumination sid="self_illumination" type="float">0</self_illumination>
            <specular_level sid="specular_level" type="float">0</specular_level>
            <soften sid="soften" type="float">0.1</soften>
          </shader>
        </technique>
      </extra>
    </effect>
  </library_effects>
  <library_materials>
    <material id="_3_-_Default-material" name="_3_-_Default">
      <instance_effect url="#_3_-_Default"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="geom-Arm" name="Arm">
      <mesh>
        <source id="geom-Arm-positions">
          <float_array id="geom-Arm-positions-array" count="606">-149.116 -92.38941 72.75529 -149.116 -80.01157 118.95 -149.116 -46.19468 152.7669 -149.116 5.40574e-5 165.1448 -149.116 46.19479 152.7669 -149.116 80.01167 118.95 -149.116 92.38952 72.7553 -149.116 80.01169 26.56058 -149.116 46.19481 -7.256324 -149.116 9.29719e-5 -19.63418 -149.116 -46.19464 -7.256364 -149.116 -80.01155 26.5605 -149.116 -77.5091 72.75529 -149.116 -67.12485 111.5099 -149.116 -38.75452 139.8802 -149.116 5.31392e-5 150.2645 -149.116 38.75463 139.8802 -149.116 67.12495 111.5099 -149.116 77.50921 72.7553 -149.116 67.12496 34.00073 -149.116 38.75465 5.630403 -149.116 8.63148e-5 -4.753872 -149.116 -38.75449 5.630363 -149.116 -67.12483 34.00066 -60.82654 5.52462e-5 72.75529 -93.67908 -284.6031 102.7514 -134.3604 -284.6031 102.7514 -134.3604 -284.6031 79.42773 -93.67908 -284.6031 79.42773 -93.67908 -309.5224 96.07433 -134.3604 -309.5223 96.07433 -134.3604 -297.8605 75.87541 -93.67908 -297.8605 75.87541 -93.67908 -327.7646 77.83214 -134.3604 -327.7646 77.83214 -134.3604 -307.5656 66.17029 -93.67908 -307.5657 66.17029 -93.67908 -334.4417 52.91285 -134.3604 -334.4417 52.91285 -134.3604 -311.118 52.91285 -93.67908 -311.118 52.91285 -93.67908 -327.7646 27.99356 -134.3604 -327.7646 27.99356 -134.3604 -307.5656 39.65541 -93.67908 -307.5657 39.65541 -93.67908 -309.5224 9.751376 -134.3604 -309.5223 9.751376 -134.3604 -297.8605 29.95029 -93.67908 -297.8605 29.95029 -93.67908 -284.6031 3.074269 -134.3604 -284.6031 3.074269 -134.3604 -284.6031 26.39797 -93.67908 -284.6031 26.39797 -93.67908 -259.6838 9.751376 -134.3604 -259.6838 9.751376 -134.3604 -271.3457 29.9503 -93.67908 -271.3457 29.9503 -93.67908 -241.4416 27.99356 -134.3604 -241.4416 27.99356 -134.3604 -261.6406 39.6554 -93.67908 -261.6406 39.65541 -93.67908 -234.7645 52.91284 -134.3604 -234.7645 52.91284 -134.3604 -258.0882 52.91284 -93.67908 -258.0882 52.91285 -93.67908 -241.4416 77.83213 -134.3604 -241.4416 77.83213 -134.3604 -261.6406 66.17028 -93.67908 -261.6406 66.17028 -93.67908 -259.6838 96.07433 -134.3604 -259.6838 96.07432 -134.3604 -271.3457 75.8754 -93.67908 -271.3457 75.8754 -93.67908 -280.1176 101.5496 -134.3604 -280.1176 101.5496 -93.67908 -249.377 20.05821 -134.3604 -249.377 20.05821 -134.3604 -71.62653 8.376291 -134.3604 -19.09124 160.2669 -93.67908 -19.09127 160.2669 -93.67908 -71.62653 8.376291 -93.67908 -215.5443 28.14892 -93.67908 -175.1573 31.82735 -93.67908 -130.1171 27.58546 -134.3604 -215.5443 28.14891 -134.3604 -175.1573 31.82735 -134.3604 -130.1171 27.58546 -134.3604 -227.5685 123.2099 -134.3604 -164.9556 144.1217 -134.3604 -103.6631 157.1101 -93.67908 -227.5685 123.2099 -93.67908 -164.9556 144.1217 -93.67908 -103.6632 157.1101 -134.3604 -72.12896 93.28773 -93.67908 -72.12899 93.28773 0 51.30091 161.611 0 51.30093 -16.10042 0 -88.85567 21.45436 149.116 -92.38941 72.75529 149.116 -80.01157 118.95 149.116 -67.12485 111.5099 149.116 -77.5091 72.75529 149.116 -46.19468 152.7669 149.116 -38.75452 139.8802 149.116 5.40574e-5 165.1448 149.116 5.31392e-5 150.2645 149.116 46.19479 152.7669 149.116 38.75463 139.8802 149.116 80.01167 118.95 149.116 67.12495 111.5099 149.116 92.38952 72.7553 149.116 77.50921 72.7553 149.116 80.01169 26.56058 149.116 67.12496 34.00073 149.116 46.19481 -7.256324 149.116 38.75465 5.630403 149.116 9.29719e-5 -19.63418 149.116 8.63148e-5 -4.753872 149.116 -46.19464 -7.256364 149.116 -38.75449 5.630363 149.116 -80.01155 26.5605 149.116 -67.12483 34.00066 60.82654 5.52462e-5 72.75529 93.67908 -284.6031 102.7514 134.3604 -284.6031 102.7514 134.3604 -309.5223 96.07433 93.67908 -309.5224 96.07433 134.3604 -284.6031 79.42773 134.3604 -297.8605 75.87541 93.67908 -284.6031 79.42773 93.67908 -297.8605 75.87541 134.3604 -327.7646 77.83214 93.67908 -327.7646 77.83214 134.3604 -307.5656 66.17029 93.67908 -307.5657 66.17029 134.3604 -334.4417 52.91285 93.67908 -334.4417 52.91285 134.3604 -311.118 52.91285 93.67908 -311.118 52.91285 134.3604 -327.7646 27.99356 93.67908 -327.7646 27.99356 134.3604 -307.5656 39.65541 93.67908 -307.5657 39.65541 134.3604 -309.5223 9.751376 93.67908 -309.5224 9.751376 134.3604 -297.8605 29.95029 93.67908 -297.8605 29.95029 134.3604 -284.6031 3.074269 93.67908 -284.6031 3.074269 134.3604 -284.6031 26.39797 93.67908 -284.6031 26.39797 134.3604 -259.6838 9.751376 93.67908 -259.6838 9.751376 134.3604 -271.3457 29.9503 93.67908 -271.3457 29.9503 134.3604 -249.377 20.05821 93.67908 -249.377 20.05821 134.3604 -261.6406 39.6554 134.3604 -241.4416 27.99356 93.67908 -261.6406 39.65541 93.67908 -241.4416 27.99356 134.3604 -258.0882 52.91284 134.3604 -234.7645 52.91284 93.67908 -258.0882 52.91285 93.67908 -234.7645 52.91284 134.3604 -261.6406 66.17028 134.3604 -241.4416 77.83213 93.67908 -261.6406 66.17028 93.67908 -241.4416 77.83213 134.3604 -271.3457 75.8754 134.3604 -259.6838 96.07432 93.67908 -271.3457 75.8754 93.67908 -259.6838 96.07433 134.3604 -280.1176 101.5496 93.67908 -280.1176 101.5496 134.3604 -215.5443 28.14891 134.3604 -175.1573 31.82735 93.67908 -175.1573 31.82735 93.67908 -215.5443 28.14892 134.3604 -227.5685 123.2099 134.3604 -164.9556 144.1217 93.67908 -227.5685 123.2099 93.67908 -164.9556 144.1217 134.3604 -130.1171 27.58546 134.3604 -71.62653 8.376291 93.67908 -71.62653 8.376291 93.67908 -130.1171 27.58546 134.3604 -103.6631 157.1101 134.3604 -19.09124 160.2669 134.3604 -72.12896 93.28773 93.67908 -103.6632 157.1101 93.67908 -19.09127 160.2669 93.67908 -72.12899 93.28773 0 -88.85571 124.0562 0 -102.6017 72.75529 0 -51.30085 161.611 0 2.86784e-5 175.3571 0 88.85575 124.0562 0 102.6018 72.75531 0 88.85576 21.45444 0 7.18944e-5 -29.84647 0 -51.3008 -16.10047</float_array>
          <technique_common>
            <accessor source="#geom-Arm-positions-array" count="202" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Arm-normals">
          <float_array id="geom-Arm-normals-array" count="966">-1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999999 0 0 -1 0 0 -0.6597373 0.7514962 2.1727e-8 -0.6597374 0.6508148 -0.375748 -1 -3.35675e-8 -2.79729e-8 -0.6597374 0.3757481 -0.6508148 -0.6597374 0 -0.7514962 -0.6597374 -0.3757481 -0.6508148 -0.6597374 -0.6508148 -0.3757481 -0.6597373 -0.7514963 -8.69081e-8 -0.6597374 -0.650815 0.3757479 -0.6597374 -0.3757484 0.6508147 -0.6597375 -4.0195e-7 0.7514962 -0.6597374 0.3757477 0.650815 -0.6597375 0.6508146 0.3757483 -1.00712e-7 -4.91051e-7 1 -1.00711e-7 -2.45525e-7 1 -7.46948e-7 -0.4999996 0.8660256 -7.46948e-7 -0.5000002 0.8660253 -0.9999999 0 0 -0.7702618 -2.56789e-7 -0.6377277 -0.7702616 0.3188638 -0.5522888 -1 0 0 0.7702619 -1.0021e-7 -0.6377277 0.7702618 0.3188637 -0.5522885 1 0 0 1 0 0 -9.21926e-7 -0.8660254 0.4999998 -9.21926e-7 -0.8660256 0.4999998 -0.7702618 0.5522885 -0.3188639 -1 0 0 0.7702622 0.5522881 -0.3188636 0.9999999 0 0 -7.51652e-7 -0.9999999 2.9463e-8 -7.51652e-7 -1 3.92841e-8 -0.7702613 0.6377283 2.50526e-8 -0.9999999 0 0 0.7702618 0.6377278 1.87894e-8 0.9999999 0 0 -9.28511e-7 -0.8660254 -0.4999999 -9.28511e-7 -0.8660255 -0.4999999 -0.7702618 0.5522885 0.318864 -1 0 0 0.7702622 0.5522881 0.3188637 1 0 0 -7.55414e-7 -0.4999997 -0.8660256 -7.55415e-7 -0.5000003 -0.8660252 -0.7702615 0.3188639 0.5522889 -1 0 0 0.7702618 0.3188639 0.5522887 1 0 0 -1.524e-7 4.91051e-8 -1 -1.524e-7 -2.06241e-7 -1 -0.7702619 -1.56578e-7 0.6377277 -1 0 0 0.770262 0 0.6377276 1 0 0 4.98592e-8 0.5000002 -0.8660253 4.98592e-8 0.5000002 -0.8660253 -0.7702618 -0.318864 0.5522885 -1 0 0 0.7702615 -0.318864 0.5522888 0.9999999 0 0 4.76186e-8 0.4882337 -0.872713 4.76186e-8 0.4882337 -0.872713 -0.7702617 -0.5522882 0.3188645 -1 0 0 -1 0 0 0.7702617 -0.5522882 0.3188645 1 0 0 1 0 0 -0.7702622 -0.6377274 1.25263e-8 -1 0 0 0.7702622 -0.6377274 0 1 0 0 -0.7702616 -0.5522885 -0.3188644 -1 0 0 0.7702617 -0.5522884 -0.3188643 1 0 0 -0.7702618 -0.3188641 -0.5522883 -1 0 0 0.7702615 -0.3188641 -0.5522887 1 0 0 -0.9999999 0 0 1 0 0 2.59591e-8 -0.0645421 0.997915 2.59592e-8 -0.06454179 0.997915 9.23083e-8 0.162062 -0.9867806 4.64424e-8 -0.001537127 -0.9999989 4.64424e-8 -0.001537127 -0.9999989 9.23083e-8 0.162062 -0.9867806 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1.01059e-7 -0.3491415 0.93707 -8.84181e-8 -0.2624678 0.9649408 -8.84182e-8 -0.2624678 0.9649408 -1.01059e-7 -0.3491415 0.93707 1 0 0 1 0 0 1 0 0 1 0 0 -1.83922e-8 -0.2041646 -0.9789366 -3.65552e-8 -0.3120188 -0.950076 -3.65552e-8 -0.3120188 -0.9500759 -1.83922e-8 -0.2041646 -0.9789366 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999999 0 0 -7.34318e-8 -0.1227545 0.992437 -2.65918e-8 -0.03730119 0.9993041 -2.65918e-8 -0.03730119 0.9993041 -7.34317e-8 -0.1227544 0.9924371 0.9999999 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1.24432e-9 -0.8660254 0.5 -0.06832574 -0.8640016 0.4988315 -0.0683257 -0.9976631 -1.45652e-7 2.48864e-9 -1 -1.19455e-7 0 -0.5 0.8660254 -0.06832568 -0.4988315 0.8640016 2.48864e-9 1.095e-7 1 -0.06832559 1.84493e-7 0.9976631 -6.2216e-9 0.5 0.8660254 -0.0683255 0.4988316 0.8640016 -3.73296e-9 0.8660254 0.5000001 -0.06832542 0.8640016 0.4988316 0 1 9.95456e-8 -0.06832533 0.9976631 7.76813e-8 -1.24432e-9 0.8660255 -0.4999998 -0.06832533 0.8640018 -0.4988314 -2.48864e-9 0.5000003 -0.8660252 -0.0683254 0.4988319 -0.8640014 -1.24432e-9 4.18091e-7 -1 -0.06832549 4.66088e-7 -0.997663 -2.48864e-9 -0.4999995 -0.8660257 -0.06832559 -0.498831 -0.8640019 -1.24432e-9 -0.8660252 -0.5000003 -0.06832565 -0.8640014 -0.4988319 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999999 0 0 0.6597373 0.7514962 2.1727e-8 1 -3.35675e-8 -2.79729e-8 0.6597374 0.6508148 -0.375748 0.6597374 0.3757481 -0.6508148 0.6597374 0 -0.7514962 0.6597374 -0.3757481 -0.6508148 0.6597374 -0.6508148 -0.3757481 0.6597373 -0.7514963 -8.69081e-8 0.6597374 -0.650815 0.3757479 0.6597374 -0.3757484 0.6508147 0.6597375 -4.0195e-7 0.7514962 0.6597374 0.3757477 0.650815 0.6597375 0.6508146 0.3757483 1.00712e-7 -4.91051e-7 1 7.50711e-7 -0.5000002 0.8660253 7.5071e-7 -0.4999996 0.8660256 1.00711e-7 -2.45525e-7 1 0.9999999 0 0 1 0 0 0.7702616 0.3188638 -0.5522888 0.7702618 -2.44262e-7 -0.6377277 -0.7702618 0.3188637 -0.5522885 -0.7702619 -9.3947e-8 -0.6377277 -1 0 0 -1 0 0 9.25689e-7 -0.8660256 0.4999998 9.25689e-7 -0.8660254 0.4999998 1 0 0 0.7702618 0.5522885 -0.3188639 -0.7702622 0.5522881 -0.3188636 -0.9999999 0 0 7.50711e-7 -1 1.9642e-8 7.50711e-7 -0.9999999 1.9642e-8 0.9999999 0 0 0.7702613 0.6377283 3.13157e-8 -0.7702618 0.6377278 2.50525e-8 -0.9999999 0 0 9.20044e-7 -0.8660255 -0.4999999 9.20044e-7 -0.8660254 -0.4999999 1 0 0 0.7702618 0.5522885 0.318864 -0.7702622 0.5522881 0.3188637 -1 0 0 7.47889e-7 -0.5000003 -0.8660253 7.47888e-7 -0.4999997 -0.8660256 1 0 0 0.7702615 0.3188639 0.5522889 -0.7702618 0.3188639 0.5522887 -1 0 0 1.51459e-7 -1.86599e-7 -1 1.51459e-7 5.89261e-8 -1 1 0 0 0.7702619 -1.44052e-7 0.6377277 -0.770262 6.26313e-9 0.6377276 -1 0 0 -5.08e-8 0.5000001 -0.8660253 -5.08e-8 0.5000001 -0.8660253 1 0 0 0.7702617 -0.318864 0.5522885 -0.7702615 -0.318864 0.5522888 -0.9999999 0 0 -4.76186e-8 0.4882337 -0.872713 -4.76186e-8 0.4882337 -0.872713 1 0 0 1 0 0 0.7702618 -0.5522882 0.3188645 -0.7702617 -0.5522882 0.3188645 -1 0 0 -1 0 0 1 0 0 0.7702622 -0.6377274 1.25263e-8 -0.7702622 -0.6377274 0 -1 0 0 0.9999999 0 0 0.7702616 -0.5522885 -0.3188644 -0.7702616 -0.5522885 -0.3188643 -1 0 0 1 0 0 0.7702618 -0.3188641 -0.5522884 -0.7702615 -0.3188641 -0.5522887 -1 0 0 1 0 0 -1 0 0 -2.59591e-8 -0.0645421 0.997915 -2.59592e-8 -0.06454179 0.997915 -9.23083e-8 0.162062 -0.9867806 -9.23083e-8 0.162062 -0.9867806 -4.64424e-8 -0.001537136 -0.9999989 -4.64424e-8 -0.001537136 -0.9999989 1 0 0 1 0 0 1 0 0 1 0 0 1.01059e-7 -0.3491415 0.93707 1.01059e-7 -0.3491415 0.93707 8.84182e-8 -0.2624677 0.9649408 8.84181e-8 -0.2624678 0.9649408 -1 0 0 -1 0 0 -1 0 0 -1 0 0 1.83922e-8 -0.2041645 -0.9789365 1.83922e-8 -0.2041645 -0.9789365 3.65552e-8 -0.3120188 -0.9500759 3.65552e-8 -0.3120188 -0.950076 1 0 0 0.9999999 0 0 1 0 0 1 0 0 1 0 0 7.34318e-8 -0.1227545 0.992437 7.34317e-8 -0.1227544 0.992437 2.65918e-8 -0.03730119 0.9993041 2.65918e-8 -0.03730119 0.9993041 -0.9999999 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 0.06832571 -0.9976631 -1.26232e-7 0.06832574 -0.8640016 0.4988315 0.06832568 -0.4988315 0.8640016 0.06832559 1.65073e-7 0.9976631 0.0683255 0.4988316 0.8640016 0.06832541 0.8640016 0.4988316 0.06832533 0.9976631 7.76813e-8 0.06832533 0.8640018 -0.4988314 0.0683254 0.4988319 -0.8640015 0.06832549 4.66088e-7 -0.997663 0.06832558 -0.498831 -0.8640019 0.06832565 -0.8640014 -0.4988319</float_array>
          <technique_common>
            <accessor source="#geom-Arm-normals-array" count="322" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Arm-map1">
          <float_array id="geom-Arm-map1-array" count="1098">0.3992521 0.002000002 0 0.4475653 0.002000002 0 0.3507851 0.002000002 0 0.3021301 0.002000002 0 0.2534745 0.002000002 0 0.2050031 0.002000002 0 0.3765014 0.7451246 0 0.3767934 0.8001809 0 0.3602231 0.7958349 0 0.3599781 0.7496461 0 0.3495181 0.8480069 0 0.3373408 0.8359581 0 0.301984 0.8757879 0 0.2974627 0.8592646 0 0.246928 0.8760799 0 0.2512739 0.8595095 0 0.1991019 0.8488046 0 0.2111506 0.8366274 0 0.1713209 0.8012706 0 0.1878442 0.7967491 0 0.1710289 0.7462144 0 0.1875992 0.7505603 0 0.1983041 0.6983883 0 0.2104815 0.7104371 0 0.2458381 0.6706074 0 0.2503596 0.6871306 0 0.3008943 0.6703154 0 0.2965484 0.6868857 0 0.3487204 0.6975906 0 0.3366715 0.7097679 0 0.2739112 0.7731976 0 0.276882 0.2001693 0 0.2768949 0.1603662 0 0.1685087 0.2954321 0 0.1685087 0.335477 0 0.187749 0.5758139 0 0.3021367 0.1603744 0 0.3021234 0.2001777 0 0.1551604 0.2954321 0 0.1551604 0.335477 0 0.1864839 0.5985997 0 0.3273784 0.1603827 0 0.3273648 0.2001863 0 0.1418121 0.2954321 0 0.1418121 0.335477 0 0.1733394 0.6013511 0 0.3526204 0.1603913 0 0.3526067 0.2001951 0 0.1284639 0.2954321 0 0.1284639 0.335477 0 0.163042 0.5809852 0 0.3778626 0.1604 0 0.3778484 0.2002041 0 0.1151156 0.2954321 0 0.1151156 0.335477 0 0.4477502 0.454197 0 0.4031049 0.160409 0 0.4030904 0.2002135 0 0.1017673 0.2954321 0 0.1017673 0.335477 0 0.4465033 0.4314105 0 0.4283474 0.1604183 0 0.4283325 0.200223 0 0.08841909 0.2954321 0 0.08841909 0.335477 0 0.4712057 0.4366015 0 0.45359 0.1604278 0 0.4535749 0.2002326 0 0.07507072 0.2954321 0 0.07507072 0.335477 0 0.4608923 0.4569587 0 0.1633316 0.610306 0 0.06172255 0.2954321 0 0.06172255 0.335477 0 0.1442309 0.5978175 0 0.4900031 0.4534483 0 0.04837431 0.2954321 0 0.04837431 0.335477 0 0.4708928 0.4659216 0 0.159142 0.6230649 0 0.03502595 0.2954321 0 0.03502595 0.335477 0 0.1363562 0.6217999 0 0.4978587 0.4774367 0 0.02167765 0.2954321 0 0.02167765 0.335477 0 0.4750721 0.4786836 0 0.4678523 0.1604331 0 0.4678368 0.2002383 0 0.008329229 0.2954321 0 0.008329229 0.335477 0 0.1618933 0.636209 0 0.2723514 0.1603648 0 0.141528 0.6465063 0 0.2723387 0.2001677 0 0.50189 0.1604466 0 0.4926677 0.502139 0 0.5415713 0.1604625 0 0.541555 0.200268 0 0.5018743 0.2002518 0 0.216728 0.2001501 0 0.1521412 0.2001303 0 0.4723104 0.4918258 0 0.1708481 0.6462165 0 0.15836 0.6653166 0 0.1521534 0.1603276 0 0.2167406 0.1603474 0 0.5858371 0.1604805 0 0.6460761 0.1605055 0 0.6460596 0.2003111 0 0.5858206 0.2002862 0 0.09084098 0.2001117 0 0.008037919 0.2000868 0 0.008049639 0.1602841 0 0.09085292 0.160309 0 0.4758209 0.5209364 0 0.4633477 0.5018262 0 0.1836069 0.6504058 0 0.1823419 0.6731911 0 0.4518325 0.5287922 0 0.1566784 0.002000002 0 0.1084747 0.002000002 0 0.06038593 0.002000002 0 0.0124367 0.002000002 0 0.5437846 0.002000002 0 0.495744 0.002000002 0 0.4505855 0.5060056 0 0.1967512 0.6476545 0 0.591646 0.002000002 0 0.2070482 0.6680198 0 0.4271301 0.5236011 0 0.4374435 0.5032439 0 0.2067589 0.6386999 0 0.225859 0.6511884 0 0.2176763 0.6585101 0 0.4165097 0.5140826 0 0.4083327 0.5067543 0 0.427443 0.4942811 0 0.2109486 0.625941 0 0.2337341 0.6272064 0 0.4004771 0.4827659 0 0.4232636 0.481519 0 0.2081975 0.6127967 0 0.2285631 0.6024999 0 0.4056681 0.4580636 0 0.4260253 0.4683769 0 0.1992429 0.6027891 0 0.2117316 0.583689 0 0.4225148 0.4392661 0 0.4349881 0.4583764 0 0.1920661 0.5772313 0 0.4421854 0.4328246 0 0.2445783 0.5589219 0 0.3068813 0.5418901 0 0.2908218 0.6510396 0 0.2511671 0.6524419 0 0.3830235 0.5079872 0 0.3433699 0.5065528 0 0.3273995 0.3973898 0 0.3896884 0.4144726 0 0.3674642 0.5325271 0 0.4502558 0.534032 0 0.3948077 0.5965878 0 0.3906911 0.6795672 0 0.3345925 0.6576276 0 0.2995938 0.5131049 0 0.2434773 0.5349989 0 0.2394283 0.4520161 0 0.1840311 0.3894149 0 0.2668242 0.3879775 0 0.4001258 0.1489381 0 0.4494536 0.1489381 0 0.3510281 0.1489381 0 0.3020071 0.1489381 0 0.2529986 0.1489381 0 0.2039128 0.1489381 0 0.1545967 0.1489381 0 0.1048057 0.1489381 0 0.05414959 0.1489381 0 0.002000035 0.1489381 0 0.6020852 0.1489381 0 0.5499238 0.1489381 0 0.4992563 0.1489381 0 1.376501 0.7451246 0 1.376793 0.8001809 0 1.360223 0.7958349 0 1.359978 0.7496461 0 1.349518 0.8480069 0 1.337341 0.8359581 0 1.301984 0.8757879 0 1.297463 0.8592646 0 1.246928 0.8760799 0 1.251274 0.8595095 0 1.199102 0.8488046 0 1.211151 0.8366274 0 1.171321 0.8012706 0 1.187844 0.7967491 0 1.171029 0.7462144 0 1.187599 0.7505603 0 1.198304 0.6983883 0 1.210481 0.7104371 0 1.245838 0.6706074 0 1.25036 0.6871306 0 1.300894 0.6703154 0 1.296548 0.6868857 0 1.34872 0.6975906 0 1.336672 0.7097679 0 1.273911 0.7731976 0 1.276882 0.2001693 0 1.276895 0.1603662 0 1.302137 0.1603744 0 1.302123 0.2001777 0 1.187749 0.5758139 0 1.186484 0.5985997 0 1.173339 0.6013511 0 1.163042 0.5809852 0 1.168509 0.2954321 0 1.168509 0.335477 0 1.15516 0.335477 0 1.15516 0.2954321 0 1.44775 0.454197 0 1.446503 0.4314105 0 1.471206 0.4366015 0 1.460892 0.4569587 0 1.327378 0.1603827 0 1.327365 0.2001863 0 1.163332 0.610306 0 1.144231 0.5978175 0 1.141812 0.335477 0 1.141812 0.2954321 0 1.490003 0.4534483 0 1.470893 0.4659216 0 1.35262 0.1603913 0 1.352607 0.2001951 0 1.159142 0.6230649 0 1.136356 0.6217999 0 1.128464 0.335477 0 1.128464 0.2954321 0 1.497859 0.4774367 0 1.475072 0.4786836 0 1.377863 0.1604 0 1.377848 0.2002041 0 1.161893 0.636209 0 1.141528 0.6465063 0 1.115116 0.335477 0 1.115116 0.2954321 0 1.492668 0.502139 0 1.47231 0.4918258 0 1.403105 0.160409 0 1.40309 0.2002135 0 1.170848 0.6462165 0 1.15836 0.6653166 0 1.101767 0.335477 0 1.101767 0.2954321 0 1.475821 0.5209364 0 1.463348 0.5018262 0 1.428347 0.1604183 0 1.428333 0.200223 0 1.183607 0.6504058 0 1.182342 0.6731911 0 1.088419 0.335477 0 1.088419 0.2954321 0 1.451833 0.5287922 0 1.450585 0.5060056 0 1.45359 0.1604278 0 1.453575 0.2002326 0 1.196751 0.6476545 0 1.207048 0.6680198 0 1.075071 0.335477 0 1.075071 0.2954321 0 1.42713 0.5236011 0 1.437443 0.5032439 0 1.467852 0.1604331 0 1.467837 0.2002383 0 1.206759 0.6386999 0 1.225859 0.6511884 0 1.217676 0.6585101 0 1.061723 0.335477 0 1.061723 0.2954321 0 1.41651 0.5140826 0 1.408333 0.5067543 0 1.427443 0.4942811 0 1.210949 0.625941 0 1.233734 0.6272064 0 1.048374 0.335477 0 1.048374 0.2954321 0 1.400477 0.4827659 0 1.423264 0.481519 0 1.208198 0.6127967 0 1.228563 0.6024999 0 1.035026 0.335477 0 1.035026 0.2954321 0 1.405668 0.4580636 0 1.426025 0.4683769 0 1.199243 0.6027891 0 1.211732 0.583689 0 1.021678 0.335477 0 1.021678 0.2954321 0 1.422515 0.4392661 0 1.434988 0.4583764 0 1.192066 0.5772313 0 1.008329 0.335477 0 1.008329 0.2954321 0 1.442185 0.4328246 0 1.272351 0.1603648 0 1.272339 0.2001677 0 1.50189 0.1604466 0 1.541571 0.1604625 0 1.541555 0.200268 0 1.501874 0.2002518 0 1.244578 0.5589219 0 1.306881 0.5418901 0 1.290822 0.6510396 0 1.251167 0.6524419 0 1.216728 0.2001501 0 1.152141 0.2001303 0 1.152153 0.1603276 0 1.216741 0.1603474 0 1.383024 0.5079872 0 1.34337 0.5065528 0 1.327399 0.3973898 0 1.389688 0.4144726 0 1.585837 0.1604805 0 1.646076 0.1605055 0 1.64606 0.2003111 0 1.585821 0.2002862 0 1.367464 0.5325271 0 1.450256 0.534032 0 1.394808 0.5965878 0 1.390691 0.6795672 0 1.334593 0.6576276 0 1.090841 0.2001117 0 1.008038 0.2000868 0 1.00805 0.1602841 0 1.090853 0.160309 0 1.299594 0.5131049 0 1.243477 0.5349989 0 1.239428 0.4520161 0 1.184031 0.3894149 0 1.266824 0.3879775 0 1.400126 0.1489381 0 1.399252 0.002000002 0 1.447565 0.002000002 0 1.449454 0.1489381 0 1.351028 0.1489381 0 1.350785 0.002000002 0 1.302007 0.1489381 0 1.30213 0.002000002 0 1.252999 0.1489381 0 1.253474 0.002000002 0 1.203913 0.1489381 0 1.205003 0.002000002 0 1.154597 0.1489381 0 1.156678 0.002000002 0 1.104806 0.1489381 0 1.108475 0.002000002 0 1.05415 0.1489381 0 1.060386 0.002000002 0 1.002 0.1489381 0 1.012437 0.002000002 0 1.549924 0.1489381 0 1.543785 0.002000002 0 1.591646 0.002000002 0 1.602085 0.1489381 0 1.499256 0.1489381 0 1.495744 0.002000002 0</float_array>
          <technique_common>
            <accessor source="#geom-Arm-map1-array" count="366" stride="3">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
              <param name="P" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-Arm-vertices">
          <input semantic="POSITION" source="#geom-Arm-positions"/>
        </vertices>
        <polylist material="_3_-_Default_1" count="194">
          <input semantic="VERTEX" source="#geom-Arm-vertices" offset="0"/>
          <input semantic="NORMAL" source="#geom-Arm-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#geom-Arm-map1" offset="2" set="0"/>
          <vcount>4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 5 4 5 4 4 4 4 4 4 4 4 4 5 4 5 4 4 4 4 4 4 4 4 5 4 8 4 4 4 5 4 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 3 3 3 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 5 4 5 4 4 4 4 4 4 4 4 4 5 4 5 4 4 4 4 4 4 4 4 5 4 8 4 4 4 5 4 8 4 4 4 4 4 4 4 4 4 4 4 4 </vcount>
          <p>0 0 6 1 1 7 13 2 8 12 3 9 1 1 7 2 4 10 14 5 11 13 2 8 2 4 10 3 6 12 15 7 13 14 5 11 3 6 12 4 8 14 16 9 15 15 7 13 4 8 14 5 10 16 17 11 17 16 9 15 5 10 16 6 12 18 18 13 19 17 11 17 6 12 18 7 14 20 19 15 21 18 13 19 7 14 20 8 16 22 20 17 23 19 15 21 8 16 22 9 18 24 21 19 25 20 17 23 9 18 24 10 20 26 22 21 27 21 19 25 10 20 26 11 22 28 23 23 29 22 21 27 11 22 28 0 0 6 12 3 9 23 23 29 12 24 9 13 25 8 24 26 30 13 25 8 14 27 11 24 26 30 14 27 11 15 28 13 24 26 30 15 28 13 16 29 15 24 26 30 16 29 15 17 30 17 24 26 30 17 30 17 18 31 19 24 26 30 18 31 19 19 32 21 24 26 30 19 32 21 20 33 23 24 26 30 20 33 23 21 34 25 24 26 30 21 34 25 22 35 27 24 26 30 22 35 27 23 36 29 24 26 30 23 36 29 12 24 9 24 26 30 25 37 31 26 38 32 30 39 36 29 40 37 26 41 35 27 42 40 31 43 45 30 44 50 27 42 33 28 45 34 32 46 39 31 43 38 28 45 55 25 47 60 29 48 65 32 46 70 29 40 37 30 39 36 34 49 41 33 50 42 30 44 50 31 43 45 35 51 71 34 52 74 31 43 38 32 46 39 36 53 44 35 51 43 32 46 70 29 48 65 33 54 75 36 53 78 33 50 42 34 49 41 38 55 46 37 56 47 34 52 74 35 51 71 39 57 79 38 58 82 35 51 43 36 53 44 40 59 49 39 57 48 36 53 78 33 54 75 37 60 83 40 59 86 37 56 47 38 55 46 42 61 51 41 62 52 38 58 82 39 57 79 43 63 91 42 64 93 39 57 48 40 59 49 44 65 54 43 63 53 40 59 86 37 60 83 41 66 96 44 65 102 41 62 52 42 61 51 46 67 56 45 68 57 42 64 93 43 63 91 47 69 103 46 70 104 43 63 53 44 65 54 48 71 59 47 69 58 44 65 102 41 66 96 45 72 115 48 71 116 45 68 57 46 67 56 50 73 61 49 74 62 46 70 104 47 69 103 51 75 117 50 76 118 47 69 58 48 71 59 52 77 64 51 75 63 48 71 116 45 72 115 49 78 119 52 77 126 49 74 62 50 73 61 54 79 66 53 80 67 50 76 118 51 75 117 55 81 127 54 82 129 51 75 63 52 77 64 56 83 69 55 81 68 52 77 126 49 78 119 53 84 130 56 83 131 53 80 67 54 79 66 76 85 87 75 86 88 54 82 129 55 81 127 59 87 132 58 88 133 76 89 134 55 81 68 56 83 69 60 90 73 59 87 72 56 83 131 53 84 130 75 91 135 57 92 136 60 90 137 58 88 133 59 87 132 63 93 138 62 94 139 59 87 72 60 90 73 64 95 77 63 93 76 60 90 137 57 92 136 61 96 140 64 95 141 62 94 139 63 93 138 67 97 142 66 98 143 63 93 76 64 95 77 68 99 81 67 97 80 64 95 141 61 96 140 65 100 144 68 99 145 66 98 143 67 97 142 71 101 146 70 102 147 67 97 80 68 99 81 72 103 85 71 101 84 68 99 145 65 100 144 69 104 148 72 103 149 70 102 147 71 101 146 27 42 40 26 41 35 74 105 150 71 101 84 72 103 85 28 45 90 27 42 89 72 103 149 69 104 148 73 106 151 25 47 60 28 45 55 74 107 92 26 38 32 25 37 31 73 108 94 84 109 95 85 110 97 82 111 98 81 112 99 87 113 152 88 114 153 85 115 154 84 116 155 90 117 100 91 118 101 88 119 105 87 120 106 81 121 156 82 122 157 91 123 158 90 124 159 86 125 107 77 126 108 80 127 109 83 128 110 85 110 97 86 125 107 83 128 110 82 111 98 81 112 99 75 86 88 76 85 87 84 109 95 89 129 160 78 130 161 93 131 162 77 132 163 86 133 164 88 114 153 89 129 160 86 133 164 85 115 154 84 116 155 76 89 134 58 88 133 62 94 139 66 98 143 70 102 147 74 105 150 87 113 152 92 134 111 79 135 112 78 136 113 89 137 114 91 118 101 92 134 111 89 137 114 88 119 105 87 120 106 74 107 92 73 108 94 90 117 100 83 138 165 80 139 166 94 140 167 79 141 168 92 142 169 82 122 157 83 138 165 92 142 169 91 123 158 90 124 159 73 106 151 69 104 148 65 100 144 61 96 140 57 92 136 75 91 135 81 121 156 193 143 170 1 144 0 0 145 1 194 146 171 195 147 172 2 148 2 1 144 0 193 143 170 196 149 173 3 150 3 2 148 2 195 147 172 95 151 174 4 152 4 3 150 3 196 149 173 197 153 175 5 154 5 4 152 4 95 151 174 198 155 176 6 156 120 5 154 5 197 153 175 199 157 177 7 158 121 6 156 120 198 155 176 96 159 178 8 160 122 7 158 121 199 157 177 200 161 179 9 162 123 8 160 122 96 159 178 201 163 181 10 164 124 9 162 128 200 161 180 97 165 182 11 166 125 10 164 124 201 163 181 194 146 171 0 145 1 11 166 125 97 165 182 98 167 183 101 168 186 100 169 185 99 170 184 99 170 184 100 169 185 103 171 188 102 172 187 102 172 187 103 171 188 105 173 190 104 174 189 104 174 189 105 173 190 107 175 192 106 176 191 106 176 191 107 175 192 109 177 194 108 178 193 108 178 193 109 177 194 111 179 196 110 180 195 110 180 195 111 179 196 113 181 198 112 182 197 112 182 197 113 181 198 115 183 200 114 184 199 114 184 199 115 183 200 117 185 202 116 186 201 116 186 201 117 185 202 119 187 204 118 188 203 118 188 203 119 187 204 121 189 206 120 190 205 120 190 205 121 189 206 101 168 186 98 167 183 101 191 186 122 192 207 100 193 185 100 193 185 122 192 207 103 194 188 103 194 188 122 192 207 105 195 190 105 195 190 122 192 207 107 196 192 107 196 192 122 192 207 109 197 194 109 197 194 122 192 207 111 198 196 111 198 196 122 192 207 113 199 198 113 199 198 122 192 207 115 200 200 115 200 200 122 192 207 117 201 202 117 201 202 122 192 207 119 202 204 119 202 204 122 192 207 121 203 206 121 203 206 122 192 207 101 191 186 123 204 208 126 205 211 125 206 210 124 207 209 124 208 212 125 209 215 128 210 214 127 211 213 127 211 216 128 210 219 130 212 218 129 213 217 129 213 220 130 212 223 126 214 222 123 215 221 126 205 211 132 216 225 131 217 224 125 206 210 125 209 215 131 218 227 133 219 226 128 210 214 128 210 219 133 219 229 134 220 228 130 212 218 130 212 223 134 220 231 132 221 230 126 214 222 132 216 225 136 222 233 135 223 232 131 217 224 131 218 227 135 224 235 137 225 234 133 219 226 133 219 229 137 225 237 138 226 236 134 220 228 134 220 231 138 226 239 136 227 238 132 221 230 136 222 233 140 228 241 139 229 240 135 223 232 135 224 235 139 230 243 141 231 242 137 225 234 137 225 237 141 231 245 142 232 244 138 226 236 138 226 239 142 232 247 140 233 246 136 227 238 140 228 241 144 234 249 143 235 248 139 229 240 139 230 243 143 236 251 145 237 250 141 231 242 141 231 245 145 237 253 146 238 252 142 232 244 142 232 247 146 238 255 144 239 254 140 233 246 144 234 249 148 240 257 147 241 256 143 235 248 143 236 251 147 242 259 149 243 258 145 237 250 145 237 253 149 243 261 150 244 260 146 238 252 146 238 255 150 244 263 148 245 262 144 239 254 148 240 257 152 246 265 151 247 264 147 241 256 147 242 259 151 248 267 153 249 266 149 243 258 149 243 261 153 249 269 154 250 268 150 244 260 150 244 263 154 250 271 152 251 270 148 245 262 152 246 265 156 252 273 155 253 272 151 247 264 151 248 267 155 254 276 158 255 275 157 256 274 153 249 266 153 249 269 157 256 278 159 257 277 154 250 268 154 250 271 159 257 281 160 258 280 156 259 279 152 251 270 158 255 275 162 260 283 161 261 282 157 256 274 157 256 278 161 261 285 163 262 284 159 257 277 159 257 281 163 262 287 164 263 286 160 258 280 162 260 283 166 264 289 165 265 288 161 261 282 161 261 285 165 265 291 167 266 290 163 262 284 163 262 287 167 266 293 168 267 292 164 263 286 166 264 289 170 268 295 169 269 294 165 265 288 165 265 291 169 269 297 171 270 296 167 266 290 167 266 293 171 270 299 172 271 298 168 267 292 170 268 295 173 272 300 124 208 212 127 211 213 169 269 294 169 269 297 127 211 302 129 213 301 171 270 296 171 270 299 129 213 220 123 215 221 174 273 303 172 271 298 173 274 304 174 275 305 123 204 208 124 207 209 175 276 306 178 277 309 177 278 308 176 279 307 179 280 310 175 281 313 176 282 312 180 283 311 181 284 314 179 285 317 180 286 316 182 287 315 178 288 318 181 289 321 182 290 320 177 291 319 183 292 322 186 293 325 185 294 324 184 295 323 176 279 307 177 278 308 186 293 325 183 292 322 178 277 309 175 276 306 155 253 272 156 252 273 187 296 326 183 297 330 184 298 329 189 299 328 188 300 327 180 283 311 176 282 312 183 297 330 187 296 326 175 281 313 179 280 310 173 272 300 170 268 295 166 264 289 162 260 283 158 255 275 155 254 276 190 301 331 187 302 334 188 303 333 191 304 332 182 287 315 180 286 316 187 302 334 190 301 331 179 285 317 181 284 314 174 275 305 173 274 304 186 305 335 190 306 339 191 307 338 192 308 337 185 309 336 177 291 319 182 290 320 190 306 339 186 305 335 181 289 321 178 288 318 156 259 279 160 258 280 164 263 286 168 267 292 172 271 298 174 273 303 193 143 340 194 146 343 98 310 342 99 311 341 195 147 344 193 143 340 99 311 341 102 312 345 196 149 346 195 147 344 102 312 345 104 313 347 95 151 348 196 149 346 104 313 347 106 314 349 197 153 350 95 151 348 106 314 349 108 315 351 198 155 352 197 153 350 108 315 351 110 316 353 199 157 354 198 155 352 110 316 353 112 317 355 96 159 356 199 157 354 112 317 355 114 318 357 200 161 358 96 159 356 114 318 357 116 319 359 201 163 360 200 161 363 116 319 362 118 320 361 97 165 364 201 163 360 118 320 361 120 321 365 194 146 343 97 165 364 120 321 365 98 310 342</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_lights>
    <light id="EnvironmentAmbientLight" name="EnvironmentAmbientLight">
      <technique_common>
        <ambient>
          <color>0 0 0</color>
        </ambient>
      </technique_common>
    </light>
  </library_lights>
  <library_images>
    <image id="TrayParts_png">
      <!--<init_from>trayParts.png</init_from>-->
    </image>
  </library_images>
  <library_visual_scenes>
    <visual_scene id="MaxScene">
      <node name="EnvironmentAmbientLight">
        <instance_light url="#EnvironmentAmbientLight"/>
      </node>
      <node id="node-Arm" name="Arm">
        <rotate>-1 0 0 -6.433012</rotate>
        <node>
          <matrix>1 0 0 0 0 1 0 3.295969 0 0 1 29.23217 0 0 0 1</matrix>
          <instance_geometry url="#geom-Arm">
            <bind_material>
              <technique_common>
                <instance_material symbol="_3_-_Default_1" target="#_3_-_Default-material">
                  <bind_vertex_input semantic="CHANNEL1" input_semantic="TEXCOORD" input_set="0"/>
                </instance_material>
              </technique_common>
            </bind_material>
          </instance_geometry>
        </node>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows sid="cast_shadows" type="bool">1</cast_shadows>
            <receive_shadows sid="receive_shadows" type="bool">1</receive_shadows>
            <primary_visibility sid="primary_visibility" type="int">1</primary_visibility>
            <secondary_visibility sid="secondary_visibility" type="int">1</secondary_visibility>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#MaxScene"/>
  </scene>
</COLLADA>

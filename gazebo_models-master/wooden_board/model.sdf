<?xml version="1.0" ?>

<sdf version="1.5">
  <model name="wooden_board">
    <link name="base">
      
      <pose>0 0 0.00523 0 0 0</pose>
      <inertial>
        <mass>0.2765</mass>
        <inertia>
          <ixx>0.0025148009472833336</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00024037001228333334</iyy>
          <iyz>0</iyz>
          <izz>0.002750128908333333</izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <box>
            <size>0.1016 0.3302 0.01046</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <!-- Red Pine coefficients for longitudinal axis of the wood
                 according to:
                 http://www.fpl.fs.fed.us/documnts/fplgtr/fplgtr113/ch04.pdf -->
            <poissons_ratio>0.347</poissons_ratio>
            <elastic_modulus>8.8e+09</elastic_modulus>
            <ode>
              <kp>100000</kp>
              <kd>100</kd>
              <max_vel>100.0</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
          <friction>
            <torsional>
              <coefficient>1.0</coefficient>
              <use_patch_radius>0</use_patch_radius>
              <surface_radius>0.01</surface_radius>
            </torsional>
          </friction>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <box>
            <size>0.1016 0.3302 0.01046</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
        </material>
      </visual>

      
      <visual name='tee_nut_1'>
        <pose>0.0 -0.116 0.0  0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.00635</radius>
            <length>0.0105646</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      
      <visual name='tee_nut_2'>
        <pose>0.0 -0.038 0.0  0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.00635</radius>
            <length>0.0105646</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      
      <visual name='tee_nut_3'>
        <pose>0.0 0.038 0.0  0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.00635</radius>
            <length>0.0105646</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      
      <visual name='tee_nut_4'>
        <pose>0.0 0.116 0.0  0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.00635</radius>
            <length>0.0105646</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      
    </link>

  </model>
</sdf>

<?xml version="1.0" ?>

<sdf version="1.5">
  <model name="wooden_board">
    <link name="base">
      <%
        # Wood block with dimensions 33 x 10 x 1 cm
        # SI units (length in meters)

        # Geometry
        dx = 0.1016
        dy = 0.3302
        dz = 0.01046

        # Inertia
        mass = 0.2765
        ixx  = mass/12.0 * (dy**2 + dz**2)
        iyy  = mass/12.0 * (dz**2 + dx**2)
        izz  = mass/12.0 * (dx**2 + dy**2)

        inchToMeter = 0.0254
        tee_nut_diameter = 0.5 * inchToMeter 
        tee_nut_radius = tee_nut_diameter / 2
        tee_nut_length = dz*1.01
        tee_nut_pos = {
          "tee_nut_1" => [0.0, -0.116, 0.0],
          "tee_nut_2" => [0.0, -0.038, 0.0],
          "tee_nut_3" => [0.0,  0.038, 0.0],
          "tee_nut_4" => [0.0,  0.116, 0.0],
        }
      %>
      <pose>0 0 <%= dz/2 %> 0 0 0</pose>
      <inertial>
        <mass><%= mass %></mass>
        <inertia>
          <ixx><%= ixx %></ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy><%= iyy %></iyy>
          <iyz>0</iyz>
          <izz><%= izz %></izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <box>
            <size><%= dx %> <%= dy %> <%= dz %></size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>0.1</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <box>
            <size><%= dx %> <%= dy %> <%= dz %></size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
        </material>
      </visual>

      <% tee_nut_pos.keys.each do |k|
          name = k
          pos  = tee_nut_pos[k]
      %>
      <%= "<visual name='#{name}'>" %>
        <pose><%= pos.join(' ') %>  0 0 0</pose>
        <geometry>
          <cylinder>
            <radius><%= tee_nut_radius %></radius>
            <length><%= tee_nut_length %></length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      <% end %>
    </link>

  </model>
</sdf>

<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="washer">
    <link name="link">
      <pose>0 0 0.001 0 0 0</pose>
      <inertial>
        <mass>0.0122</mass>
        <inertia>
          <ixx>1.087579166666667e-06</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1.087579166666667e-06</iyy>
          <iyz>0</iyz>
          <izz>2.1670250000000002e-06</izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <mesh>
            <uri>model://washer/meshes/washer.dae</uri>
          </mesh>
        </geometry>
        <surface>
          <contact>
            <!-- stainless steel 18-8 -->
            <poissons_ratio>0.305</poissons_ratio>
            <elastic_modulus>2.0e+11</elastic_modulus>
            <ode>
              <kp>100000</kp>
              <kd>100</kd>
              <max_vel>100.0</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
          <friction>
            <torsional>
              <coefficient>1.0</coefficient>
              <use_patch_radius>0</use_patch_radius>
              <surface_radius>0.01</surface_radius>
            </torsional>
          </friction>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://washer/meshes/washer.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>

      </visual>

    </link>
  </model>
</sdf>

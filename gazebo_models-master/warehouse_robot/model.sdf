<?xml version="1.0" ?>
<sdf version="1.6">
  <model name="warehouse_robot">
    <static>true</static>
    <link name="link">
      <visual name="visual">
        <geometry>
          <mesh>
            <uri>model://warehouse_robot/meshes/robot.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="collision_base">
        <pose>0 0 0.20437 0 0 0</pose>
        <geometry>
          <box>
            <size>0.55508 1.0228 0.40874</size>
          </box>
        </geometry>
      </collision>
      <collision name="collision_bumper">
        <pose>0 0 0.062205 0 0 0</pose>
        <geometry>
          <box>
            <size>0.67266 1.17126 0.12441</size>
          </box>
        </geometry>
      </collision>
      <collision name="collision_column_large">
        <pose>0 -0.27539 0.43738 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.08652</radius>
            <length>0.87476</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="collision_column_small">
        <pose>0 -0.27539 0.70424 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.04261</radius>
            <length>1.40848</length>
          </cylinder>
        </geometry>
      </collision>
    </link>
  </model>
</sdf>

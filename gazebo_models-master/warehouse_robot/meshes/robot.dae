<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
  <asset>
    <contributor>
      <author>ColeB</author>
      <authoring_tool>OpenCOLLADA for 3ds Max;  Version: 1.6;  Revision: 24</authoring_tool>
    </contributor>
    <created>2016-08-15T14:49:08</created>
    <modified>2016-08-15T14:49:08</modified>
    <unit name="millimeter" meter="0.001"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="MobileRobot">
      <profile_COMMON>
        <newparam sid="MobileRobot_png-surface">
          <surface type="2D">
            <init_from>MobileRobot_png</init_from>
          </surface>
        </newparam>
        <newparam sid="MobileRobot_png-sampler">
          <sampler2D>
            <source>MobileRobot_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <blinn>
            <emission>
              <color>0 0 0 1</color>
            </emission>
            <ambient>
              <color>0.5882353 0.5882353 0.5882353 1</color>
            </ambient>
            <diffuse>
              <texture texture="MobileRobot_png-sampler" texcoord="CHANNEL1"/>
            </diffuse>
            <specular>
              <color>0 0 0 1</color>
            </specular>
            <shininess>
              <float>9.999999</float>
            </shininess>
            <reflective>
              <color>0 0 0 1</color>
            </reflective>
            <transparent opaque="A_ONE">
              <color>1 1 1 1</color>
            </transparent>
            <transparency>
              <float>1</float>
            </transparency>
          </blinn>
        </technique>
      </profile_COMMON>
      <extra>
        <technique profile="OpenCOLLADA3dsMax">
          <extended_shader>
            <opacity_type sid="opacity_type" type="int">0</opacity_type>
            <falloff_type sid="falloff_type" type="int">0</falloff_type>
            <falloff sid="falloff" type="float">0</falloff>
            <index_of_refraction sid="index_of_refraction" type="float">1.5</index_of_refraction>
            <wire_size sid="wire_size" type="float">1</wire_size>
            <wire_units sid="wire_units" type="int">0</wire_units>
            <apply_reflection_dimming sid="apply_reflection_dimming" type="bool">0</apply_reflection_dimming>
            <dim_level sid="dim_level" type="float">0</dim_level>
            <reflection_level sid="reflection_level" type="float">3</reflection_level>
          </extended_shader>
          <shader>
            <ambient_diffuse_texture_lock sid="ambient_diffuse_texture_lock" type="bool">1</ambient_diffuse_texture_lock>
            <ambient_diffuse_lock sid="ambient_diffuse_lock" type="bool">1</ambient_diffuse_lock>
            <diffuse_specular_lock sid="diffuse_specular_lock" type="bool">0</diffuse_specular_lock>
            <use_self_illum_color sid="use_self_illum_color" type="bool">0</use_self_illum_color>
            <self_illumination sid="self_illumination" type="float">0</self_illumination>
            <specular_level sid="specular_level" type="float">0</specular_level>
            <soften sid="soften" type="float">0.1</soften>
          </shader>
        </technique>
      </extra>
    </effect>
  </library_effects>
  <library_materials>
    <material id="MobileRobot-material" name="MobileRobot">
      <instance_effect url="#MobileRobot"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="geom-Line002" name="Line002">
      <spline closed="0">
        <source id="geom-Line002-positions">
          <float_array id="geom-Line002-positions-array" count="18">47.5387 11.30841 0 -1.713394 11.30841 0 -4.704048 10.37383 0 -10.87227 1.588785 0 -14.7975 -11.12149 0 -15.73209 -24.11218 0</float_array>
          <technique_common>
            <accessor source="#geom-Line002-positions-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Line002-intangents">
          <float_array id="geom-Line002-intangents-array" count="18">47.5387 11.30841 0 14.70397 11.30841 0 -3.707164 10.68536 0 -8.816198 4.517135 0 -13.48909 -6.884735 0 -15.42056 -19.78195 0</float_array>
          <technique_common>
            <accessor source="#geom-Line002-intangents-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Line002-outtangents">
          <float_array id="geom-Line002-outtangents-array" count="18">31.12134 11.30841 0 -2.710279 10.99689 0 -6.760123 7.445484 0 -12.18068 -2.647975 0 -15.10903 -15.45172 0 -15.73209 -24.11218 0</float_array>
          <technique_common>
            <accessor source="#geom-Line002-outtangents-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Line002-interpolations">
          <Name_array id="geom-Line002-interpolations-array" count="6">BEZIER BEZIER BEZIER BEZIER BEZIER BEZIER</Name_array>
          <technique_common>
            <accessor source="#geom-Line002-interpolations-array" count="6" stride="1">
              <param name="INTERPOLATION" type="name"/>
            </accessor>
          </technique_common>
        </source>
        <control_vertices>
          <input semantic="POSITION" source="#geom-Line002-positions"/>
          <input semantic="IN_TANGENT" source="#geom-Line002-intangents"/>
          <input semantic="OUT_TANGENT" source="#geom-Line002-outtangents"/>
          <input semantic="INTERPOLATION" source="#geom-Line002-interpolations"/>
        </control_vertices>
      </spline>
    </geometry>
    <geometry id="geom-Box002" name="Box002">
      <mesh>
        <source id="geom-Box002-positions">
          <float_array id="geom-Box002-positions-array" count="1017">283.4061 431.7179 0 307.5594 -431.186 26.6574 -8.86397e-5 -544.0664 0 197.2135 -511.6386 0 195.6808 534.4603 26.63802 237.0293 420.1648 395.9989 261.1552 -374.1783 395.9963 -8.01714e-5 -492.0882 396.0041 8.01714e-5 492.0882 396.0041 174.4254 463.4072 396.0056 174.4253 -463.4073 396.0056 253.0755 431.8213 357.492 277.5355 -386.329 357.4823 176.64 482.3522 357.5222 173.6933 -422.3893 408.7252 198.8979 400.192 408.7313 220.9026 -351.4906 408.7366 7.34689e-5 450.9487 408.7253 173.6935 422.3893 408.7252 176.6398 -482.3523 357.5222 283.0981 476.6762 26.6574 268.2933 -459.8227 0 261.1553 374.1782 395.9963 237.0291 -420.1649 395.9989 277.5356 386.329 357.4823 253.0754 -431.8214 357.492 220.9028 351.4906 408.7366 198.8978 -400.1921 408.7313 280.3023 448.1645 0 301.444 -457.2196 26.6574 255.1238 400.3358 395.997 255.1237 -400.3359 395.997 271.4206 412.363 357.4847 271.4205 -412.3631 357.4847 215.4016 378.3269 408.7352 215.4014 -378.327 408.7352 307.5595 431.1859 26.6574 283.406 -431.718 0 -9.23167e-5 -566.6356 26.64123 195.6806 -534.4604 26.63802 197.2136 511.6386 0 268.2934 459.8226 0 283.0979 -476.6763 26.6574 301.4442 457.2195 26.6574 280.3022 -448.1646 0 302.7267 433.1239 6.66435 302.7266 -433.1241 6.66435 -9.17591e-5 -563.213 6.660308 196.8302 -530.8484 6.659506 196.8304 530.8483 6.659506 280.6024 474.2679 6.66435 280.6023 -474.2679 6.66435 297.3642 456.7608 6.66435 297.3641 -456.7609 6.66435 238.9133 537.273 124.4086 119.9436 573.6125 124.4086 321.0284 479.3466 124.4086 336.3321 444.3692 124.4086 289.9762 436.8393 124.4086 282.3503 450.2323 124.4086 216.569 493.7169 124.4086 110.2867 526.0381 124.4086 336.3321 444.3692 33.53983 289.9762 436.8393 33.53983 321.0284 479.3466 33.53983 282.3503 450.2323 33.53983 238.9133 537.273 33.53983 216.569 493.7169 33.53983 119.9436 573.6125 33.53983 110.2867 526.0381 33.53983 336.332 -444.3694 124.4086 289.976 -436.8394 124.4086 321.0282 -479.3466 124.4086 282.3502 -450.2325 124.4086 238.9131 -537.2731 124.4086 216.5688 -493.717 124.4086 119.9434 -573.6125 124.4086 110.2866 -526.0381 124.4086 336.332 -444.3694 33.53983 289.976 -436.8394 33.53983 321.0282 -479.3466 33.53983 282.3502 -450.2325 33.53983 238.9131 -537.2731 33.53983 216.5688 -493.717 33.53983 119.9434 -573.6125 33.53983 110.2866 -526.0381 33.53983 63.35174 -385.1235 404.6679 109.7307 -338.7446 404.6679 126.7066 -275.3898 404.6679 109.7307 -212.035 404.6679 63.35181 -165.6561 404.6679 43.26025 -350.324 874.7576 74.93121 -318.6531 874.7576 86.52359 -275.3898 874.7576 74.93124 -232.1265 874.7576 43.2603 -200.4555 874.7576 47.10745 -356.9876 424.0307 81.59475 -322.5003 424.0307 94.21797 -275.3898 424.0307 81.59477 -228.2793 424.0307 47.1075 -193.792 424.0307 43.26025 -350.324 451.4241 74.93121 -318.6531 451.4241 86.52359 -275.3898 451.4241 74.93124 -232.1265 451.4241 43.2603 -200.4555 451.4241 21.3045 -312.2956 874.7576 36.90272 -296.6973 874.7576 42.61206 -275.3898 874.7576 36.90274 -254.0822 874.7576 21.30452 -238.484 874.7576 21.3045 -312.2956 1243.024 36.90272 -296.6973 1243.024 42.61206 -275.3898 1243.024 36.90274 -254.0822 1243.024 21.30452 -238.484 1243.024 29.90721 -327.1959 1243.024 51.80305 -305.3 1243.024 59.81749 -275.3898 1243.024 51.80307 -245.4795 1243.024 29.90723 -223.5837 1243.024 33.14127 -332.7974 1290.042 57.40461 -308.5341 1290.042 66.28561 -275.3898 1290.042 57.40464 -242.2455 1290.042 33.1413 -217.9821 1290.042 33.14127 -332.7974 1312.124 57.40461 -308.5341 1312.124 66.28561 -275.3898 1312.124 57.40464 -242.2455 1312.124 33.1413 -217.9821 1312.124 29.91547 -327.2103 1312.125 51.81741 -305.3084 1312.125 59.8341 -275.3898 1312.125 51.81745 -245.4712 1312.125 29.91554 -223.5693 1312.125 29.91553 -275.3898 1400.462 25.9072 -260.4305 1400.462 14.95624 -249.4795 1400.462 14.95621 -301.3 1400.462 25.90718 -290.3491 1400.462 51.81745 -275.3898 1378.56 44.87482 -249.4795 1378.56 25.90721 -230.5119 1378.56 25.90715 -320.2677 1378.56 44.87479 -301.3001 1378.56 59.8341 -275.3898 1348.642 51.81745 -245.4712 1348.642 29.91554 -223.5693 1348.642 29.91547 -327.2103 1348.642 51.81741 -305.3084 1348.642 8.75642e-5 537.4651 33.53983 9.54114e-5 585.6312 33.53983 -8.75642e-5 -537.4651 124.4086 -8.75642e-5 -537.4651 33.53983 -6.02171e-5 -369.61 424.0307 -2.95163e-5 -181.1696 424.0307 -3.07698e-5 -188.8639 451.4241 -3.07698e-5 -188.8639 874.7576 -5.18094e-5 -318.0041 874.7576 -3.79239e-5 -232.7755 874.7576 -5.18094e-5 -318.0041 1243.025 -5.56664e-5 -341.6777 1290.042 -3.4067e-5 -209.1019 1290.042 -5.56664e-5 -341.6777 1312.124 -5.46153e-5 -335.2261 1312.125 -3.64242e-5 -223.5701 1378.56 -5.33092e-5 -327.2094 1378.56 8.86397e-5 544.0664 0 -197.2135 511.6386 0 -197.2136 -511.6386 0 -215.4014 378.327 408.7352 -198.8978 400.192 408.7313 -173.6933 422.3893 408.7252 4.97408e-5 305.3071 408.7253 -268.2933 459.8227 0 -280.3022 448.1646 0 -283.406 431.718 0 -283.4061 -431.7179 0 -280.3023 -448.1645 0 -268.2934 -459.8226 0 -261.1553 -374.1782 395.9963 -277.5356 -386.329 357.4823 -277.5355 386.329 357.4823 -261.1552 374.1783 395.9963 -237.0291 420.1649 395.9989 -253.0754 431.8214 357.492 -176.6398 482.3523 357.5222 -174.4253 463.4073 396.0056 -8.33174e-5 -511.3984 357.5151 -176.64 -482.3522 357.5222 -174.4254 -463.4072 396.0056 8.33174e-5 511.3984 357.5151 -253.0755 -431.8213 357.492 -237.0293 -420.1648 395.9989 -220.9028 -351.4906 408.7366 -220.9027 351.4906 408.7366 -7.34689e-5 -450.9487 408.7253 -173.6935 -422.3893 408.7252 -198.8979 -400.192 408.7313 -307.5595 -431.1859 26.6574 -307.5594 431.186 26.6574 -283.0979 476.6763 26.6574 -195.6806 534.4604 26.63802 -195.6808 -534.4603 26.63802 9.23167e-5 566.6356 26.64123 -283.0981 -476.6762 26.6574 -255.1237 400.3359 395.997 -255.1238 -400.3358 395.997 -271.4206 -412.363 357.4847 -215.4016 -378.3269 408.7352 -271.4205 412.3631 357.4847 -301.444 457.2196 26.6574 -301.4442 -457.2195 26.6574 -302.7266 433.1241 6.66435 -302.7267 -433.1239 6.66435 -280.6024 -474.2679 6.66435 -196.8304 -530.8483 6.659506 9.17591e-5 563.213 6.660308 -196.8302 530.8484 6.659506 -280.6023 474.2679 6.66435 -297.3641 456.7609 6.66435 -297.3642 -456.7608 6.66435 -336.332 444.3694 124.4086 -289.976 436.8394 124.4086 -289.9762 -436.8393 124.4086 -336.3321 -444.3693 124.4086 -321.0282 479.3467 124.4086 -282.3502 450.2324 124.4086 -238.9131 537.2731 124.4086 -216.5688 493.717 124.4086 -119.9434 573.6125 124.4086 -110.2866 526.0381 124.4086 -321.0282 479.3467 33.53983 -238.9131 537.2731 33.53983 -119.9434 573.6125 33.53983 -336.332 444.3694 33.53983 -336.3321 -444.3693 33.53983 -289.9762 -436.8393 33.53983 -289.976 436.8394 33.53983 -282.3502 450.2324 33.53983 -216.5688 493.717 33.53983 -110.2866 526.0381 33.53983 -321.0284 -479.3465 124.4086 -282.3503 -450.2323 124.4086 -238.9133 -537.2729 124.4086 -216.569 -493.7169 124.4086 -119.9436 -573.6125 124.4086 -110.2867 -526.0381 124.4086 -238.9133 -537.2729 33.53983 -321.0284 -479.3465 33.53983 -119.9436 -573.6125 33.53983 -282.3503 -450.2323 33.53983 -216.569 -493.7169 33.53983 -110.2867 -526.0381 33.53983 -63.35186 -385.1235 404.6679 -109.7308 -338.7446 404.6679 -81.59486 -322.5003 424.0307 -47.10756 -356.9876 424.0307 -126.7067 -275.3898 404.6679 -94.21806 -275.3898 424.0307 -109.7308 -212.035 404.6679 -81.59485 -228.2793 424.0307 -63.35186 -165.6561 404.6679 -47.10756 -193.792 424.0307 -2.42232e-5 -148.681 404.6679 -43.26037 -350.324 451.4241 -43.26037 -350.324 874.7576 -5.89635e-5 -361.9156 874.7576 -5.89635e-5 -361.9156 451.4241 -74.93132 -318.6531 451.4241 -74.93132 -318.6531 874.7576 -86.52368 -275.3898 451.4241 -86.52368 -275.3898 874.7576 -74.93131 -232.1265 451.4241 -74.93131 -232.1265 874.7576 -43.26037 -200.4555 451.4241 -43.26037 -200.4555 874.7576 -21.3046 -312.2956 874.7576 -36.90282 -296.6973 874.7576 -42.61216 -275.3898 874.7576 -36.90282 -254.0822 874.7576 -21.3046 -238.484 874.7576 -36.90282 -296.6973 1243.024 -21.3046 -312.2956 1243.024 -42.61216 -275.3898 1243.024 -36.90282 -254.0822 1243.024 -21.3046 -238.484 1243.024 -3.79239e-5 -232.7755 1243.024 -51.80315 -305.3 1243.024 -29.90732 -327.1959 1243.024 -59.81758 -275.3898 1243.024 -51.80315 -245.4795 1243.024 -29.90731 -223.5837 1243.024 -3.51208e-5 -215.57 1243.024 -57.40471 -308.5341 1290.042 -33.14138 -332.7974 1290.042 -66.28571 -275.3898 1290.042 -57.40471 -242.2455 1290.042 -33.14137 -217.9821 1290.042 -57.40471 -308.5341 1312.124 -33.14138 -332.7974 1312.124 -66.28571 -275.3898 1312.124 -57.40471 -242.2455 1312.124 -33.14137 -217.9821 1312.124 -3.4067e-5 -209.1019 1312.124 -51.81751 -305.3084 1312.125 -29.91558 -327.2103 1312.125 -59.83419 -275.3898 1312.125 -51.81753 -245.4712 1312.125 -29.91561 -223.5693 1312.125 -3.51181e-5 -215.5534 1312.125 -29.91562 -275.3898 1400.462 -51.81754 -275.3898 1378.56 -44.8749 -249.4795 1378.56 -25.90728 -260.4305 1400.462 -25.90728 -230.5119 1378.56 -14.95632 -249.4795 1400.462 -3.99925e-5 -245.472 1400.462 -14.95631 -301.3 1400.462 -25.90725 -320.2677 1378.56 -44.87489 -301.3001 1378.56 -25.90727 -290.3491 1400.462 -59.83419 -275.3898 1348.642 -51.81753 -245.4712 1348.642 -29.91561 -223.5693 1348.642 -3.51181e-5 -215.5534 1348.642 -29.91558 -327.2103 1348.642 -51.81751 -305.3084 1348.642 -5.46153e-5 -335.2261 1348.642 -4.5256e-5 -277.7794 408.7253 8.75642e-5 537.4651 124.4086 9.54114e-5 585.6312 124.4086 -9.54114e-5 -585.6312 124.4086 -9.54114e-5 -585.6312 33.53983 -6.55102e-5 -402.0986 404.6679 -5.46126e-5 -335.2095 1243.024 -4.97409e-5 -305.3075 1400.462 -4.48667e-5 -275.3898 1408.478</float_array>
          <technique_common>
            <accessor source="#geom-Box002-positions-array" count="339" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Box002-normals">
          <float_array id="geom-Box002-normals-array" count="1341">6.51725e-8 0.1832956 -0.9830577 0.08567227 0.179448 -0.9800299 0.08567226 -0.1794479 -0.9800299 -6.63794e-8 -0.1832956 -0.9830579 0.1415943 0.06740554 0.9876272 0.1111256 0.1239059 0.986052 0.08340129 0.1654007 0.9826937 1.73701e-11 1.31144e-5 0.9999999 0.1187167 0.1414517 -0.9828009 0.171189 0.09336873 -0.9808041 0.1750483 0.01871295 -0.984382 0.1750483 -0.018713 -0.984382 0.1711888 -0.09336887 -0.9808041 0.1187166 -0.1414518 -0.9828009 0.6703061 -0.07007451 0.7387687 0.9583075 -0.113325 0.2623052 0.9583076 0.1133248 0.2623052 0.6703062 0.07007439 0.7387687 0.4388087 0.5022541 0.7451093 0.6196516 0.7244902 0.3019038 0.3440626 0.8894491 0.3008342 0.25674 0.6329692 0.7303661 -1.13192e-7 -0.6688335 0.7434122 -1.97057e-7 -0.9521688 0.3055725 0.3440624 -0.8894492 0.3008342 0.2567399 -0.6329694 0.730366 1.87204e-7 0.9521688 0.3055725 1.11861e-7 0.6688335 0.7434122 0.6196513 -0.7244904 0.3019038 0.4388087 -0.5022542 0.7451091 0.1562322 -0.0121742 0.9876453 0.1562336 0.0121737 0.9876451 -2.77495e-8 -0.1644571 0.9863842 0.08339689 -0.1653996 0.9826943 2.69435e-8 0.164457 0.9863843 0.1111212 -0.1238992 0.9860533 0.9917363 -0.1143766 -0.05811163 0.9917363 0.1143765 -0.05811164 0.6439555 0.7650593 0.002385311 0.3584632 0.933539 0.003027943 -2.72733e-7 -0.9999979 -0.002087526 0.358463 -0.9335391 0.003027698 2.65295e-7 0.9999979 -0.002087521 0.6439548 -0.7650599 0.002384873 0.5952128 0.3040435 0.7438273 0.5952129 -0.3040436 0.7438273 0.843062 -0.4479598 0.2976216 0.1415926 -0.06740073 0.9876279 0.8430621 0.4479594 0.2976216 0.8804228 0.4735655 -0.02432165 0.8804224 -0.4735662 -0.02432181 0.7550467 0.08931881 -0.6495588 0.7550467 -0.08931893 -0.6495588 0.50642 -0.6028615 -0.6165199 0.2936954 -0.7382272 -0.6072591 2.54283e-7 0.7833406 -0.6215927 0.2936957 0.7382273 -0.6072588 -2.48504e-7 -0.7833406 -0.6215927 0.506421 0.6028613 -0.6165192 0.6893039 0.3791805 -0.6173187 0.6893033 -0.3791814 -0.6173187 0 0 1 0 0 1 0 0 1 0 0 0.9999999 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999999 0.4398271 0.8980825 0 0.7747757 0.6322362 0 0.7747757 0.6322362 0 0.4398271 0.8980825 0 0.1968659 0.9804304 0 0.1968659 0.9804304 0 0 0 -1 0 0 -0.9999999 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999999 0.9788123 0.2047599 0 0.9788123 0.2047599 0 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0.4398268 -0.8980826 0 0.4398268 -0.8980826 0 0.7747749 -0.6322371 0 0.7747749 -0.6322371 0 0.1968657 -0.9804304 0 0.1968657 -0.9804304 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0.978812 -0.2047612 0 0.978812 -0.2047612 0 0.2559797 -0.4433702 0.8590095 0.4433699 -0.2559799 0.8590096 0.6658629 -0.3844363 0.6394022 0.3844361 -0.6658632 0.6394022 0.5119597 -1.11694e-7 0.8590096 0.7688726 -3.72068e-7 0.639402 0.4433701 0.2559797 0.8590094 0.6658635 0.3844361 0.6394019 0.2559799 0.44337 0.8590096 0.3844367 0.6658632 0.6394018 1.34033e-7 0.5119596 0.8590096 1.68671e-7 0.7688725 0.6394021 0.4951018 -0.8575414 0.1396309 0.5000001 -0.8660253 0 9.82102e-9 -1 0 -4.84268e-9 -0.9902036 0.1396307 0.8575414 -0.4951018 0.1396311 0.8660254 -0.5000001 0 0.9902036 -4.84268e-8 0.1396308 1 -1.86599e-7 0 0.8575417 0.4951015 0.1396307 0.8660255 0.4999997 0 0.4951021 0.8575413 0.1396306 0.5000004 0.8660252 0 -7.44137e-9 -0.7688727 0.6394019 0 0 1 0 0 1 0 0 1 0 0 0.9999999 0 0 1 0 0 0.9999999 0 0 1 0 0 1 0 0 0.9999999 0 0 1 0 0 1 0 0 1 0.5000006 -0.866025 0 0.8660254 -0.4999998 0 0.8660254 -0.4999998 0 0.5000002 -0.8660253 0 0.9999999 -2.45525e-7 0 0.9999999 -2.45525e-7 0 0.8660254 0.5 0 0.8660254 0.5 0 0.5000002 0.8660253 0 0.5000002 0.8660253 0 0 1 0 0 1 0 -1.58725e-6 1.47577e-6 -0.9999999 0 0 -1 0 0 -1 -1.58725e-6 1.47577e-6 -1 0 0 -0.9999999 0 0 -1 0 0 -0.9999999 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0.4953351 -0.8579451 -0.1362839 0.8579451 -0.4953351 -0.1362838 0.8640474 -0.4988581 -0.0675469 0.4988585 -0.8640471 -0.06754693 0.9906698 -1.63188e-7 -0.136284 0.9977161 -1.74173e-7 -0.06754701 0.8579454 0.4953346 -0.136284 0.8640476 0.4988576 -0.06754701 0.4953353 0.8579451 -0.136284 0.4988584 0.8640472 -0.06754701 0 0.9906698 -0.136284 1.99055e-8 0.997716 -0.067547 0.8660253 -0.5000001 0 0.5000007 -0.866025 0 0.9999999 -2.06241e-7 0 0.8660256 0.4999996 0 0.5000004 0.8660251 0 1.9642e-8 1 0 9.46065e-5 -1.63862e-4 1 1.63863e-4 -9.46058e-5 1 1.63862e-4 -9.46065e-5 0.9999999 9.46059e-5 -1.63863e-4 1 1.89212e-4 -3.61297e-11 1 1.89212e-4 -4.96288e-11 0.9999999 1.63862e-4 9.46058e-5 1 1.63862e-4 9.4606e-5 0.9999999 9.46061e-5 1.63862e-4 0.9999999 9.4606e-5 1.63862e-4 1 2.77921e-12 1.89212e-4 0.9999999 -5.95545e-12 1.89212e-4 1 0.5308993 -9.13098e-8 0.8474349 0.8759835 -2.09269e-7 0.4823411 0.758624 0.4379917 0.4823411 0.4597709 0.2654496 0.8474357 0.4379919 0.7586238 0.4823411 0.2654465 0.4597689 0.8474377 1.04635e-8 0.8759835 0.4823412 6.34098e-9 0.5308928 0.8474389 0.2654456 -0.4597695 0.8474377 0.4379908 -0.7586246 0.4823411 0.7586237 -0.4379924 0.4823411 0.4597706 -0.2654499 0.8474357 0.9917965 -2.32543e-7 0.1278264 0.8589211 0.495898 0.1278263 0.4958986 0.8589208 0.1278264 1.51659e-8 0.9917966 0.1278265 0.4958973 -0.8589215 0.1278265 0.8589206 -0.495899 0.1278264 1 -2.16062e-7 0 0.8660255 0.4999998 0 0.5000004 0.8660251 0 -1.01106e-8 -0.9917967 0.1278266 0 -0.9999999 4.19042e-7 0.4999992 -0.8660258 2.09521e-7 0.866025 -0.5000008 0 -6.00715e-12 -1.17583e-5 1 0 0 1 0 0 1 0 0 -1 0 0 -1 1.43008e-8 1 0 1.43008e-8 1 0 0 0 1 0 0 1 0 0 -1 0 0 -1 -1.43008e-8 -1 0 -1.43008e-8 -1 0 -1.11694e-8 -0.5119596 0.8590095 0 1 0 7.74828e-8 0.9902036 0.1396307 0 0 1 0 0 1 0 -1 0 0 -1 0 -3.72216e-13 2.95153e-6 -1 3.47401e-13 2.95153e-6 -0.9999999 -1.91986e-8 -0.9906698 -0.1362842 -2.98582e-8 -0.9977161 -0.06754708 -3.92841e-8 -0.9999999 0 -5.55843e-12 -1.89212e-4 0.9999999 1.98515e-12 -1.89212e-4 1 -1.01998e-8 7.07613e-8 1 -5.07278e-9 -0.5308927 0.8474392 -1.04635e-8 -0.8759835 0.4823411 3.92841e-8 1 0 -0.08567227 -0.179448 -0.9800299 -0.08567226 0.1794479 -0.9800299 -0.1415943 0.06740554 0.9876272 -0.0834012 0.1654007 0.9826937 -0.1111255 0.1239059 0.9860519 -0.1187167 -0.1414517 -0.9828009 -0.171189 -0.09336873 -0.9808041 -0.1750483 -0.01871295 -0.984382 -0.1750483 0.018713 -0.984382 -0.1711888 0.09336886 -0.9808041 -0.1187166 0.1414518 -0.9828009 -0.6703061 -0.07007439 0.7387687 -0.6703061 0.07007451 0.7387688 -0.9583075 0.113325 0.2623053 -0.9583076 -0.1133248 0.2623052 -0.4388086 0.5022542 0.7451091 -0.2567398 0.6329693 0.7303661 -0.3440624 0.8894492 0.3008342 -0.6196513 0.7244904 0.3019038 -0.25674 -0.6329693 0.7303659 -0.3440626 -0.8894491 0.3008341 -0.4388087 -0.5022541 0.7451093 -0.6196517 -0.7244902 0.3019038 -0.1562323 -0.01217417 0.9876453 -0.1562335 0.01217374 0.9876451 -0.08339696 -0.1653995 0.9826943 -0.1111212 -0.1238991 0.9860533 -0.9917363 0.1143767 -0.05811163 -0.9917363 -0.1143765 -0.05811164 -0.3584629 0.933539 0.00302771 -0.6439547 0.7650599 0.00238487 -0.3584632 -0.933539 0.003027973 -0.6439555 -0.7650593 0.002385326 -0.5952129 0.3040437 0.7438274 -0.8430621 -0.4479594 0.2976216 -0.595213 -0.3040435 0.7438274 -0.1415925 -0.06740072 0.9876278 -0.8430619 0.4479598 0.2976217 -0.8804224 0.4735663 -0.02432181 -0.8804228 -0.4735655 -0.02432165 -0.7550467 -0.08931881 -0.6495588 -0.7550467 0.08931894 -0.6495588 -0.2936957 -0.7382273 -0.6072588 -0.506421 -0.6028613 -0.6165192 -0.2936954 0.7382272 -0.607259 -0.50642 0.6028615 -0.6165199 -0.6893033 0.3791814 -0.6173187 -0.6893039 -0.3791805 -0.6173187 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 0 0 1 -0.4398266 0.8980827 0 -0.4398266 0.8980827 0 -0.7747753 0.6322368 0 -0.7747753 0.6322368 0 -0.1968654 0.9804305 0 -0.1968654 0.9804305 0 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -1 -0.9788122 0.2047607 0 -0.9788122 0.2047607 0 0 0 0.9999999 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999999 -0.4398272 -0.8980824 0 -0.7747754 -0.6322366 0 -0.7747754 -0.6322366 0 -0.4398272 -0.8980824 0 -0.1968662 -0.9804303 0 -0.1968662 -0.9804303 0 0 0 -0.9999999 0 0 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999999 -0.9788121 -0.2047603 0 -0.9788121 -0.2047603 0 -0.2559798 -0.4433701 0.8590095 -0.3844362 -0.6658632 0.6394021 -0.6658631 -0.3844362 0.6394022 -0.4433702 -0.2559797 0.8590094 -0.7688726 1.0914e-7 0.6394019 -0.5119597 5.58472e-8 0.8590094 -0.6658633 0.3844362 0.639402 -0.4433701 0.2559798 0.8590094 -0.3844363 0.6658632 0.639402 -0.2559798 0.44337 0.8590095 -0.495102 -0.8575414 0.139631 -0.5000002 -0.8660253 0 -0.8575416 -0.4951017 0.139631 -0.8660254 -0.4999998 0 -0.9902036 1.11382e-7 0.1396308 -1 8.83892e-8 0 -0.8575416 0.4951018 0.1396308 -0.8660254 0.5 0 -0.4951019 0.8575414 0.1396308 -0.5000001 0.8660254 0 0 0 1 0 0 1 0 0 1 0 0 1 0 0 0.9999999 0 0 1 0 0 0.9999999 0 0 1 0 0 1 0 0 1 -0.5000006 -0.866025 0 -0.5000002 -0.8660253 0 -0.8660257 -0.4999996 0 -0.8660257 -0.4999996 0 -0.9999999 9.82102e-8 0 -0.9999999 9.82102e-8 0 -0.8660253 0.5000002 0 -0.8660253 0.5000002 0 -0.5 0.8660254 0 -0.5 0.8660254 0 1.58725e-6 1.47577e-6 -1 1.58725e-6 1.47577e-6 -1 0 0 -1 0 0 -1 0 0 -1 0 0 -0.9999999 0 0 -1 0 0 -0.9999999 0 0 -1 0 0 -1 -0.4953352 -0.857945 -0.1362839 -0.4988586 -0.8640471 -0.06754693 -0.8640475 -0.4988579 -0.06754689 -0.8579453 -0.4953349 -0.1362838 -0.997716 1.99055e-8 -0.06754699 -0.9906698 5.75958e-8 -0.136284 -0.8640474 0.4988582 -0.06754702 -0.8579452 0.495335 -0.136284 -0.4988581 0.8640474 -0.06754699 -0.495335 0.8579452 -0.136284 -0.5000008 -0.866025 0 -0.8660254 -0.4999999 0 -1 0 0 -0.8660254 0.5000001 0 -0.5 0.8660254 0 -9.46065e-5 -1.63862e-4 1 -9.4606e-5 -1.63863e-4 1 -1.63862e-4 -9.46065e-5 1 -1.63863e-4 -9.46058e-5 1 -1.89212e-4 0 0.9999999 -1.89212e-4 -2.77921e-12 1 -1.63862e-4 9.46061e-5 1 -1.63862e-4 9.46059e-5 1 -9.4606e-5 1.63862e-4 0.9999999 -9.4606e-5 1.63862e-4 1 -0.5308993 9.00416e-8 0.8474349 -0.4597707 0.2654496 0.8474357 -0.758624 0.4379919 0.4823412 -0.8759835 5.23174e-8 0.4823411 -0.2654464 0.459769 0.8474377 -0.4379917 0.758624 0.4823412 -0.2654456 -0.4597695 0.8474377 -0.4597707 -0.2654499 0.8474357 -0.7586237 -0.4379923 0.4823411 -0.4379908 -0.7586245 0.4823411 -0.8589209 0.4958985 0.1278264 -0.9917965 2.02212e-8 0.1278264 -0.4958983 0.858921 0.1278265 -0.8589206 -0.4958988 0.1278264 -0.4958974 -0.8589215 0.1278265 -0.8660253 0.5000002 0 -1 9.82102e-9 0 -0.5 0.8660254 0 -0.4999993 -0.8660257 2.09521e-7 -0.866025 -0.5000006 0</float_array>
          <technique_common>
            <accessor source="#geom-Box002-normals-array" count="447" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="geom-Box002-map1">
          <float_array id="geom-Box002-map1-array" count="1545">0.0576345 0.6875871 0 0.07879964 0.578548 0 0.6077944 0.5785071 0 0.6289622 0.6875351 0 0.02665024 0.4729064 0 0.0466162 0.3489936 0 0.6943195 0.3489933 0 0.7142867 0.4729064 0 0.07974257 0.3049157 0 0.08709828 0.2975892 0 0.09731819 0.2955278 0 0.6436136 0.2955279 0 0.1072123 0.5250088 0 0.09672316 0.499353 0 0.6538335 0.2975899 0 0.6611893 0.3049168 0 0.589517 0.4988416 0 0.5789781 0.5246109 0 0.606119 0.5387867 0 0.6244232 0.5174533 0 0.09634027 0.2828323 0 0.6445906 0.2828326 0 0.6704601 0.2966487 0 0.706848 0.3488166 0 0.6601922 0.5684281 0 0.6340464 0.5765772 0 0.02969433 0.6862143 0 0.001999999 0.6823123 0 0.02655927 0.5686701 0 0.05262525 0.5767385 0 0.6569244 0.6861637 0 0.6846492 0.6822202 0 0.06201906 0.5180103 0 0.08026119 0.5391866 0 0.1217191 0.550795 0 0.5644531 0.5503446 0 0.5939005 0.5632464 0 0.0924371 0.5634799 0 0.2228708 0.002373723 0 0.7650204 0.002000003 0 0.7985594 0.004034118 0 0.8638443 0.01285352 0 0.001999999 0.03873819 0 0.1240613 0.01336296 0 0.9859399 0.03806024 0 0.1893344 0.004453721 0 0.5805847 0.5534238 0 0.594071 0.5286555 0 0.09218647 0.5290558 0 0.07754706 0.505367 0 0.1056027 0.5538146 0 0.6086701 0.5047298 0 0.7818275 0.002395667 0 0.2060641 0.002792196 0 0.01455035 0.4729068 0 0.03408873 0.3488175 0 0.7263874 0.4729066 0 0.07047099 0.2966474 0 0.09605526 0.269929 0 0.08175056 0.2866997 0 0.07678454 0.2754165 0 0.6796567 0.2880234 0 0.6591804 0.2867005 0 0.6641462 0.2754173 0 0.06127388 0.288022 0 0.6448756 0.2699295 0 0.7187087 0.3456154 0 0.001999999 0.4729068 0 0.0222281 0.3456169 0 0.7389381 0.4729068 0 0.4420073 0.8120461 0 0.4659148 0.8258489 0 0.4619178 0.7921353 0 0.4757209 0.8160427 0 0.4891163 0.7848475 0 0.4891163 0.8124535 0 0.5163146 0.7921353 0 0.5025117 0.8160427 0 0.2247705 0.9077243 0 0.2247705 0.8781944 0 0.7731364 0.8781944 0 0.7731364 0.9077243 0 0.2059143 0.9077243 0 0.2059143 0.8781944 0 0.1483823 0.9077243 0 0.1483823 0.8781944 0 0.07414319 0.9077243 0 0.07414319 0.8781944 0 0.2059143 0.9644052 0 0.1483823 0.9644052 0 0.07414319 0.9644052 0 0.2247705 0.9644052 0 0.7731364 0.9644052 0 0.7731364 0.9939352 0 0.2247705 0.9939352 0 0.2059143 0.9939352 0 0.1483823 0.9939352 0 0.07414319 0.9939352 0 0.7919909 0.9077243 0 0.7919909 0.8781944 0 0.8495207 0.9077243 0 0.8495207 0.8781944 0 0.923758 0.9077243 0 0.923758 0.8781944 0 0.8495207 0.9644052 0 0.7919909 0.9644052 0 0.923758 0.9644052 0 0.5362254 0.8120461 0 0.5123178 0.8258489 0 0.7919909 0.9939352 0 1.200759 0.7987549 0 0.8495207 0.9939352 0 1.191392 0.7933465 0 1.210565 0.7889487 0 1.205157 0.7795811 0 1.22396 0.7853595 0 1.22396 0.7745426 0 1.237356 0.7889487 0 1.242764 0.7795811 0 0.923758 0.9939352 0 1.247162 0.7987549 0 1.256529 0.7933465 0 0.9432102 0.6259235 0 0.936882 0.6048945 0 0.9075588 0.6259235 0 0.905335 0.6048945 0 0.8735077 0.6259234 0 0.8735077 0.6048945 0 0.8394575 0.6259234 0 0.8416815 0.6048945 0 0.803807 0.6259235 0 0.8101355 0.6048945 0 0.9321227 0.5887973 0 0.9321227 0.3235857 0 0.9031684 0.5887973 0 0.9031684 0.3235857 0 0.8735077 0.5887973 0 0.8735078 0.3235857 0 0.8438482 0.5887971 0 0.8438482 0.3235856 0 0.8148952 0.5887973 0 0.8148952 0.3235857 0 0.5868028 0.821835 0 0.8183215 0.8218351 0 0.5868028 0.8079671 0 0.8183215 0.8079671 0 0.5868028 0.794099 0 0.8183215 0.794099 0 0.5868028 0.7802309 0 0.8183215 0.7802309 0 0.5868028 0.7663629 0 0.8183215 0.7663629 0 0.8696885 0.7867707 0 0.8696885 0.8149711 0 0.8893406 0.7867707 0 0.8893406 0.8149711 0 0.9088447 0.7867707 0 0.9088447 0.8149711 0 0.9284832 0.7867707 0 0.9284832 0.8149711 0 0.3200937 0.7410222 0 0.3236061 0.7430503 0 0.3353472 0.7257686 0 0.337375 0.7292809 0 0.3561843 0.7201852 0 0.3561843 0.7242411 0 0.9481178 0.7867707 0 0.9481178 0.8149711 0 0.8696885 0.8331203 0 0.8893406 0.8331203 0 0.9088447 0.8331203 0 0.9284832 0.8331203 0 0.9481178 0.8331203 0 0.08003782 0.7756385 0 0.08918547 0.7777145 0 0.09747633 0.7831694 0 0.06187894 0.7815809 0 0.0707168 0.7768605 0 0.08042681 0.7588475 0 0.0944105 0.7609736 0 0.1085348 0.7667583 0 0.05193092 0.765166 0 0.06635094 0.7601733 0 0.08092493 0.7421283 0 0.09834547 0.744223 0 0.1159735 0.7495114 0 0.5362443 0.6896682 0 0.3770212 0.7257686 0 0.3749933 0.7292809 0 0.3922748 0.7410222 0 0.3887624 0.7430503 0 0.04548261 0.747664 0 0.06340771 0.743292 0 0.0815297 0.7196974 0 0.102384 0.7218722 0 0.1228859 0.7269641 0 0.03974801 0.7246917 0 0.06056554 0.7207168 0 0.251215 0.2111897 0 0.736964 0.2108547 0 0.7705032 0.2128941 0 0.827585 0.2206339 0 0.05042196 0.2439942 0 0.1606072 0.2210945 0 0.9378016 0.243382 0 0.2176785 0.2132754 0 0.234408 0.2116098 0 0.7537716 0.2112514 0 0.002001809 0.9077243 0 0.002001968 0.8781944 0 0.002001969 0.9939352 0 0.002001809 0.9644052 0 0.9958989 0.8781944 0 0.9958991 0.9077243 0 0.9958991 0.9644052 0 0.9958989 0.9939352 0 0.9800361 0.6259235 0 0.7669821 0.6259235 0 0.9667194 0.6048946 0 0.780299 0.6048943 0 0.9602585 0.5887973 0 0.78676 0.5887973 0 0.4347199 0.8392426 0 0.9602585 0.3235857 0 0.5435128 0.8392426 0 0.7867599 0.3235857 0 0.5868028 0.8357011 0 0.462326 0.8392426 0 0.5868028 0.7524968 0 0.5159067 0.8392426 0 1.19717 0.8121483 0 0.8183215 0.8357012 0 1.250751 0.8121483 0 0.8183215 0.752497 0 0.8500283 0.7867708 0 1.186354 0.8121483 0 0.9679966 0.7867707 0 1.261567 0.8121483 0 0.8500281 0.8149711 0 0.9679968 0.8149711 0 0.3145111 0.7618572 0 0.8500281 0.8331203 0 0.3978575 0.7618572 0 0.9679968 0.8331203 0 0.3185669 0.7618572 0 0.01916972 0.7307239 0 0.3938015 0.7618573 0 0.142994 0.7340595 0 0.07912795 0.7968946 0 0.07913083 0.7968943 0 0.1037742 0.7921999 0 0.05462784 0.7902758 0 0.07912493 0.7968937 0 0.1234189 0.7771531 0 0.03645669 0.7748253 0 0.1346285 0.7577681 0 0.02628218 0.7549869 0 0.1677319 0.6901464 0 0.1677322 0.6901464 0 1.02665 0.4729064 0 1.046616 0.3489936 0 1.694319 0.3489933 0 1.714287 0.4729064 0 1.580585 0.5534238 0 1.5939 0.5632464 0 1.607794 0.5785071 0 1.536244 0.6896682 0 1.079743 0.3049157 0 1.087098 0.2975892 0 1.097318 0.2955278 0 1.643614 0.2955279 0 1.653834 0.2975899 0 1.661189 0.3049168 0 1.107212 0.5250088 0 1.096723 0.499353 0 1.589517 0.4988416 0 1.578978 0.5246109 0 1.606119 0.5387867 0 1.624423 0.5174533 0 1.660192 0.5684281 0 1.634046 0.5765772 0 1.029694 0.6862143 0 1.002 0.6823123 0 1.026559 0.5686701 0 1.052625 0.5767385 0 1.684649 0.6822202 0 1.656924 0.6861637 0 1.062019 0.5180103 0 1.080261 0.5391866 0 1.121719 0.550795 0 1.564453 0.5503446 0 1.057634 0.6875871 0 1.0788 0.578548 0 1.628962 0.6875351 0 1.092437 0.5634799 0 1.251215 0.2111897 0 1.222871 0.002373723 0 1.76502 0.002000003 0 1.736964 0.2108547 0 1.770503 0.2128941 0 1.798559 0.004034118 0 1.863844 0.01285352 0 1.827585 0.2206339 0 1.050422 0.2439942 0 1.002 0.03873819 0 1.124061 0.01336296 0 1.160607 0.2210945 0 1.98594 0.03806024 0 1.937802 0.243382 0 1.189334 0.004453721 0 1.217679 0.2132754 0 1.594071 0.5286555 0 1.092186 0.5290558 0 1.077547 0.505367 0 1.105603 0.5538146 0 1.60867 0.5047298 0 1.753772 0.2112514 0 1.781827 0.002395667 0 1.234408 0.2116098 0 1.206064 0.002792196 0 1.09634 0.2828323 0 1.644591 0.2828326 0 1.67046 0.2966487 0 1.706848 0.3488166 0 1.01455 0.4729068 0 1.034089 0.3488175 0 1.726387 0.4729066 0 1.070471 0.2966474 0 1.096055 0.269929 0 1.081751 0.2866997 0 1.076784 0.2754165 0 1.679657 0.2880234 0 1.65918 0.2867005 0 1.664146 0.2754173 0 1.061274 0.288022 0 1.644876 0.2699295 0 1.718709 0.3456154 0 1.002 0.4729068 0 1.022228 0.3456169 0 1.738938 0.4729068 0 1.22477 0.9077243 0 1.22477 0.8781944 0 1.773136 0.8781944 0 1.773136 0.9077243 0 1.205914 0.9077243 0 1.205914 0.8781944 0 1.148382 0.9077243 0 1.148382 0.8781944 0 1.074143 0.9077243 0 1.074143 0.8781944 0 1.205914 0.9644052 0 1.148382 0.9644052 0 1.074143 0.9644052 0 1.22477 0.9644052 0 1.773136 0.9644052 0 1.773136 0.9939352 0 1.22477 0.9939352 0 1.205914 0.9939352 0 1.148382 0.9939352 0 1.074143 0.9939352 0 1.791991 0.9077243 0 1.791991 0.8781944 0 1.849521 0.9077243 0 1.849521 0.8781944 0 1.923758 0.9077243 0 1.923758 0.8781944 0 1.849521 0.9644052 0 1.791991 0.9644052 0 1.923758 0.9644052 0 1.791991 0.9939352 0 1.849521 0.9939352 0 1.923758 0.9939352 0 1.94321 0.6259235 0 1.907559 0.6259235 0 1.905335 0.6048945 0 1.936882 0.6048945 0 1.873508 0.6259234 0 1.873508 0.6048945 0 1.839458 0.6259234 0 1.841681 0.6048945 0 1.803807 0.6259235 0 1.810136 0.6048945 0 1.766982 0.6259235 0 1.780299 0.6048943 0 1.932123 0.5887973 0 1.932123 0.3235857 0 1.960258 0.3235857 0 1.960258 0.5887973 0 1.903168 0.5887973 0 1.903168 0.3235857 0 1.873508 0.5887973 0 1.873508 0.3235857 0 1.843848 0.5887971 0 1.843848 0.3235856 0 1.814895 0.5887973 0 1.814895 0.3235857 0 1.966719 0.6048946 0 1.465915 0.8258489 0 1.442007 0.8120461 0 1.461918 0.7921353 0 1.475721 0.8160427 0 1.489116 0.7848475 0 1.489116 0.8124535 0 1.516315 0.7921353 0 1.502512 0.8160427 0 1.536225 0.8120461 0 1.512318 0.8258489 0 1.543513 0.8392426 0 1.515907 0.8392426 0 1.586803 0.821835 0 1.586803 0.8079671 0 1.818321 0.8079671 0 1.818321 0.8218351 0 1.586803 0.794099 0 1.818321 0.794099 0 1.586803 0.7802309 0 1.818321 0.7802309 0 1.586803 0.7663629 0 1.818321 0.7663629 0 1.586803 0.7524968 0 1.818321 0.752497 0 0.200759 0.7987549 0 0.2105651 0.7889487 0 0.2051568 0.7795811 0 0.1913916 0.7933465 0 0.2239604 0.7853595 0 0.2239604 0.7745426 0 0.2373558 0.7889487 0 0.2427641 0.7795811 0 0.247162 0.7987549 0 0.2565294 0.7933465 0 0.2507509 0.8121483 0 0.2615672 0.8121483 0 1.869689 0.7867707 0 1.889341 0.7867707 0 1.889341 0.8149711 0 1.869689 0.8149711 0 1.908845 0.7867707 0 1.908845 0.8149711 0 1.928483 0.7867707 0 1.928483 0.8149711 0 1.948118 0.7867707 0 1.948118 0.8149711 0 1.967997 0.7867707 0 1.967997 0.8149711 0 1.889341 0.8331203 0 1.869689 0.8331203 0 1.908845 0.8331203 0 1.928483 0.8331203 0 1.948118 0.8331203 0 1.967997 0.8331203 0 1.320094 0.7410222 0 1.335347 0.7257686 0 1.337375 0.7292809 0 1.323606 0.7430503 0 1.356184 0.7201852 0 1.356184 0.7242411 0 1.377021 0.7257686 0 1.374993 0.7292809 0 1.392275 0.7410222 0 1.388762 0.7430503 0 1.397858 0.7618572 0 1.393801 0.7618573 0 1.080038 0.7756385 0 1.080427 0.7588475 0 1.094411 0.7609736 0 1.089185 0.7777145 0 1.108535 0.7667583 0 1.097476 0.7831694 0 1.123419 0.7771531 0 1.103774 0.7921999 0 1.061879 0.7815809 0 1.051931 0.765166 0 1.066351 0.7601733 0 1.070717 0.7768605 0 1.080925 0.7421283 0 1.098346 0.744223 0 1.115973 0.7495114 0 1.134629 0.7577681 0 1.045483 0.747664 0 1.063408 0.743292 0 1.08153 0.7196974 0 1.102384 0.7218722 0 1.122886 0.7269641 0 1.026282 0.7549869 0 1.01917 0.7307239 0 1.039748 0.7246917 0 1.060566 0.7207168 0 1.167732 0.6901464 0 1.002002 0.8781944 0 1.002002 0.9077243 0 1.002002 0.9644052 0 1.002002 0.9939352 0 1.995899 0.9077243 0 1.995899 0.8781944 0 1.995899 0.9939352 0 1.995899 0.9644052 0 1.980036 0.6259235 0 1.78676 0.3235857 0 1.78676 0.5887973 0 1.43472 0.8392426 0 1.462326 0.8392426 0 1.586803 0.8357011 0 1.818321 0.8357012 0 0.1971701 0.8121483 0 0.1863537 0.8121483 0 1.850028 0.7867708 0 1.850028 0.8149711 0 1.850028 0.8331203 0 1.314511 0.7618572 0 1.318567 0.7618572 0 1.054628 0.7902758 0 1.079129 0.7968943 0 1.036457 0.7748253 0 1.142994 0.7340595 0</float_array>
          <technique_common>
            <accessor source="#geom-Box002-map1-array" count="515" stride="3">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
              <param name="P" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="geom-Box002-vertices">
          <input semantic="POSITION" source="#geom-Box002-positions"/>
        </vertices>
        <polylist material="MobileRobot" count="320">
          <input semantic="VERTEX" source="#geom-Box002-vertices" offset="0"/>
          <input semantic="NORMAL" source="#geom-Box002-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#geom-Box002-map1" offset="2" set="0"/>
          <vcount>4 4 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 4 4 4 3 3 3 4 4 8 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 4 3 3 3 3 3 3 4 4 4 3 3 3 </vcount>
          <p>168 0 4 40 1 5 3 2 6 2 3 7 34 4 46 15 5 36 18 6 2 174 7 186 40 1 5 41 8 8 28 9 9 0 10 10 37 11 11 44 12 14 21 13 15 3 2 6 6 14 12 12 15 13 24 16 16 22 17 17 5 18 18 11 19 19 13 20 24 9 21 25 7 22 26 189 23 27 19 24 28 10 25 29 9 21 25 13 20 24 192 26 31 8 27 30 10 25 29 19 24 28 25 28 32 23 29 33 16 30 34 6 14 12 22 17 17 26 31 35 15 5 36 5 18 18 9 21 25 18 6 2 197 32 0 7 22 26 10 25 29 14 33 1 18 6 2 9 21 25 8 27 30 17 34 3 14 33 1 10 25 29 23 29 33 27 35 37 12 15 198 1 36 38 36 37 39 24 16 199 11 19 200 20 38 40 4 39 41 13 20 201 189 23 202 38 40 42 39 41 43 19 24 203 13 20 201 4 39 41 205 42 44 192 26 204 19 24 203 39 41 43 42 43 45 25 28 205 15 5 36 34 4 46 30 44 47 5 18 18 6 14 12 31 45 48 33 46 49 12 15 13 16 30 34 35 47 50 31 45 48 6 14 12 5 18 18 30 44 47 32 48 51 11 19 19 11 19 200 32 48 207 43 49 52 20 38 40 12 15 198 33 46 206 29 50 53 1 36 38 34 4 46 26 31 35 22 17 17 30 44 47 31 45 48 23 29 33 25 28 32 33 46 49 35 47 50 27 35 37 23 29 33 31 45 48 30 44 47 22 17 17 24 16 16 32 48 51 32 48 207 24 16 199 36 37 39 43 49 52 33 46 206 25 28 205 42 43 45 29 50 53 0 10 10 45 51 20 46 52 21 37 11 11 21 13 15 51 53 22 48 54 23 3 2 6 168 0 4 218 55 54 49 56 55 40 1 5 3 2 6 48 54 23 47 57 56 2 3 7 40 1 5 49 56 55 50 58 57 41 8 8 36 37 58 45 51 20 52 59 59 43 49 60 42 43 61 51 53 22 53 60 62 29 50 63 43 49 60 52 59 59 50 58 57 20 38 64 29 50 63 53 60 62 46 52 21 1 36 65 45 51 20 36 37 58 1 36 65 46 52 21 51 53 22 42 43 61 39 41 66 48 54 23 218 55 54 205 42 67 4 39 68 49 56 55 48 54 23 39 41 66 38 40 69 47 57 56 49 56 55 4 39 68 20 38 64 50 58 57 45 51 20 0 10 10 28 9 9 52 59 59 51 53 22 21 13 15 44 12 14 53 60 62 52 59 59 28 9 9 41 8 8 50 58 57 53 60 62 44 12 14 37 11 11 46 52 21 57 61 78 58 62 79 71 63 80 70 64 81 56 65 82 59 66 83 58 62 79 57 61 78 54 67 84 60 68 85 59 66 83 56 65 82 55 69 86 61 70 87 60 68 85 54 67 84 54 71 84 56 72 82 64 73 88 66 74 89 55 75 86 54 71 84 66 74 89 68 76 90 62 77 91 78 78 92 79 79 93 63 80 94 64 81 88 62 77 91 63 80 94 65 82 95 66 83 89 64 81 88 65 82 95 67 84 96 68 85 90 66 83 89 67 84 96 69 86 97 64 73 88 56 72 82 57 87 78 62 88 91 72 89 98 70 64 81 71 63 80 73 90 99 74 91 100 72 89 98 73 90 99 75 92 101 76 93 102 74 91 100 75 92 101 77 94 103 74 95 100 82 96 104 80 97 105 72 98 98 76 99 102 84 100 106 82 96 104 74 95 100 80 101 105 81 102 109 79 79 93 78 78 92 82 103 104 83 104 111 81 102 109 80 101 105 84 105 106 85 106 119 83 104 111 82 103 104 78 107 92 62 88 91 57 87 78 70 108 81 80 97 105 78 107 92 70 108 81 72 98 98 86 109 122 87 110 124 97 111 125 96 112 123 87 110 124 88 113 126 98 114 127 97 111 125 88 113 126 89 115 128 99 116 129 98 114 127 89 115 128 90 117 130 100 118 131 99 116 129 90 117 130 265 119 217 156 120 219 100 118 131 101 121 132 91 122 133 268 123 223 269 124 220 102 125 134 92 126 135 91 122 133 101 121 132 103 127 136 93 128 137 92 126 135 102 125 134 104 129 138 94 130 139 93 128 137 103 127 136 105 131 140 95 132 141 94 130 139 104 129 138 96 112 123 101 121 132 269 124 220 155 133 218 97 111 125 102 125 134 101 121 132 96 112 123 98 114 127 103 127 136 102 125 134 97 111 125 99 116 129 104 129 138 103 127 136 98 114 127 100 118 131 105 131 140 104 129 138 99 116 129 106 134 71 91 135 70 92 136 72 107 137 73 107 137 73 92 136 72 93 138 74 108 139 75 108 139 75 93 138 74 94 140 76 109 141 77 109 141 77 94 140 76 95 142 107 110 143 108 110 143 108 95 142 107 158 144 224 160 145 229 106 146 142 107 147 144 112 148 145 111 149 143 107 147 144 108 150 146 113 151 147 112 148 145 108 150 146 109 152 148 114 153 149 113 151 147 109 152 148 110 154 150 115 155 151 114 153 149 110 154 150 160 156 228 288 157 233 115 155 151 111 158 110 112 159 113 117 160 114 116 161 112 112 159 113 113 162 115 118 163 116 117 160 114 113 162 115 114 164 117 119 165 118 118 163 116 114 164 117 115 166 120 120 167 121 119 165 118 115 166 120 288 168 232 294 169 237 120 167 121 116 170 152 117 171 154 122 172 155 121 173 153 117 171 154 118 174 156 123 175 157 122 172 155 118 174 156 119 176 158 124 177 159 123 175 157 119 176 158 120 178 166 125 179 167 124 177 159 120 178 166 294 180 236 163 181 239 125 179 167 121 173 153 122 172 155 127 182 169 126 183 168 122 172 155 123 175 157 128 184 170 127 182 169 123 175 157 124 177 159 129 185 171 128 184 170 124 177 159 125 179 167 130 186 172 129 185 171 125 179 167 163 181 239 305 187 243 130 186 172 126 188 160 127 189 162 132 190 163 131 191 161 127 189 162 128 192 164 133 193 165 132 190 163 128 192 164 129 194 187 134 195 188 133 193 165 129 194 187 130 196 189 135 197 190 134 195 188 130 196 189 305 198 242 311 199 246 135 197 190 136 200 173 141 201 178 142 202 179 137 203 174 137 203 174 142 202 179 143 204 180 138 205 175 138 205 175 143 204 180 166 206 253 318 207 250 139 208 176 144 209 181 145 210 182 140 211 177 140 211 177 145 210 182 141 201 178 136 200 173 141 201 178 146 212 183 147 213 184 142 202 179 142 202 179 147 213 184 148 214 185 143 204 180 143 204 180 148 214 185 326 215 255 166 206 253 144 209 181 149 216 191 150 217 192 145 210 182 145 210 182 150 217 192 146 212 183 141 201 178 147 213 184 146 212 183 133 218 193 134 219 194 148 214 185 147 213 184 134 219 194 135 220 195 149 216 191 329 221 256 165 222 245 131 223 196 150 217 192 149 216 191 131 223 196 132 224 197 146 212 183 150 217 192 132 224 197 133 218 193 18 6 2 17 34 3 174 7 186 26 31 35 34 4 46 174 7 186 27 35 37 35 47 50 330 225 257 16 30 34 26 31 35 174 7 186 330 225 258 331 226 209 61 70 87 55 69 86 332 227 208 152 228 211 68 85 90 69 86 97 151 229 210 332 230 208 55 75 86 68 76 90 152 231 211 333 232 213 76 93 102 77 94 103 153 233 212 154 234 215 85 106 119 84 105 106 334 235 214 334 236 214 84 100 106 76 99 102 333 237 213 335 238 216 86 109 122 96 112 123 155 133 218 158 239 225 95 132 141 105 131 140 157 240 221 157 240 221 105 131 140 100 118 131 156 120 219 268 241 222 91 135 70 106 134 71 159 242 227 159 243 226 106 146 142 111 149 143 161 244 231 161 245 230 111 158 110 116 161 112 336 246 235 336 247 234 116 170 152 121 173 153 162 248 238 162 248 238 121 173 153 126 183 168 164 249 241 164 250 240 126 188 160 131 191 161 165 251 244 338 252 248 136 200 173 137 203 174 137 203 174 138 205 175 338 252 249 338 252 249 138 205 175 318 207 250 337 253 251 139 208 176 338 252 252 338 252 252 139 208 176 140 211 177 140 211 177 136 200 173 338 252 248 167 254 254 144 209 181 139 208 176 337 253 251 329 221 256 149 216 191 144 209 181 167 254 254 326 215 255 148 214 185 135 220 195 311 255 247 197 32 0 14 33 1 330 225 257 14 33 1 27 35 37 330 225 257 35 47 50 16 30 34 330 225 258 168 0 259 2 3 262 170 256 261 169 257 260 171 258 263 174 7 266 173 259 265 172 260 264 169 257 260 170 256 261 180 261 272 179 262 271 178 263 270 177 264 269 176 265 268 175 266 267 181 267 273 184 268 276 183 269 275 182 270 274 185 271 277 188 272 280 187 273 279 186 274 278 7 22 281 191 275 284 190 276 283 189 23 282 188 272 280 8 27 286 192 26 285 187 273 279 191 275 284 194 277 288 193 278 287 190 276 283 195 279 289 196 280 290 184 268 276 181 267 273 172 260 264 173 259 265 188 272 280 185 271 277 197 32 291 198 281 292 191 275 284 7 22 281 173 259 265 17 34 293 8 27 286 188 272 280 198 281 292 199 282 294 194 277 288 191 275 284 182 270 295 183 269 298 201 283 297 200 284 296 186 274 299 187 273 302 203 285 301 202 286 300 189 23 303 190 276 306 204 287 305 38 40 304 187 273 302 192 26 308 205 42 307 203 285 301 190 276 306 193 278 310 206 288 309 204 287 305 172 260 264 185 271 277 207 289 311 171 258 263 181 267 273 182 270 274 209 290 313 208 291 312 195 279 289 181 267 273 208 291 312 210 292 314 185 271 277 186 274 278 211 293 315 207 289 311 186 274 299 202 286 300 212 294 317 211 293 316 182 270 295 200 284 296 213 295 319 209 290 318 171 258 263 207 289 311 184 268 276 196 280 290 208 291 312 209 290 313 193 278 287 194 277 288 210 292 314 208 291 312 194 277 288 199 282 294 207 289 311 211 293 315 183 269 275 184 268 276 211 293 316 212 294 317 201 283 297 183 269 298 209 290 318 213 295 319 206 288 309 193 278 310 177 264 269 178 263 270 215 296 321 214 297 320 180 261 272 170 256 261 217 298 323 216 299 322 168 0 259 169 257 260 219 300 325 218 55 324 170 256 261 2 3 262 47 57 326 217 298 323 169 257 260 175 266 267 220 301 327 219 300 325 201 283 328 212 294 330 221 302 329 214 297 320 206 288 331 213 295 333 222 303 332 216 299 322 212 294 330 202 286 334 220 301 327 221 302 329 213 295 333 200 284 335 215 296 321 222 303 332 214 297 320 215 296 321 200 284 335 201 283 328 216 299 322 217 298 323 204 287 336 206 288 331 218 55 324 219 300 325 203 285 338 205 42 337 217 298 323 47 57 326 38 40 339 204 287 336 219 300 325 220 301 327 202 286 334 203 285 338 214 297 320 221 302 329 176 265 268 177 264 269 216 299 322 222 303 332 179 262 271 180 261 272 221 302 329 220 301 327 175 266 267 176 265 268 222 303 332 215 296 321 178 263 270 179 262 271 223 304 340 226 305 343 225 306 342 224 307 341 227 308 344 223 304 340 224 307 341 228 309 345 229 310 346 227 308 344 228 309 345 230 311 347 231 312 348 229 310 346 230 311 347 232 313 349 229 314 346 234 315 351 233 316 350 227 317 344 231 318 348 235 319 352 234 315 351 229 314 346 236 320 353 239 321 356 238 322 355 237 323 354 233 324 350 240 325 357 239 321 356 236 320 353 234 326 351 241 327 358 240 325 357 233 324 350 235 328 352 242 329 359 241 327 358 234 326 351 233 316 350 236 330 353 223 331 340 227 317 344 243 332 360 244 333 361 225 306 342 226 305 343 245 334 362 246 335 363 244 333 361 243 332 360 247 336 364 248 337 365 246 335 363 245 334 362 245 338 362 243 339 360 250 340 367 249 341 366 247 342 364 245 338 362 249 341 366 251 343 368 250 344 367 237 323 354 238 322 355 252 345 369 249 346 366 250 344 367 252 345 369 253 347 370 251 348 368 249 346 366 253 347 370 254 349 371 237 350 354 226 351 343 223 331 340 236 330 353 250 340 367 243 339 360 226 351 343 237 350 354 255 352 372 258 353 375 257 354 374 256 355 373 256 355 373 257 354 374 260 356 377 259 357 376 259 357 376 260 356 377 262 358 379 261 359 378 261 359 378 262 358 379 264 360 381 263 361 380 263 361 380 264 360 381 156 120 383 265 119 382 266 362 384 269 124 387 268 123 386 267 363 385 270 364 388 266 362 384 267 363 385 271 365 389 272 366 390 270 364 388 271 365 389 273 367 391 274 368 392 272 366 390 273 367 391 275 369 393 276 370 394 274 368 392 275 369 393 277 371 395 258 353 375 155 133 396 269 124 387 266 362 384 257 354 374 258 353 375 266 362 384 270 364 388 260 356 377 257 354 374 270 364 388 272 366 390 262 358 379 260 356 377 272 366 390 274 368 392 264 360 381 262 358 379 274 368 392 276 370 394 278 372 397 279 373 400 271 374 399 267 375 398 279 373 400 280 376 402 273 377 401 271 374 399 280 376 402 281 378 404 275 379 403 273 377 401 281 378 404 282 380 406 277 381 405 275 379 403 282 380 406 160 145 408 158 144 407 277 381 405 278 382 409 284 383 412 283 384 411 279 385 410 279 385 410 283 384 411 285 386 414 280 387 413 280 387 413 285 386 414 286 388 416 281 389 415 281 389 415 286 388 416 287 390 418 282 391 417 282 391 417 287 390 418 288 157 420 160 156 419 284 392 421 290 393 424 289 394 423 283 395 422 283 395 422 289 394 423 291 396 426 285 397 425 285 397 425 291 396 426 292 398 428 286 399 427 286 399 427 292 398 428 293 400 430 287 401 429 287 401 429 293 400 430 294 169 432 288 168 431 290 402 433 296 403 436 295 404 435 289 405 434 289 405 434 295 404 435 297 406 438 291 407 437 291 407 437 297 406 438 298 408 440 292 409 439 292 409 439 298 408 440 299 410 442 293 411 441 293 411 441 299 410 442 163 181 444 294 180 443 296 403 436 301 412 446 300 413 445 295 404 435 295 404 435 300 413 445 302 414 447 297 406 438 297 406 438 302 414 447 303 415 448 298 408 440 298 408 440 303 415 448 304 416 449 299 410 442 299 410 442 304 416 449 305 187 450 163 181 444 301 417 451 307 418 454 306 419 453 300 420 452 300 420 452 306 419 453 308 421 456 302 422 455 302 422 455 308 421 456 309 423 458 303 424 457 303 424 457 309 423 458 310 425 460 304 426 459 304 426 459 310 425 460 311 199 462 305 198 461 312 427 463 315 428 466 314 429 465 313 430 464 315 428 466 317 431 468 316 432 467 314 429 465 317 431 468 318 207 470 166 206 469 316 432 467 319 433 471 322 434 474 321 435 473 320 436 472 322 434 474 312 427 463 313 430 464 321 435 473 313 430 464 314 429 465 324 437 476 323 438 475 314 429 465 316 432 467 325 439 477 324 437 476 316 432 467 166 206 469 326 215 478 325 439 477 320 436 472 321 435 473 328 440 480 327 441 479 321 435 473 313 430 464 323 438 475 328 440 480 324 437 476 309 442 482 308 443 481 323 438 475 325 439 477 310 444 483 309 442 482 324 437 476 327 441 479 307 445 486 165 222 485 329 221 484 328 440 480 306 446 487 307 445 486 327 441 479 323 438 475 308 443 481 306 446 487 328 440 480 173 259 265 174 7 266 17 34 293 196 280 290 174 7 266 171 258 263 199 282 294 330 225 488 210 292 314 195 279 289 330 225 488 174 7 266 196 280 290 331 226 489 332 227 490 231 312 348 232 313 349 152 228 491 151 229 492 242 329 359 235 328 352 332 230 490 152 231 491 235 319 352 231 318 348 333 232 493 153 233 494 248 337 365 247 336 364 154 234 495 334 235 496 251 348 368 254 349 371 334 236 496 333 237 493 247 342 364 251 343 368 335 238 497 155 133 396 258 353 375 255 352 372 158 239 498 157 240 499 276 370 394 277 371 395 157 240 499 156 120 383 264 360 381 276 370 394 268 241 500 159 242 501 278 372 397 267 375 398 159 243 502 161 244 503 284 383 412 278 382 409 161 245 504 336 246 505 290 393 424 284 392 421 336 247 506 162 248 507 296 403 436 290 402 433 162 248 507 164 249 508 301 412 446 296 403 436 164 250 509 165 251 510 307 418 454 301 417 451 338 252 512 315 428 466 312 427 463 338 252 512 317 431 468 315 428 466 338 252 512 318 207 470 317 431 468 337 253 511 338 252 512 319 433 471 338 252 512 322 434 474 319 433 471 338 252 512 312 427 463 322 434 474 167 254 513 337 253 511 319 433 471 320 436 472 329 221 484 167 254 513 320 436 472 327 441 479 326 215 478 311 255 514 310 444 483 325 439 477 197 32 291 330 225 488 198 281 292 330 225 488 199 282 294 198 281 292 330 225 488 195 279 289 210 292 314</p>
        </polylist>
      </mesh>
    </geometry>
  </library_geometries>
  <library_lights>
    <light id="EnvironmentAmbientLight" name="EnvironmentAmbientLight">
      <technique_common>
        <ambient>
          <color>0 0 0</color>
        </ambient>
      </technique_common>
    </light>
  </library_lights>
  <library_images>
    <image id="MobileRobot_png">
      <init_from>robot.png</init_from>
    </image>
  </library_images>
  <library_visual_scenes>
    <visual_scene id="MaxScene">
      <node name="EnvironmentAmbientLight">
        <instance_light url="#EnvironmentAmbientLight"/>
      </node>
      <node id="node-Box002" name="Box002">
        <instance_geometry url="#geom-Box002">
          <bind_material>
            <technique_common>
              <instance_material symbol="MobileRobot" target="#MobileRobot-material">
                <bind_vertex_input semantic="CHANNEL1" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
        <extra>
          <technique profile="OpenCOLLADA">
            <cast_shadows sid="cast_shadows" type="bool">1</cast_shadows>
            <receive_shadows sid="receive_shadows" type="bool">1</receive_shadows>
            <primary_visibility sid="primary_visibility" type="int">1</primary_visibility>
            <secondary_visibility sid="secondary_visibility" type="int">1</secondary_visibility>
          </technique>
        </extra>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#MaxScene"/>
  </scene>
</COLLADA>

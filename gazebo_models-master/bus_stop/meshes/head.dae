<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.3.1 commit date:2022-10-04, commit time:18:35, hash:b292cfe5a936</authoring_tool>
    </contributor>
    <created>2022-12-09T12:35:39</created>
    <modified>2022-12-09T12:35:39</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="plastic-effect">
      <profile_COMMON>
        <newparam sid="Glass_Frosted_001_basecolor_jpg-surface">
          <surface type="2D">
            <init_from>Glass_Frosted_001_basecolor_jpg</init_from>
          </surface>
        </newparam>
        <newparam sid="Glass_Frosted_001_basecolor_jpg-sampler">
          <sampler2D>
            <source>Glass_Frosted_001_basecolor_jpg-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="Glass_Frosted_001_basecolor_jpg-sampler" texcoord="UVMap"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="Glass_Frosted_001_basecolor_jpg" name="Glass_Frosted_001_basecolor_jpg">
      <init_from>Glass_Frosted_001_basecolor.jpg</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="plastic-material" name="plastic">
      <instance_effect url="#plastic-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="head-mesh" name="head">
      <mesh>
        <source id="head-mesh-positions">
          <float_array id="head-mesh-positions-array" count="216">-1.5 0 0.8 -1.5 0.00999999 0.8 -1.5 0 0.75 -1.5 0.00999999 0.759898 -1.5 0.1250759 0.733596 -1.5 0.1278562 0.7431275 -1.5 0.2446806 0.6851015 -1.5 0.2483877 0.6941774 -1.5 0.3535819 0.6066378 -1.5 0.3581963 0.6152597 -1.5 0.4470161 0.5016373 -1.5 0.4525415 0.5097811 -1.5 0.520896 0.3746932 -1.5 0.5273505 0.3822953 -1.5 0.5719898 0.2313585 -1.5 0.5793937 0.2383058 -1.5 0.5980624 0.07790321 -1.5 0.6064245 0.08402854 -1.5 0.5979733 -0.07895988 -1.5 0.6072758 -0.07387614 -1.5 0.5717265 -0.232369 -1.5 0.581911 -0.2285916 -1.5 0.5204699 -0.3756133 -1.5 0.5314251 -0.3734388 -1.5 0.4464459 -0.5024268 -1.5 0.4579976 -0.5021646 -1.5 0.3528925 -0.607262 -1.5 0.3647983 -0.6092119 -1.5 0.2439022 -0.6855331 -1.5 0.2558506 -0.6899595 -1.5 0.1242426 -0.7338163 -1.5 0.1358578 -0.7409216 -1.5 0 -0.75 -1.5 0.00999999 -0.759898 -1.5 0 -0.8 -1.5 0.00999999 -0.8 1.5 0.00999999 0.8 1.5 0 0.8 1.5 0 -0.8 1.5 0.00999999 -0.8 1.5 0 -0.75 1.5 0.00999999 -0.759898 1.5 0.1242426 -0.7338163 1.5 0.1358578 -0.7409216 1.5 0.2439022 -0.6855331 1.5 0.2558506 -0.6899595 1.5 0.3528925 -0.607262 1.5 0.3647983 -0.6092119 1.5 0.4464459 -0.5024268 1.5 0.4579976 -0.5021646 1.5 0.5204699 -0.3756133 1.5 0.5314251 -0.3734388 1.5 0.5717265 -0.232369 1.5 0.581911 -0.2285916 1.5 0.5979733 -0.07895988 1.5 0.6072758 -0.07387614 1.5 0.5980624 0.07790321 1.5 0.6064245 0.08402854 1.5 0.5719898 0.2313585 1.5 0.5793937 0.2383058 1.5 0.520896 0.3746932 1.5 0.5273505 0.3822953 1.5 0.4470161 0.5016373 1.5 0.4525415 0.5097811 1.5 0.3535819 0.6066378 1.5 0.3581963 0.6152597 1.5 0.2446806 0.6851015 1.5 0.2483877 0.6941774 1.5 0.1250759 0.733596 1.5 0.1278562 0.7431275 1.5 0 0.75 1.5 0.00999999 0.759898</float_array>
          <technique_common>
            <accessor source="#head-mesh-positions-array" count="72" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="head-mesh-normals">
          <float_array id="head-mesh-normals-array" count="186">-1 0 0 -1 2.30373e-5 0 0 0 1 1 0 0 1 2.27448e-5 0 1 -2.04298e-5 0 1 2.24818e-5 0 0 1 0 0 -1 0 0 -0.1291679 0.9916228 0 -0.3741904 0.927352 0 -0.3741905 0.9273519 0 -0.5833135 0.8122472 0 -0.5833132 0.8122474 0 -0.7461126 0.6658198 0 -0.7461129 0.6658195 0 -0.8636322 0.5041226 0 -0.8636324 0.5041223 0 -0.9415381 0.3369066 0 -0.9415379 0.336907 0 -0.9856778 0.1686397 0 -0.9856777 0.1686403 0 -0.9999999 5.67941e-4 0 -0.9999999 5.68448e-4 0 -0.9858716 -0.1675029 0 -0.9858716 -0.1675034 0 -0.941944 -0.33577 0 -0.9419444 -0.3357691 0 -0.8642845 -0.5030034 0 -0.8642847 -0.5030029 0 -0.7470542 -0.6647631 0 -0.7470548 -0.6647624 0 -0.5845729 -0.8113412 0 -0.3757461 -0.9267227 0 -0.1300389 -0.9915089 0 0.140877 0.9900271 0 0.140877 0.9900272 0 0.3762726 0.926509 0 0.3762724 0.9265091 0 0.5836012 0.8120404 0 0.583601 0.8120406 0 0.7453479 0.6666758 0 0.7453476 0.6666761 0 0.8624739 0.5061019 0 0.940456 0.3399154 0 0.9404557 0.3399164 0 0.9849956 0.1725803 0 0.9849956 0.1725798 0 0.9999855 0.005391716 0 0.9999855 0.00539118 0 0.9868261 -0.161785 0 0.9442857 -0.3291271 0 0.9442859 -0.3291267 0 0.8686208 -0.4954776 0 0.8686212 -0.4954768 0 0.7542052 -0.6566388 0 0.7542049 -0.6566391 0 0.5954448 -0.8033963 0 0.3909138 -0.9204274 0 0.390914 -0.9204273 0 0.1490913 -0.9888234 0 0 -1</float_array>
          <technique_common>
            <accessor source="#head-mesh-normals-array" count="62" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="head-mesh-map-0">
          <float_array id="head-mesh-map-0-array" count="840">0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1</float_array>
          <technique_common>
            <accessor source="#head-mesh-map-0-array" count="420" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="head-mesh-vertices">
          <input semantic="POSITION" source="#head-mesh-positions"/>
        </vertices>
        <triangles material="plastic-material" count="140">
          <input semantic="VERTEX" source="#head-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#head-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#head-mesh-map-0" offset="2" set="0"/>
          <p>0 0 0 1 0 1 2 0 2 2 0 3 1 0 4 3 0 5 2 0 6 3 0 7 4 0 8 4 0 9 3 0 10 5 0 11 4 0 12 5 0 13 6 0 14 6 0 15 5 0 16 7 0 17 6 0 18 7 0 19 8 0 20 8 0 21 7 0 22 9 0 23 8 0 24 9 0 25 10 0 26 10 0 27 9 0 28 11 0 29 10 0 30 11 0 31 12 0 32 12 0 33 11 0 34 13 0 35 12 0 36 13 0 37 14 0 38 14 0 39 13 0 40 15 0 41 14 0 42 15 0 43 16 0 44 16 0 45 15 0 46 17 0 47 16 0 48 17 0 49 18 0 50 18 0 51 17 0 52 19 0 53 18 1 54 19 1 55 20 1 56 20 0 57 19 0 58 21 0 59 20 0 60 21 0 61 22 0 62 22 0 63 21 0 64 23 0 65 22 0 66 23 0 67 24 0 68 24 0 69 23 0 70 25 0 71 24 0 72 25 0 73 26 0 74 26 0 75 25 0 76 27 0 77 26 0 78 27 0 79 28 0 80 28 0 81 27 0 82 29 0 83 28 0 84 29 0 85 30 0 86 30 0 87 29 0 88 31 0 89 30 0 90 31 0 91 32 0 92 32 0 93 31 0 94 33 0 95 32 0 96 33 0 97 34 0 98 34 0 99 33 0 100 35 0 101 36 2 102 1 2 103 37 2 104 37 2 105 1 2 106 0 2 107 38 3 108 39 3 109 40 3 110 40 3 111 39 3 112 41 3 113 40 3 114 41 3 115 42 3 116 42 3 117 41 3 118 43 3 119 42 3 120 43 3 121 44 3 122 44 3 123 43 3 124 45 3 125 44 3 126 45 3 127 46 3 128 46 3 129 45 3 130 47 3 131 46 3 132 47 3 133 48 3 134 48 3 135 47 3 136 49 3 137 48 3 138 49 3 139 50 3 140 50 3 141 49 3 142 51 3 143 50 3 144 51 3 145 52 3 146 52 3 147 51 3 148 53 3 149 52 3 150 53 3 151 54 3 152 54 4 153 53 4 154 55 4 155 54 5 156 55 5 157 56 5 158 56 6 159 55 6 160 57 6 161 56 3 162 57 3 163 58 3 164 58 3 165 57 3 166 59 3 167 58 3 168 59 3 169 60 3 170 60 3 171 59 3 172 61 3 173 60 3 174 61 3 175 62 3 176 62 3 177 61 3 178 63 3 179 62 3 180 63 3 181 64 3 182 64 3 183 63 3 184 65 3 185 64 3 186 65 3 187 66 3 188 66 3 189 65 3 190 67 3 191 66 3 192 67 3 193 68 3 194 68 3 195 67 3 196 69 3 197 68 3 198 69 3 199 70 3 200 70 3 201 69 3 202 71 3 203 70 3 204 71 3 205 37 3 206 37 3 207 71 3 208 36 3 209 1 7 210 36 7 211 3 7 212 3 7 213 36 7 214 71 7 215 2 8 216 70 8 217 0 8 218 0 8 219 70 8 220 37 8 221 32 9 222 40 9 223 42 9 224 32 9 225 42 9 226 30 9 227 30 10 228 42 10 229 44 10 230 30 11 231 44 11 232 28 11 233 28 12 234 44 12 235 46 12 236 28 13 237 46 13 238 26 13 239 26 14 240 46 14 241 48 14 242 26 15 243 48 15 244 24 15 245 24 16 246 48 16 247 50 16 248 24 17 249 50 17 250 22 17 251 22 18 252 50 18 253 52 18 254 22 19 255 52 19 256 20 19 257 20 20 258 52 20 259 54 20 260 20 21 261 54 21 262 18 21 263 18 22 264 54 22 265 56 22 266 18 23 267 56 23 268 16 23 269 16 24 270 56 24 271 58 24 272 16 25 273 58 25 274 14 25 275 14 26 276 58 26 277 60 26 278 14 27 279 60 27 280 12 27 281 12 28 282 60 28 283 62 28 284 12 29 285 62 29 286 10 29 287 10 30 288 62 30 289 64 30 290 10 31 291 64 31 292 8 31 293 8 32 294 64 32 295 66 32 296 8 32 297 66 32 298 6 32 299 6 33 300 66 33 301 68 33 302 6 33 303 68 33 304 4 33 305 4 34 306 68 34 307 70 34 308 4 34 309 70 34 310 2 34 311 3 35 312 71 35 313 69 35 314 3 36 315 69 36 316 5 36 317 5 37 318 69 37 319 67 37 320 5 38 321 67 38 322 7 38 323 7 39 324 67 39 325 65 39 326 7 40 327 65 40 328 9 40 329 9 41 330 65 41 331 63 41 332 9 42 333 63 42 334 11 42 335 11 43 336 63 43 337 61 43 338 11 43 339 61 43 340 13 43 341 13 44 342 61 44 343 59 44 344 13 45 345 59 45 346 15 45 347 15 46 348 59 46 349 57 46 350 15 47 351 57 47 352 17 47 353 17 48 354 57 48 355 55 48 356 17 49 357 55 49 358 19 49 359 19 50 360 55 50 361 53 50 362 19 50 363 53 50 364 21 50 365 21 51 366 53 51 367 51 51 368 21 52 369 51 52 370 23 52 371 23 53 372 51 53 373 49 53 374 23 54 375 49 54 376 25 54 377 25 55 378 49 55 379 47 55 380 25 56 381 47 56 382 27 56 383 27 57 384 47 57 385 45 57 386 27 57 387 45 57 388 29 57 389 29 58 390 45 58 391 43 58 392 29 59 393 43 59 394 31 59 395 31 60 396 43 60 397 41 60 398 31 60 399 41 60 400 33 60 401 38 8 402 40 8 403 34 8 404 34 8 405 40 8 406 32 8 407 35 61 408 39 61 409 34 61 410 34 61 411 39 61 412 38 61 413 41 7 414 39 7 415 33 7 416 33 7 417 39 7 418 35 7 419</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="head" name="head" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#head-mesh" name="head">
          <bind_material>
            <technique_common>
              <instance_material symbol="plastic-material" target="#plastic-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>
<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 3.3.1 commit date:2022-10-04, commit time:18:35, hash:b292cfe5a936</authoring_tool>
    </contributor>
    <created>2022-12-09T11:55:21</created>
    <modified>2022-12-09T11:55:21</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">39.59775</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <dof_distance sid="dof_distance" type="float">10</dof_distance>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="Light-light" name="Light">
      <technique_common>
        <directional>
          <color sid="color">1000 1000 1000</color>
        </directional>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">1</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">1</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1000</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">0.04999995</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_effects>
    <effect id="wood-effect">
      <profile_COMMON>
        <newparam sid="Wood066_4K_Color_png-surface">
          <surface type="2D">
            <init_from>Wood066_4K_Color_png</init_from>
          </surface>
        </newparam>
        <newparam sid="Wood066_4K_Color_png-sampler">
          <sampler2D>
            <source>Wood066_4K_Color_png-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="Wood066_4K_Color_png-sampler" texcoord="UVMap"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.55</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="Wood066_4K_Color_png" name="Wood066_4K_Color_png">
      <init_from>Wood066_4K_Color.png</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="wood-material" name="wood">
      <instance_effect url="#wood-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="bench-mesh" name="bench">
      <mesh>
        <source id="bench-mesh-positions">
          <float_array id="bench-mesh-positions-array" count="24">0.8 0.03999996 -0.15 0.8 0.03999996 0.15 0.8 0 -0.15 0.8 0 0.15 -0.8 0.03999996 -0.15 -0.8 0 -0.15 -0.8 0.03999996 0.15 -0.8 0 0.15</float_array>
          <technique_common>
            <accessor source="#bench-mesh-positions-array" count="8" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="bench-mesh-normals">
          <float_array id="bench-mesh-normals-array" count="18">1 0 0 0 0 -1 -1 0 0 0 0 1 0 1 0 0 -1 0</float_array>
          <technique_common>
            <accessor source="#bench-mesh-normals-array" count="6" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="bench-mesh-map-0">
          <float_array id="bench-mesh-map-0-array" count="72">0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1 0 0 1 0 1 1</float_array>
          <technique_common>
            <accessor source="#bench-mesh-map-0-array" count="36" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="bench-mesh-vertices">
          <input semantic="POSITION" source="#bench-mesh-positions"/>
        </vertices>
        <triangles material="wood-material" count="12">
          <input semantic="VERTEX" source="#bench-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#bench-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#bench-mesh-map-0" offset="2" set="0"/>
          <p>0 0 0 1 0 1 2 0 2 2 0 3 1 0 4 3 0 5 4 1 6 0 1 7 5 1 8 5 1 9 0 1 10 2 1 11 6 2 12 4 2 13 7 2 14 7 2 15 4 2 16 5 2 17 1 3 18 6 3 19 3 3 20 3 3 21 6 3 22 7 3 23 4 4 24 6 4 25 0 4 26 0 4 27 6 4 28 1 4 29 7 5 30 5 5 31 3 5 32 3 5 33 5 5 34 2 5 35</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="bench" name="bench" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#bench-mesh" name="bench">
          <bind_material>
            <technique_common>
              <instance_material symbol="wood-material" target="#wood-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.358891 0.7276763 0.3054208 -0.6141704 -6.925791 0 0.8953956 0.4452714 4.958309 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Light" name="Light" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Light-light"/>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>
<?xml version="1.0" ?>
<sdf version="1.4">
  <model name="table">
    <static>true</static>
    <link name="link">
      <collision name="surface">
        <pose>0 0 1.0 0 0 0</pose>
        <geometry>
          <box>
            <size>1.5 0.8 0.03</size>
          </box>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>0.6</mu>
              <mu2>0.6</mu2>
            </ode>
          </friction>
        </surface>
      </collision>
      <visual name="visual1">
        <pose>0 0 1.0 0 0 0</pose>
        <geometry>
          <box>
            <size>1.5 0.8 0.03</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
        </material>
      </visual>
      <collision name="front_left_leg">
        <pose>0.68 0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="front_left_leg">
        <pose>0.68 0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      <collision name="front_right_leg">
        <pose>0.68 -0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="front_right_leg">
        <pose>0.68 -0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      <collision name="back_right_leg">
        <pose>-0.68 -0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="back_right_leg">
        <pose>-0.68 -0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
      <collision name="back_left_leg">
        <pose>-0.68 0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="back_left_leg">
        <pose>-0.68 0.38 0.5 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.02</radius>
            <length>1.0</length>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>
    </link>
  </model>
</sdf>

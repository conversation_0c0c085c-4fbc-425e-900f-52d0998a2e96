<?xml version="1.0" ?>
<%
  # Wood block with dimensions 10 x 10 x 10 cm
  # SI units (length in meters)

  # Geometry
  dx = 0.10
  dy = 0.10
  dz = 0.10

  # Inertia
  mass = 0.5167
  ixx  = mass/12.0 * (dy**2 + dz**2)
  iyy  = mass/12.0 * (dz**2 + dx**2)
  izz  = mass/12.0 * (dx**2 + dy**2)
%>
<sdf version="1.5">
  <model name="wood_cube_10cm">
    <link name="link">
      <pose>0 0 <%= dz/2 %> 0 0 0</pose>
      <inertial>
        <mass><%= mass %></mass>
        <inertia>
          <ixx><%= ixx %></ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy><%= iyy %></iyy>
          <iyz>0</iyz>
          <izz><%= izz %></izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <box>
            <size><%= dx %> <%= dy %> <%= dz %></size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <max_vel>0.1</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <box>
            <size><%= dx %> <%= dy %> <%= dz %></size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Wood</name>
          </script>
        </material>
      </visual>

    </link>
  </model>
</sdf>

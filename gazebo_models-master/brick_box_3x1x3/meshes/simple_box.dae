<?xml version="1.0" encoding="utf-8"?>
<COLLADA version="1.4.0" xmlns="http://www.collada.org/2005/11/COLLADASchema">
	<asset>
		<contributor>
			<author>Illusoft Collada 1.4.0 plugin for Blender - http://colladablender.illusoft.com</author>
			<authoring_tool>Blender v:249 - Illusoft Collada Exporter v:0.3.162</authoring_tool>
			<comments></comments>
			<copyright></copyright>
			<source_data>file:///u/nkoenig/plate_2.blend</source_data>
		</contributor>
		<created>2011-03-08T12:41:39.033566</created>
		<modified>2011-03-08T12:41:39.033583</modified>
		<unit meter="1.00" name="meter"/>
		<up_axis>Z_UP</up_axis>
	</asset>
	<library_effects>
		<effect id="plate_2_tga-fx" name="plate_2_tga-fx">
			<profile_COMMON>
				<newparam sid="plate_2_tga-surface">
					<surface type="2D">
						<init_from>plate_2_tga-img</init_from>
						<format>A8R8G8B8</format>
					</surface>
				</newparam>
				<newparam sid="plate_2_tga-sampler">
					<sampler2D>
						<source>plate_2_tga-surface</source>
						<minfilter>LINEAR_MIPMAP_LINEAR</minfilter>
						<magfilter>LINEAR</magfilter>
					</sampler2D>
				</newparam>
				<technique sid="blender">
					<phong>
						<emission>
							<color>0.30000 0.30000 0.30000 1</color>
						</emission>
						<ambient>
							<color>1.00000 1.00000 1.00000 1</color>
						</ambient>
						<diffuse>
							<texture texcoord="CHANNEL1" texture="plate_2_tga-sampler"/>
						</diffuse>
						<specular>
							<color>0.50000 0.50000 0.50000 1</color>
						</specular>
						<shininess>
							<float>12.5</float>
						</shininess>
						<reflective>
							<color>1.00000 1.00000 1.00000 1</color>
						</reflective>
						<reflectivity>
							<float>0.0</float>
						</reflectivity>
						<transparent>
							<color>1 1 1 1</color>
						</transparent>
						<transparency>
							<float>0.0</float>
						</transparency>
					</phong>
				</technique>
			</profile_COMMON>
		</effect>
	</library_effects>
	<library_images>
		<image id="plate_2_tga-img" name="plate_2_tga-img">
			<init_from>../materials/textures/simple_box.png</init_from>
		</image>
	</library_images>
	<library_materials>
		<material id="plate_2_tga" name="plate_2_tga">
			<instance_effect url="#plate_2_tga-fx"/>
		</material>
	</library_materials>
	<library_geometries>
		<geometry id="Mesh-Geometry" name="Mesh-Geometry">
			<mesh>
				<source id="Mesh-Geometry-Position">
					<float_array count="24" id="Mesh-Geometry-Position-array">0.5000 0.5000 -0.5000 0.5000 -0.5000 -0.5000 -0.5000 -0.5000 -0.5000 -0.5000 0.5000 -0.5000 0.5000 0.5000 0.5000 0.5000 -0.5000 0.5000 -0.5000 -0.5000 0.5000 -0.5000 0.5000 0.5000</float_array>
					<technique_common>
						<accessor count="8" source="#Mesh-Geometry-Position-array" stride="3">
							<param type="float" name="X"></param>
							<param type="float" name="Y"></param>
							<param type="float" name="Z"></param>
						</accessor>
					</technique_common>
				</source>
				<source id="Mesh-Geometry-Normals">
					<float_array count="18" id="Mesh-Geometry-Normals-array">0.00000 0.00000 -1.00000 0.00000 0.00000 1.00000 1.00000 -0.00000 0.00000 -0.00000 -1.00000 -0.00000 -1.00000 0.00000 -0.00000 0.00000 1.00000 0.00000</float_array>
					<technique_common>
						<accessor count="6" source="#Mesh-Geometry-Normals-array" stride="3">
							<param type="float" name="X"></param>
							<param type="float" name="Y"></param>
							<param type="float" name="Z"></param>
						</accessor>
					</technique_common>
				</source>
				<source id="Mesh-Geometry-UV">
					<float_array count="72" id="Mesh-Geometry-UV-array">0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 0.0 0.0 1.0 1.0 1.0 0.0 0.0 1.0 1.0 1.0 0.0</float_array>
					<technique_common>
						<accessor count="36" source="#Mesh-Geometry-UV-array" stride="2">
							<param type="float" name="S"></param>
							<param type="float" name="T"></param>
						</accessor>
					</technique_common>
				</source>
				<vertices id="Mesh-Geometry-Vertex">
					<input semantic="POSITION" source="#Mesh-Geometry-Position"/>
				</vertices>
				<triangles count="12" material="plate_2_tga">
					<input offset="0" semantic="VERTEX" source="#Mesh-Geometry-Vertex"/>
					<input offset="1" semantic="NORMAL" source="#Mesh-Geometry-Normals"/>
					<input offset="2" semantic="TEXCOORD" source="#Mesh-Geometry-UV"/>
					<p>0 0 0 1 0 1 2 0 2 2 0 4 3 0 5 0 0 3 4 1 0 7 1 1 6 1 2 6 1 4 5 1 5 4 1 3 0 2 0 4 2 1 5 2 2 5 2 4 1 2 5 0 2 3 1 3 0 5 3 1 6 3 2 6 3 4 2 3 5 1 3 3 2 4 0 6 4 1 7 4 2 7 4 4 3 4 5 2 4 3 4 5 0 0 5 1 3 5 2 3 5 4 7 5 5 4 5 3</p>
				</triangles>
			</mesh>
		</geometry>
	</library_geometries>
	<library_visual_scenes>
		<visual_scene id="Scene001" name="Scene001">
			<node layer="L1" id="Mesh" name="Mesh">
				<translate sid="translate">0.00000 0.00000 0.00000</translate>
				<rotate sid="rotateZ">0 0 1 -0.00001</rotate>
				<rotate sid="rotateY">0 1 0 -0.00000</rotate>
				<rotate sid="rotateX">1 0 0 0.00000</rotate>
				<scale sid="scale">1.00000 1.00000 1.00000</scale>
				<instance_geometry url="#Mesh-Geometry">
					<bind_material>
						<technique_common>
							<instance_material symbol="plate_2_tga" target="#plate_2_tga">
								<bind_vertex_input input_semantic="TEXCOORD" input_set="1" semantic="CHANNEL1"/>
							</instance_material>
						</technique_common>
					</bind_material>
				</instance_geometry>
			</node>
		</visual_scene>
	</library_visual_scenes>
	<library_physics_materials>
		<physics_material id="Mesh-PhysicsMaterial" name="Mesh-PhysicsMaterial">
			<technique_common>
				<dynamic_friction>0.5</dynamic_friction>
				<restitution>0.0</restitution>
				<static_friction>0.5</static_friction>
			</technique_common>
		</physics_material>
	</library_physics_materials>
	<library_physics_models>
		<physics_model id="Scene001-PhysicsModel" name="Scene001-PhysicsModel">
			<rigid_body name="Mesh-RigidBody" sid="Mesh-RigidBody">
				<technique_common>
					<dynamic>false</dynamic>
					<mass>0</mass>
					<instance_physics_material url="#Mesh-PhysicsMaterial"/>
					<shape>
						<instance_geometry url="#Mesh-Geometry"/>
					</shape>
				</technique_common>
			</rigid_body>
		</physics_model>
	</library_physics_models>
	<library_physics_scenes>
		<physics_scene id="Scene001-Physics" name="Scene001-Physics">
			<instance_physics_model url="#Scene001-PhysicsModel">
				<instance_rigid_body body="Mesh-RigidBody" target="#Mesh"/>
			</instance_physics_model>
		</physics_scene>
	</library_physics_scenes>
	<scene>
		<instance_physics_scene url="#Scene001-Physics"/>
		<instance_visual_scene url="#Scene001"/>
	</scene>
</COLLADA>

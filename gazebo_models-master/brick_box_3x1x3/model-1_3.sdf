<?xml version="1.0" ?>
<sdf version="1.3">
    <model name="brick_box_3x1x3">
        <static>true</static>
        <link name="chassis">
            <pose>0 0 1.5 0 0 0</pose>
            <collision name="collision">
                <geometry>
                    <box>
                        <size>3.0 1.0 3.0</size>
                    </box>
                </geometry>
            </collision>
            <visual name="visual">
                <geometry>
                    <mesh>
                        <uri>model://brick_box_3x1x3/meshes/simple_box.dae</uri>
                        <scale>3.0 1.0 3.0</scale>
                    </mesh>
                </geometry>
                <material>
                  <script>
                    <uri>model://brick_box_3x1x3/materials/scripts</uri>
            <uri>model://brick_box_3x1x3/materials/textures</uri>
                    <name>BrickBox/Diffuse</name>
                  </script>
                </material>
            </visual>
        </link>
    </model>
</sdf>

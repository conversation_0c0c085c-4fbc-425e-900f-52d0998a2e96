<?xml version="1.0" ?>

<sdf version="1.5">
  <model name="ball_bearing">
    <link name="link">
      <pose>0 0 0.003 0 0 0</pose>
      <inertial>
        <mass>0.0011</mass>
        <inertia>
          <ixx>3.96e-09</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>3.96e-09</iyy>
          <iyz>0</iyz>
          <izz>3.96e-09</izz>
        </inertia>
      </inertial>

      <collision name="collision">
        <geometry>
          <sphere>
            <radius>0.003</radius>
          </sphere>
        </geometry>
        <surface>
          <contact>
            <!-- stainless steel 18-8 material properties -->
            <poissons_ratio>0.305</poissons_ratio>
            <elastic_modulus>2.0e+11</elastic_modulus>
            <ode>
              <kp>100000</kp>
              <kd>100</kd>
              <max_vel>100.0</max_vel>
              <min_depth>0.001</min_depth>
            </ode>
          </contact>
          <friction>
            <torsional>
              <coefficient>1.0</coefficient>
              <use_patch_radius>0</use_patch_radius>
              <surface_radius>0.01</surface_radius>
            </torsional>
          </friction>
        </surface>
      </collision>

      <visual name="visual">
        <geometry>
          <sphere>
            <radius>0.003</radius>
          </sphere>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/Grey</name>
          </script>
        </material>
      </visual>

      <!-- approximate rolling friction -->
      <velocity_decay>
        <linear>0.00</linear>
        <angular>0.005</angular>
      </velocity_decay>
    </link>
  </model>
</sdf>

<?xml version="1.0"?>
<package format="2">
  <name>hospital_gazebo</name>
  <version>1.0.0</version>
  <description>Hospital simulation environment for Gazebo with realistic medical facility layout and equipment</description>

  <maintainer email="<EMAIL>">Hospital Simulation Team</maintainer>
  <license>MIT</license>

  <url type="website">http://wiki.ros.org/hospital_gazebo</url>
  <url type="bugtracker">https://github.com/hospital-simulation/hospital_gazebo/issues</url>
  <url type="repository">https://github.com/hospital-simulation/hospital_gazebo</url>

  <author email="<EMAIL>">Hospital Simulation Team</author>

  <!-- Build tool dependencies -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Build dependencies -->
  <build_depend>std_msgs</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>gazebo_ros</build_depend>
  <build_depend>gazebo_msgs</build_depend>
  <build_depend>gazebo_plugins</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>xacro</build_depend>

  <!-- Runtime dependencies -->
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>gazebo_msgs</exec_depend>
  <exec_depend>gazebo_plugins</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>xacro</exec_depend>

  <export>
    <gazebo_ros gazebo_model_path="${prefix}/models"/>
    <gazebo_ros gazebo_media_path="${prefix}/media"/>
  </export>
</package>

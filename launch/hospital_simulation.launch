<?xml version="1.0"?>
<launch>
  <!-- Hospital Simulation Launch File for ROS1 -->
  
  <!-- Arguments -->
  <arg name="world_name" default="$(find hospital_gazebo)/worlds/hospital_simulation.world"/>
  <arg name="paused" default="false"/>
  <arg name="use_sim_time" default="true"/>
  <arg name="gui" default="true"/>
  <arg name="headless" default="false"/>
  <arg name="debug" default="false"/>
  <arg name="verbose" default="false"/>

  <!-- Set Gazebo model path to include our models -->
  <env name="GAZEBO_MODEL_PATH" value="$(find hospital_gazebo)/../../../gazebo_models-master:$(optenv GAZEBO_MODEL_PATH)"/>

  <!-- Launch Gazebo with hospital world -->
  <include file="$(find gazebo_ros)/launch/empty_world.launch">
    <arg name="world_name" value="$(arg world_name)"/>
    <arg name="paused" value="$(arg paused)"/>
    <arg name="use_sim_time" value="$(arg use_sim_time)"/>
    <arg name="gui" value="$(arg gui)"/>
    <arg name="headless" value="$(arg headless)"/>
    <arg name="debug" value="$(arg debug)"/>
    <arg name="verbose" value="$(arg verbose)"/>
  </include>

  <!-- Hospital Information Display -->
  <node name="hospital_info" pkg="hospital_gazebo" type="hospital_info.py" output="screen"/>

</launch>
